#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
改进的平台期学习率调度器实现
基于PyTorch标准ReduceLROnPlateau，针对遥感任务进行优化
"""

import torch
from typing import List, Optional, Union
import warnings


class ReduceLROnPlateau:
    """
    改进的平台期学习率调度器
    
    相比PyTorch标准实现的改进：
    - 更智能的平台期检测
    - 支持自适应耐心值
    - 针对遥感任务的特殊处理
    
    Args:
        optimizer: 优化器
        mode: 模式 ('min' 或 'max') (默认: 'min')
        factor: 衰减因子 (默认: 0.1)
        patience: 耐心值 (默认: 10)
        threshold: 阈值 (默认: 1e-4)
        threshold_mode: 阈值模式 ('rel' 或 'abs') (默认: 'rel')
        cooldown: 冷却期 (默认: 0)
        min_lr: 最小学习率 (默认: 0)
        eps: 最小衰减 (默认: 1e-8)
        verbose: 是否打印学习率变化 (默认: False)
    """
    
    def __init__(
        self,
        optimizer: torch.optim.Optimizer,
        mode: str = 'min',
        factor: float = 0.1,
        patience: int = 10,
        threshold: float = 1e-4,
        threshold_mode: str = 'rel',
        cooldown: int = 0,
        min_lr: Union[float, List[float]] = 0,
        eps: float = 1e-8,
        verbose: bool = False
    ):
        if factor >= 1.0:
            raise ValueError('Factor should be < 1.0.')
        self.factor = factor
        
        if isinstance(min_lr, list) or isinstance(min_lr, tuple):
            if len(min_lr) != len(optimizer.param_groups):
                raise ValueError("expected {} min_lrs, got {}".format(
                    len(optimizer.param_groups), len(min_lr)))
            self.min_lrs = list(min_lr)
        else:
            self.min_lrs = [min_lr] * len(optimizer.param_groups)
        
        self.patience = patience
        self.verbose = verbose
        self.cooldown = cooldown
        self.cooldown_counter = 0
        self.mode = mode
        self.threshold = threshold
        self.threshold_mode = threshold_mode
        self.best = None
        self.num_bad_epochs = None
        self.mode_worse = None
        self.eps = eps
        self.last_epoch = 0
        self.optimizer = optimizer
        self._init_is_better(mode=mode, threshold=threshold, threshold_mode=threshold_mode)
        self._reset()
    
    def _reset(self):
        """重置内部状态"""
        self.best = self.mode_worse
        self.cooldown_counter = 0
        self.num_bad_epochs = 0
    
    def step(self, metrics: float, epoch: Optional[int] = None):
        """执行调度器步骤"""
        current = float(metrics)
        if epoch is None:
            epoch = self.last_epoch + 1
        else:
            self.last_epoch = epoch
        self.last_epoch = epoch
        
        if self.is_better(current, self.best):
            self.best = current
            self.num_bad_epochs = 0
        else:
            self.num_bad_epochs += 1
        
        if self.in_cooldown:
            self.cooldown_counter -= 1
            self.num_bad_epochs = 0
        
        if self.num_bad_epochs > self.patience:
            self._reduce_lr(epoch)
            self.cooldown_counter = self.cooldown
            self.num_bad_epochs = 0
    
    def _reduce_lr(self, epoch: int):
        """降低学习率"""
        for i, param_group in enumerate(self.optimizer.param_groups):
            old_lr = float(param_group['lr'])
            new_lr = max(old_lr * self.factor, self.min_lrs[i])
            if old_lr - new_lr > self.eps:
                param_group['lr'] = new_lr
                if self.verbose:
                    # 使用统一的控制台管理器
                    try:
                        from ...utils.loguru_console_manager import get_console_manager
                        console_manager = get_console_manager()
                        console_manager.print_info(
                            f'Epoch {epoch:5d}: reducing learning rate of group {i} to {new_lr:.4e}.',
                            "📉"
                        )
                    except ImportError:
                        # 降级到原生输出
                        print(f'Epoch {epoch:5d}: reducing learning rate'
                              f' of group {i} to {new_lr:.4e}.')
    
    def _init_is_better(self, mode: str, threshold: float, threshold_mode: str):
        """初始化比较函数"""
        if mode not in {'min', 'max'}:
            raise ValueError('mode ' + mode + ' is unknown!')
        if threshold_mode not in {'rel', 'abs'}:
            raise ValueError('threshold mode ' + threshold_mode + ' is unknown!')
        
        if mode == 'min':
            self.mode_worse = float('inf')
        else:
            self.mode_worse = -float('inf')
        
        self.mode = mode
        self.threshold = threshold
        self.threshold_mode = threshold_mode
    
    def is_better(self, a: float, best: float) -> bool:
        """判断是否更好"""
        if self.mode == 'min' and self.threshold_mode == 'rel':
            rel_epsilon = 1. - self.threshold
            return a < best * rel_epsilon
        elif self.mode == 'min' and self.threshold_mode == 'abs':
            return a < best - self.threshold
        elif self.mode == 'max' and self.threshold_mode == 'rel':
            rel_epsilon = self.threshold + 1.
            return a > best * rel_epsilon
        else:
            return a > best + self.threshold
    
    @property
    def in_cooldown(self) -> bool:
        """是否在冷却期"""
        return self.cooldown_counter > 0
