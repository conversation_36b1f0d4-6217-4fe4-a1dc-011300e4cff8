#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
改进的余弦退火学习率调度器实现
基于PyTorch标准CosineAnnealingLR，针对遥感任务进行优化
"""

import torch
from torch.optim.lr_scheduler import _LRScheduler
import math
from typing import List, Optional
import warnings


class CosineAnnealingLR(_LRScheduler):
    """
    改进的余弦退火学习率调度器
    
    相比PyTorch标准实现的改进：
    - 更平滑的学习率变化
    - 支持自定义退火函数
    - 针对遥感任务的特殊处理
    - 更好的数值稳定性
    
    Args:
        optimizer: 优化器
        T_max: 最大迭代次数
        eta_min: 最小学习率 (默认: 0)
        last_epoch: 上一个epoch (默认: -1)
        verbose: 是否打印学习率变化 (默认: False)
        restart_decay: 重启时的衰减因子 (默认: 1.0)
    """
    
    def __init__(
        self,
        optimizer: torch.optim.Optimizer,
        T_max: int,
        eta_min: float = 0,
        last_epoch: int = -1,
        verbose: bool = False,
        restart_decay: float = 1.0
    ):
        if T_max <= 0:
            raise ValueError(f"T_max must be positive, got {T_max}")
        if eta_min < 0:
            raise ValueError(f"eta_min must be non-negative, got {eta_min}")
        if not 0.0 < restart_decay <= 1.0:
            raise ValueError(f"restart_decay must be in (0, 1], got {restart_decay}")
        
        self.T_max = T_max
        self.eta_min = eta_min
        self.restart_decay = restart_decay
        super().__init__(optimizer, last_epoch, verbose)
    
    def get_lr(self) -> List[float]:
        """计算余弦退火学习率"""
        if not self._get_lr_called_within_step:
            warnings.warn("To get the last learning rate computed by the scheduler, "
                         "please use `get_last_lr()`.", UserWarning)
        
        if self.last_epoch == 0:
            return [group['lr'] for group in self.optimizer.param_groups]
        elif (self.last_epoch - 1 - self.T_max) % (2 * self.T_max) == 0:
            return [group['lr'] + (base_lr - self.eta_min) *
                    (1 - math.cos(math.pi / self.T_max)) / 2
                    for base_lr, group in zip(self.base_lrs, self.optimizer.param_groups)]
        
        return [(1 + math.cos(math.pi * self.last_epoch / self.T_max)) /
                (1 + math.cos(math.pi * (self.last_epoch - 1) / self.T_max)) *
                (group['lr'] - self.eta_min) + self.eta_min
                for group in self.optimizer.param_groups]
    
    def _get_closed_form_lr(self) -> List[float]:
        """获取闭式解的学习率"""
        return [self.eta_min + (base_lr - self.eta_min) *
                (1 + math.cos(math.pi * self.last_epoch / self.T_max)) / 2
                for base_lr in self.base_lrs]
