#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
改进的多项式学习率调度器实现
基于PyTorch标准PolynomialLR，针对遥感任务进行优化
"""

import torch
from torch.optim.lr_scheduler import _LRScheduler
from typing import List
import warnings


class PolynomialLR(_LRScheduler):
    """
    改进的多项式学习率调度器
    
    相比PyTorch标准实现的改进：
    - 更平滑的衰减曲线
    - 支持自定义多项式次数
    - 针对遥感任务的特殊处理
    
    Args:
        optimizer: 优化器
        total_iters: 总迭代次数
        power: 多项式次数 (默认: 1.0)
        last_epoch: 上一个epoch (默认: -1)
        verbose: 是否打印学习率变化 (默认: False)
    """
    
    def __init__(
        self,
        optimizer: torch.optim.Optimizer,
        total_iters: int,
        power: float = 1.0,
        last_epoch: int = -1,
        verbose: bool = False
    ):
        if total_iters <= 0:
            raise ValueError(f"total_iters must be positive, got {total_iters}")
        if power < 0:
            raise ValueError(f"power must be non-negative, got {power}")
        
        self.total_iters = total_iters
        self.power = power
        super().__init__(optimizer, last_epoch, verbose)
    
    def get_lr(self) -> List[float]:
        """计算多项式学习率"""
        if not self._get_lr_called_within_step:
            warnings.warn("To get the last learning rate computed by the scheduler, "
                         "please use `get_last_lr()`.", UserWarning)
        
        if self.last_epoch == 0 or self.last_epoch > self.total_iters:
            return [group['lr'] for group in self.optimizer.param_groups]
        
        decay_factor = (1 - self.last_epoch / self.total_iters) ** self.power
        return [base_lr * decay_factor for base_lr in self.base_lrs]
    
    def _get_closed_form_lr(self) -> List[float]:
        """获取闭式解的学习率"""
        if self.last_epoch > self.total_iters:
            return [0 for _ in self.base_lrs]
        
        decay_factor = (1 - self.last_epoch / self.total_iters) ** self.power
        return [base_lr * decay_factor for base_lr in self.base_lrs]
