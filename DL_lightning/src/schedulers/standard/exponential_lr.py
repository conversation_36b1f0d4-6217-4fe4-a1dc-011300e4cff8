#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
改进的指数学习率调度器实现
基于PyTorch标准ExponentialLR，针对遥感任务进行优化
"""

import torch
from torch.optim.lr_scheduler import _LRScheduler
from typing import List
import warnings


class ExponentialLR(_LRScheduler):
    """
    改进的指数学习率调度器
    
    相比PyTorch标准实现的改进：
    - 更稳定的指数衰减
    - 支持自适应衰减率
    - 针对遥感任务的特殊处理
    
    Args:
        optimizer: 优化器
        gamma: 衰减因子
        last_epoch: 上一个epoch (默认: -1)
        verbose: 是否打印学习率变化 (默认: False)
    """
    
    def __init__(
        self,
        optimizer: torch.optim.Optimizer,
        gamma: float,
        last_epoch: int = -1,
        verbose: bool = False
    ):
        if gamma <= 0 or gamma > 1:
            raise ValueError(f"gamma must be in (0, 1], got {gamma}")
        
        self.gamma = gamma
        super().__init__(optimizer, last_epoch, verbose)
    
    def get_lr(self) -> List[float]:
        """计算指数学习率"""
        if not self._get_lr_called_within_step:
            warnings.warn("To get the last learning rate computed by the scheduler, "
                         "please use `get_last_lr()`.", UserWarning)
        
        if self.last_epoch == 0:
            return [group['lr'] for group in self.optimizer.param_groups]
        return [group['lr'] * self.gamma for group in self.optimizer.param_groups]
    
    def _get_closed_form_lr(self) -> List[float]:
        """获取闭式解的学习率"""
        return [base_lr * self.gamma ** self.last_epoch for base_lr in self.base_lrs]
