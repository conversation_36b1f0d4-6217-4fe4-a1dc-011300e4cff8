# 自定义学习率调度器目录

## 📋 用途说明

此目录用于存放自定义的学习率调度器实现。当前项目默认使用 PyTorch 原生调度器，通过配置文件进行管理。

## 🔧 当前实现方式

### 配置文件位置
```
configs/scheduler/
├── cosine.yaml         # 余弦退火调度器配置
└── poly.yaml           # 多项式调度器配置
```

### 使用方式
```bash
# 使用余弦退火调度器
python scripts/train.py scheduler=cosine

# 使用多项式调度器
python scripts/train.py scheduler=poly

# 覆盖调度器参数
python scripts/train.py scheduler=cosine scheduler.T_max=200
```

### 实例化机制
调度器通过 `SegmentationModule.configure_optimizers()` 方法中的 `hydra.utils.instantiate()` 动态创建：

```python
scheduler = hydra.utils.instantiate(
    self.scheduler_cfg,
    optimizer=optimizer,
    _partial_=False
)
```

## 🚀 何时需要自定义调度器

### 适用场景
- **复杂学习率策略**: 多阶段、自适应的学习率调整
- **遥感数据特化**: 针对遥感图像分割的特殊调度策略
- **多尺度训练**: 配合多尺度训练的动态学习率
- **实验性调度**: 研究新的学习率调度算法

### 不需要自定义的情况
- 使用标准调度器（StepLR、CosineAnnealingLR、ExponentialLR等）
- 只需要调整调度器参数
- 使用 PyTorch 原生支持的调度器

## 💡 实现示例

### 1. 创建自定义调度器

```python
# src/schedulers/adaptive_cosine.py
import torch
from torch.optim.lr_scheduler import _LRScheduler
import math

class AdaptiveCosineAnnealingLR(_LRScheduler):
    """
    自适应余弦退火调度器
    根据验证损失动态调整学习率衰减速度
    """
    
    def __init__(self, optimizer, T_max, eta_min=0, 
                 adaptation_factor=0.1, last_epoch=-1):
        self.T_max = T_max
        self.eta_min = eta_min
        self.adaptation_factor = adaptation_factor
        self.val_loss_history = []
        super().__init__(optimizer, last_epoch)
    
    def get_lr(self):
        # 自适应调整 T_max
        if len(self.val_loss_history) > 1:
            loss_trend = self.val_loss_history[-1] - self.val_loss_history[-2]
            if loss_trend > 0:  # 损失上升，加快衰减
                effective_T_max = self.T_max * (1 - self.adaptation_factor)
            else:  # 损失下降，减慢衰减
                effective_T_max = self.T_max * (1 + self.adaptation_factor)
        else:
            effective_T_max = self.T_max
        
        return [self.eta_min + (base_lr - self.eta_min) *
                (1 + math.cos(math.pi * self.last_epoch / effective_T_max)) / 2
                for base_lr in self.base_lrs]
    
    def update_val_loss(self, val_loss):
        """更新验证损失历史"""
        self.val_loss_history.append(val_loss)
        # 只保留最近的历史
        if len(self.val_loss_history) > 10:
            self.val_loss_history.pop(0)
```

### 2. 创建配置文件

```yaml
# configs/scheduler/adaptive_cosine.yaml
_target_: src.schedulers.adaptive_cosine.AdaptiveCosineAnnealingLR
T_max: 100
eta_min: 1e-6
adaptation_factor: 0.1
```

### 3. 注册到模块

```python
# src/schedulers/__init__.py
from .adaptive_cosine import AdaptiveCosineAnnealingLR

__all__ = ['AdaptiveCosineAnnealingLR']
```

### 4. 在训练中使用

```python
# 在 SegmentationModule 中集成自定义调度器
def on_validation_epoch_end(self):
    # 获取验证损失
    val_loss = self.trainer.callback_metrics.get("val/loss", 0.0)
    
    # 如果使用自适应调度器，更新损失历史
    scheduler = self.lr_schedulers()
    if hasattr(scheduler, 'update_val_loss'):
        scheduler.update_val_loss(val_loss.item())
```

## 📚 常见调度器模式

### 1. 多阶段调度器
```python
class MultiStageScheduler(_LRScheduler):
    """多阶段学习率调度器"""
    def __init__(self, optimizer, milestones, gamma=0.1):
        self.milestones = milestones
        self.gamma = gamma
        super().__init__(optimizer)
```

### 2. 预热调度器
```python
class WarmupScheduler(_LRScheduler):
    """学习率预热调度器"""
    def __init__(self, optimizer, warmup_epochs, base_scheduler):
        self.warmup_epochs = warmup_epochs
        self.base_scheduler = base_scheduler
        super().__init__(optimizer)
```

### 3. 循环学习率
```python
class CyclicLR(_LRScheduler):
    """循环学习率调度器"""
    def __init__(self, optimizer, base_lr, max_lr, step_size_up):
        self.base_lr = base_lr
        self.max_lr = max_lr
        self.step_size_up = step_size_up
        super().__init__(optimizer)
```

## 🎯 遥感分割特化调度策略

### 1. 多尺度训练调度
```python
class MultiScaleScheduler(_LRScheduler):
    """配合多尺度训练的学习率调度"""
    def __init__(self, optimizer, scale_schedule):
        self.scale_schedule = scale_schedule
        super().__init__(optimizer)
    
    def get_lr(self):
        # 根据当前训练尺度调整学习率
        current_scale = self.get_current_scale()
        scale_factor = self.scale_schedule.get(current_scale, 1.0)
        return [base_lr * scale_factor for base_lr in self.base_lrs]
```

### 2. 类别平衡调度
```python
class ClassBalancedScheduler(_LRScheduler):
    """根据类别分布调整学习率"""
    def __init__(self, optimizer, class_weights):
        self.class_weights = class_weights
        super().__init__(optimizer)
```

## 📚 参考资源

### PyTorch 调度器文档
- [torch.optim.lr_scheduler](https://pytorch.org/docs/stable/optim.html#how-to-adjust-learning-rate)
- [自定义调度器教程](https://pytorch.org/tutorials/beginner/saving_loading_models.html)

### 学习率调度策略论文
- Cosine Annealing: "SGDR: Stochastic Gradient Descent with Warm Restarts"
- Cyclic Learning Rates: "Cyclical Learning Rates for Training Neural Networks"
- Warm Restarts: "SGDR: Stochastic Gradient Descent with Warm Restarts"

### Lightning 集成
- [LearningRateMonitor](https://lightning.ai/docs/pytorch/stable/api/lightning.pytorch.callbacks.LearningRateMonitor.html)
- [configure_optimizers](https://lightning.ai/docs/pytorch/stable/common/lightning_module.html#configure-optimizers)

## 🎯 最佳实践

1. **继承基类**: 继承 `torch.optim.lr_scheduler._LRScheduler` 确保兼容性
2. **状态管理**: 正确处理调度器的状态保存和恢复
3. **参数验证**: 验证调度器参数的有效性
4. **监控集成**: 与 Lightning 的 LearningRateMonitor 兼容
5. **文档完整**: 提供详细的使用说明和参数解释

## 📞 技术支持

如需实现自定义调度器，请参考：
- 项目技术文档: `analysis_workspace/OPTIMIZER_SCHEDULER_ANALYSIS.md`
- 示例实现: `src/schedulers/examples/` (如果存在)
- Lightning 文档: [Learning Rate Scheduling](https://lightning.ai/docs/pytorch/stable/common/optimization.html)
