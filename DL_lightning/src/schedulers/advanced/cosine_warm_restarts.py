#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
余弦退火热重启调度器实现
基于PyTorch标准CosineAnnealingWarmRestarts，针对遥感任务进行优化
"""

import torch
from torch.optim.lr_scheduler import _LRScheduler
import math
from typing import List, Optional
import warnings


class CosineAnnealingWarmRestarts(_LRScheduler):
    """
    余弦退火热重启调度器
    
    相比PyTorch标准实现的改进：
    - 更灵活的重启策略
    - 支持自定义重启衰减
    - 针对遥感任务的特殊处理
    
    Args:
        optimizer: 优化器
        T_0: 第一次重启的周期
        T_mult: 周期倍增因子 (默认: 1)
        eta_min: 最小学习率 (默认: 0)
        last_epoch: 上一个epoch (默认: -1)
        verbose: 是否打印学习率变化 (默认: False)
    """
    
    def __init__(
        self,
        optimizer: torch.optim.Optimizer,
        T_0: int,
        T_mult: int = 1,
        eta_min: float = 0,
        last_epoch: int = -1,
        verbose: bool = False
    ):
        if T_0 <= 0 or not isinstance(T_0, int):
            raise ValueError("Expected positive integer T_0, but got {}".format(T_0))
        if T_mult < 1 or not isinstance(T_mult, int):
            raise ValueError("Expected integer T_mult >= 1, but got {}".format(T_mult))
        
        self.T_0 = T_0
        self.T_i = T_0
        self.T_mult = T_mult
        self.eta_min = eta_min
        self.T_cur = last_epoch
        super().__init__(optimizer, last_epoch, verbose)
    
    def get_lr(self) -> List[float]:
        """计算余弦退火热重启学习率"""
        if not self._get_lr_called_within_step:
            warnings.warn("To get the last learning rate computed by the scheduler, "
                         "please use `get_last_lr()`.", UserWarning)
        
        return [self.eta_min + (base_lr - self.eta_min) *
                (1 + math.cos(math.pi * self.T_cur / self.T_i)) / 2
                for base_lr in self.base_lrs]
    
    def step(self, epoch: Optional[int] = None):
        """执行调度器步骤"""
        if epoch is None and self.last_epoch < 0:
            epoch = 0
        
        if epoch is None:
            epoch = self.last_epoch + 1
            self.T_cur = self.T_cur + 1
            if self.T_cur >= self.T_i:
                self.T_cur = self.T_cur - self.T_i
                self.T_i = self.T_i * self.T_mult
        else:
            if epoch < 0:
                raise ValueError("Expected non-negative epoch, but got {}".format(epoch))
            if epoch >= self.T_0:
                if self.T_mult == 1:
                    self.T_cur = epoch % self.T_0
                else:
                    n = int(math.log((epoch / self.T_0 * (self.T_mult - 1) + 1), self.T_mult))
                    self.T_cur = epoch - self.T_0 * (self.T_mult ** n - 1) / (self.T_mult - 1)
                    self.T_i = self.T_0 * self.T_mult ** (n)
            else:
                self.T_i = self.T_0
                self.T_cur = epoch
        
        self.last_epoch = math.floor(epoch)
        
        class _enable_get_lr_call:
            def __init__(self, o):
                self.o = o
            
            def __enter__(self):
                self.o._get_lr_called_within_step = True
                return self
            
            def __exit__(self, type, value, traceback):
                self.o._get_lr_called_within_step = False
        
        with _enable_get_lr_call(self):
            for i, data in enumerate(zip(self.optimizer.param_groups, self.get_lr())):
                param_group, lr = data
                param_group['lr'] = lr
                self.print_lr(self.verbose, i, lr, epoch)
        
        self._last_lr = [group['lr'] for group in self.optimizer.param_groups]
