#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CyclicLR调度器实现
基于PyTorch标准CyclicLR，针对遥感任务进行优化
"""

import torch
from torch.optim.lr_scheduler import _LRScheduler
from typing import List, Optional, Union, Callable
import warnings
import math


class CyclicLR(_LRScheduler):
    """
    CyclicLR调度器
    
    实现循环学习率调度，在基础学习率和最大学习率之间循环
    特别适合遥感图像的训练任务
    
    Args:
        optimizer: 优化器
        base_lr: 基础学习率
        max_lr: 最大学习率
        step_size_up: 上升阶段步数 (默认: 2000)
        step_size_down: 下降阶段步数 (默认: None, 等于step_size_up)
        mode: 循环模式 ('triangular', 'triangular2', 'exp_range') (默认: 'triangular')
        gamma: exp_range模式的衰减因子 (默认: 1.0)
        scale_fn: 自定义缩放函数 (默认: None)
        scale_mode: 缩放模式 ('cycle' 或 'iterations') (默认: 'cycle')
        cycle_momentum: 是否循环动量 (默认: True)
        base_momentum: 基础动量 (默认: 0.8)
        max_momentum: 最大动量 (默认: 0.9)
        last_epoch: 上一个epoch (默认: -1)
        verbose: 是否打印学习率变化 (默认: False)
    """
    
    def __init__(
        self,
        optimizer: torch.optim.Optimizer,
        base_lr: Union[float, List[float]],
        max_lr: Union[float, List[float]],
        step_size_up: int = 2000,
        step_size_down: Optional[int] = None,
        mode: str = 'triangular',
        gamma: float = 1.0,
        scale_fn: Optional[Callable] = None,
        scale_mode: str = 'cycle',
        cycle_momentum: bool = True,
        base_momentum: Union[float, List[float]] = 0.8,
        max_momentum: Union[float, List[float]] = 0.9,
        last_epoch: int = -1,
        verbose: bool = False
    ):
        if step_size_up <= 0:
            raise ValueError("Expected positive step_size_up, but got {}".format(step_size_up))
        
        self.base_lrs = self._format_param('base_lr', optimizer, base_lr)
        self.max_lrs = self._format_param('max_lr', optimizer, max_lr)
        self.step_size_up = step_size_up
        self.step_size_down = step_size_down or step_size_up
        self.total_size = self.step_size_up + self.step_size_down
        self.mode = mode
        self.gamma = gamma
        self.scale_fn = scale_fn
        self.scale_mode = scale_mode
        self.cycle_momentum = cycle_momentum
        
        if self.cycle_momentum:
            if 'momentum' not in optimizer.defaults and 'betas' not in optimizer.defaults:
                raise ValueError('optimizer must support momentum with `cycle_momentum` option enabled')
            
            self.base_momentums = self._format_param('base_momentum', optimizer, base_momentum)
            self.max_momentums = self._format_param('max_momentum', optimizer, max_momentum)
        
        super().__init__(optimizer, last_epoch, verbose)
    
    def _format_param(self, name: str, optimizer: torch.optim.Optimizer, param: Union[float, List[float]]) -> List[float]:
        """格式化参数"""
        if isinstance(param, (list, tuple)):
            if len(param) != len(optimizer.param_groups):
                raise ValueError("expected {} values for {}, got {}".format(
                    len(optimizer.param_groups), name, len(param)))
            return list(param)
        else:
            return [param] * len(optimizer.param_groups)
    
    def get_lr(self) -> List[float]:
        """计算CyclicLR学习率"""
        if not self._get_lr_called_within_step:
            warnings.warn("To get the last learning rate computed by the scheduler, "
                         "please use `get_last_lr()`.", UserWarning)
        
        cycle = math.floor(1 + self.last_epoch / self.total_size)
        x = 1 + self.last_epoch / self.total_size - cycle
        
        if x <= self.step_size_up / self.total_size:
            scale_factor = x / (self.step_size_up / self.total_size)
        else:
            scale_factor = (x - 1) / (self.step_size_down / self.total_size) + 1
        
        lrs = []
        for base_lr, max_lr in zip(self.base_lrs, self.max_lrs):
            base_height = (max_lr - base_lr) * scale_factor
            
            if self.scale_fn is None:
                if self.mode == 'triangular':
                    lr = base_lr + base_height
                elif self.mode == 'triangular2':
                    lr = base_lr + base_height / (2 ** (cycle - 1))
                elif self.mode == 'exp_range':
                    lr = base_lr + base_height * (self.gamma ** self.last_epoch)
            else:
                if self.scale_mode == 'cycle':
                    lr = base_lr + base_height * self.scale_fn(cycle)
                else:
                    lr = base_lr + base_height * self.scale_fn(self.last_epoch)
            
            lrs.append(lr)
        
        return lrs
