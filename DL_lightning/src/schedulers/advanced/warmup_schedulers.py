#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
预热调度器实现
提供各种预热策略的学习率调度器
"""

import torch
from torch.optim.lr_scheduler import _LRScheduler
from typing import List, Optional
import warnings
import math


class WarmupSchedulers:
    """预热调度器集合类"""
    
    class WarmupCosineAnnealingLR(_LRScheduler):
        """
        带预热的余弦退火调度器
        
        Args:
            optimizer: 优化器
            warmup_epochs: 预热epoch数
            max_epochs: 最大epoch数
            warmup_start_lr: 预热起始学习率 (默认: 1e-6)
            eta_min: 最小学习率 (默认: 1e-6)
            last_epoch: 上一个epoch (默认: -1)
            verbose: 是否打印学习率变化 (默认: False)
        """
        
        def __init__(
            self,
            optimizer: torch.optim.Optimizer,
            warmup_epochs: int,
            max_epochs: int,
            warmup_start_lr: float = 1e-6,
            eta_min: float = 1e-6,
            last_epoch: int = -1,
            verbose: bool = False
        ):
            self.warmup_epochs = warmup_epochs
            self.max_epochs = max_epochs
            self.warmup_start_lr = warmup_start_lr
            self.eta_min = eta_min
            super().__init__(optimizer, last_epoch, verbose)
        
        def get_lr(self) -> List[float]:
            """计算带预热的余弦退火学习率"""
            if not self._get_lr_called_within_step:
                warnings.warn("To get the last learning rate computed by the scheduler, "
                             "please use `get_last_lr()`.", UserWarning)
            
            if self.last_epoch < self.warmup_epochs:
                # 预热阶段
                return [self.warmup_start_lr + (base_lr - self.warmup_start_lr) * 
                        self.last_epoch / self.warmup_epochs for base_lr in self.base_lrs]
            else:
                # 余弦退火阶段
                progress = (self.last_epoch - self.warmup_epochs) / (self.max_epochs - self.warmup_epochs)
                return [self.eta_min + (base_lr - self.eta_min) * 
                        (1 + math.cos(math.pi * progress)) / 2 for base_lr in self.base_lrs]
    
    class WarmupPolynomialLR(_LRScheduler):
        """
        带预热的多项式调度器
        
        Args:
            optimizer: 优化器
            warmup_epochs: 预热epoch数
            max_epochs: 最大epoch数
            power: 多项式次数 (默认: 1.0)
            warmup_start_lr: 预热起始学习率 (默认: 1e-6)
            last_epoch: 上一个epoch (默认: -1)
            verbose: 是否打印学习率变化 (默认: False)
        """
        
        def __init__(
            self,
            optimizer: torch.optim.Optimizer,
            warmup_epochs: int,
            max_epochs: int,
            power: float = 1.0,
            warmup_start_lr: float = 1e-6,
            last_epoch: int = -1,
            verbose: bool = False
        ):
            self.warmup_epochs = warmup_epochs
            self.max_epochs = max_epochs
            self.power = power
            self.warmup_start_lr = warmup_start_lr
            super().__init__(optimizer, last_epoch, verbose)
        
        def get_lr(self) -> List[float]:
            """计算带预热的多项式学习率"""
            if not self._get_lr_called_within_step:
                warnings.warn("To get the last learning rate computed by the scheduler, "
                             "please use `get_last_lr()`.", UserWarning)
            
            if self.last_epoch < self.warmup_epochs:
                # 预热阶段
                return [self.warmup_start_lr + (base_lr - self.warmup_start_lr) * 
                        self.last_epoch / self.warmup_epochs for base_lr in self.base_lrs]
            else:
                # 多项式衰减阶段
                progress = (self.last_epoch - self.warmup_epochs) / (self.max_epochs - self.warmup_epochs)
                decay_factor = (1 - progress) ** self.power
                return [base_lr * decay_factor for base_lr in self.base_lrs]
    
    class WarmupLinearLR(_LRScheduler):
        """
        带预热的线性调度器
        
        Args:
            optimizer: 优化器
            warmup_epochs: 预热epoch数
            max_epochs: 最大epoch数
            warmup_start_lr: 预热起始学习率 (默认: 1e-6)
            end_lr: 结束学习率 (默认: 0)
            last_epoch: 上一个epoch (默认: -1)
            verbose: 是否打印学习率变化 (默认: False)
        """
        
        def __init__(
            self,
            optimizer: torch.optim.Optimizer,
            warmup_epochs: int,
            max_epochs: int,
            warmup_start_lr: float = 1e-6,
            end_lr: float = 0,
            last_epoch: int = -1,
            verbose: bool = False
        ):
            self.warmup_epochs = warmup_epochs
            self.max_epochs = max_epochs
            self.warmup_start_lr = warmup_start_lr
            self.end_lr = end_lr
            super().__init__(optimizer, last_epoch, verbose)
        
        def get_lr(self) -> List[float]:
            """计算带预热的线性学习率"""
            if not self._get_lr_called_within_step:
                warnings.warn("To get the last learning rate computed by the scheduler, "
                             "please use `get_last_lr()`.", UserWarning)
            
            if self.last_epoch < self.warmup_epochs:
                # 预热阶段
                return [self.warmup_start_lr + (base_lr - self.warmup_start_lr) * 
                        self.last_epoch / self.warmup_epochs for base_lr in self.base_lrs]
            else:
                # 线性衰减阶段
                progress = (self.last_epoch - self.warmup_epochs) / (self.max_epochs - self.warmup_epochs)
                return [base_lr - (base_lr - self.end_lr) * progress for base_lr in self.base_lrs]


# 为了方便使用，直接导出常用的预热调度器
WarmupCosineAnnealingLR = WarmupSchedulers.WarmupCosineAnnealingLR
WarmupPolynomialLR = WarmupSchedulers.WarmupPolynomialLR
WarmupLinearLR = WarmupSchedulers.WarmupLinearLR
