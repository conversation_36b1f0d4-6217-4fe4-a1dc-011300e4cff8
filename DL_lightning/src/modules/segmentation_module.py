import torch
import torch.nn.functional as F
import lightning.pytorch as pl
import hydra
from torchmetrics.classification import JaccardIndex
from torchmetrics.segmentation import DiceScore
from typing import Any, Dict, List, Optional
from omegaconf import DictConfig
import logging

# 导入模型架构
from ..models.segmentation import AVAILABLE_ARCHITECTURES

class SegmentationModule(pl.LightningModule):
    """
    语义分割Lightning模块 - 现代化版本

    支持的功能：
    - 多种模型架构（DeepLabV3+, UNet, UNet++, Swin-UNet等）
    - Hydra配置驱动的模型创建
    - 自动混合精度训练
    - 分布式训练支持
    - 完整的训练生命周期管理

    Args:
        model_name: 模型架构名称
        model_params: 模型参数配置
        optimizer_cfg: 优化器配置
        scheduler_cfg: 学习率调度器配置
        loss_cfg: 损失函数配置
        num_classes: 分割类别数
        ignore_index: 忽略的标签索引，默认255
        **kwargs: 其他参数
    """
    def __init__(
        self,
        model_name: str,
        model_params: DictConfig,
        optimizer_cfg: DictConfig,
        scheduler_cfg: DictConfig,
        loss_cfg: DictConfig,
        num_classes: int,
        ignore_index: int = 255,
        **kwargs
    ):
        super().__init__()

        # 保存超参数（忽略已实例化的模块）
        self.save_hyperparameters(ignore=['loss_cfg'])

        self.model_name = model_name
        self.num_classes = num_classes
        self.ignore_index = ignore_index
        # 使用根logger以继承Loguru配置
        self.logger_instance = logging.getLogger()

        # 创建模型架构
        self.architecture = self._create_model(model_name, model_params, num_classes)

        # 保存配置，在configure_optimizers中实例化
        self.optimizer_cfg = optimizer_cfg
        self.scheduler_cfg = scheduler_cfg

        # 创建损失函数（简化处理）
        if isinstance(loss_cfg, dict) and '_target_' in loss_cfg:
            if loss_cfg['_target_'] == 'torch.nn.CrossEntropyLoss':
                import torch.nn as nn
                self.loss_fn = nn.CrossEntropyLoss(ignore_index=loss_cfg.get('ignore_index', 255))
            else:
                self.loss_fn = hydra.utils.instantiate(loss_cfg)
        else:
            # 默认使用CrossEntropyLoss
            import torch.nn as nn
            self.loss_fn = nn.CrossEntropyLoss(ignore_index=self.ignore_index)

        # 创建指标
        self.train_iou = JaccardIndex(
            num_classes=self.num_classes,
            task='multiclass',
            ignore_index=self.ignore_index
        )
        self.val_iou = JaccardIndex(
            num_classes=self.num_classes,
            task='multiclass',
            ignore_index=self.ignore_index
        )
        # DiceScore不支持ignore_index参数，显式设置average参数避免警告
        self.train_dice = DiceScore(
            num_classes=self.num_classes,
            average='micro'  # 显式设置避免未来版本警告
        )
        self.val_dice = DiceScore(
            num_classes=self.num_classes,
            average='micro'  # 显式设置避免未来版本警告
        )

        # 记录模型信息
        self._log_model_info()

    def _create_model(self, model_name: str, model_params: DictConfig, num_classes: int):
        """创建模型架构"""
        if model_name not in AVAILABLE_ARCHITECTURES:
            raise ValueError(
                f"不支持的模型架构: {model_name}。"
                f"可用的架构: {list(AVAILABLE_ARCHITECTURES.keys())}"
            )

        model_class = AVAILABLE_ARCHITECTURES[model_name]

        # 准备模型参数
        model_kwargs = dict(model_params)
        model_kwargs['num_classes'] = num_classes

        # 创建模型
        model = model_class(**model_kwargs)

        self.logger_instance.info(f"创建模型: {model_name}")
        self.logger_instance.info(f"模型参数: {model_kwargs}")

        return model

    def _log_model_info(self):
        """记录模型信息"""
        if hasattr(self.architecture, 'get_model_info'):
            model_info = self.architecture.get_model_info()
            self.logger_instance.info("="*50)
            self.logger_instance.info("模型详细信息:")
            for key, value in model_info.items():
                self.logger_instance.info(f"{key}: {value}")
            self.logger_instance.info("="*50)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.architecture(x)

    def _shared_step(self, batch: Dict[str, torch.Tensor], batch_idx: int, stage: str):
        """共享的训练/验证步骤"""
        images = batch["image"]
        masks = batch["mask"]

        # 前向传播
        logits = self.forward(images)

        # 计算损失
        loss = self.loss_fn(logits, masks)

        # 获取预测结果
        preds = torch.argmax(logits, dim=1)

        # 选择对应的指标
        iou_metric = self.train_iou if stage == "train" else self.val_iou
        dice_metric = self.train_dice if stage == "train" else self.val_dice

        # 更新指标
        iou_metric(preds, masks)
        dice_metric(preds, masks)

        # 记录指标，明确指定batch_size避免推断警告
        batch_size = images.size(0)
        self.log(f"{stage}/loss", loss, on_step=True, on_epoch=True, prog_bar=True, sync_dist=True, batch_size=batch_size)
        self.log(f"{stage}/iou", iou_metric, on_step=False, on_epoch=True, prog_bar=True, sync_dist=True, batch_size=batch_size)
        self.log(f"{stage}/dice", dice_metric, on_step=False, on_epoch=True, prog_bar=True, sync_dist=True, batch_size=batch_size)

        # 记录学习率（仅训练时且有trainer时）
        if stage == "train":
            try:
                # 检查是否有trainer且已连接
                if hasattr(self, '_trainer') and self._trainer is not None:
                    self.log("lr", self.optimizers().param_groups[0]['lr'], on_step=True, on_epoch=False, prog_bar=True, batch_size=batch_size)
            except:
                pass  # 如果没有trainer，跳过学习率记录

        return loss

    def training_step(self, batch: Dict[str, torch.Tensor], batch_idx: int):
        return self._shared_step(batch, batch_idx, "train")

    def validation_step(self, batch: Dict[str, torch.Tensor], batch_idx: int):
        self._shared_step(batch, batch_idx, "val")

    def on_validation_epoch_end(self) -> None:
        """验证epoch结束时的处理"""
        # 计算并记录最终的验证指标
        val_iou_value = self.val_iou.compute()
        val_dice_value = self.val_dice.compute()

        # 这里不需要batch_size，因为是epoch级别的聚合指标
        self.log("val/iou_final", val_iou_value, on_epoch=True, sync_dist=True)
        self.log("val/dice_final", val_dice_value, on_epoch=True, sync_dist=True)

        # 重置指标状态
        self.val_iou.reset()
        self.val_dice.reset()

    def test_step(self, batch: Dict[str, torch.Tensor], batch_idx: int):
        """测试步骤"""
        return self._shared_step(batch, batch_idx, "test")

    def predict_step(self, batch: Dict[str, torch.Tensor], batch_idx: int, dataloader_idx: int = 0):
        """预测步骤"""
        images = batch["image"]
        logits = self.forward(images)
        preds = torch.argmax(logits, dim=1)
        return {
            "predictions": preds,
            "logits": logits,
            "image_names": batch.get("image_name", None)
        }

    def configure_optimizers(self):
        """
        配置优化器和学习率调度器 - 增强版本

        增强内容:
        - 支持工厂函数创建优化器和调度器
        - 支持字符串名称和配置对象两种方式
        - 支持预热调度器
        - 改进错误处理和日志记录
        """
        # 1. 创建优化器
        optimizer = self._create_optimizer()

        # 2. 如果没有调度器配置，只返回优化器
        if self.scheduler_cfg is None:
            return optimizer

        # 3. 创建调度器
        scheduler = self._create_scheduler(optimizer)

        # 4. 返回配置字典
        return {
            "optimizer": optimizer,
            "lr_scheduler": {
                "scheduler": scheduler,
                "monitor": "val/loss",
                "interval": "epoch",
                "frequency": 1,
            },
        }

    def _create_optimizer(self):
        """创建优化器"""
        try:
            # 使用Hydra配置方式创建优化器
            optimizer = hydra.utils.instantiate(
                self.optimizer_cfg,
                params=self.architecture.parameters(),
                _partial_=False
            )
            self.logger_instance.info(f"优化器创建成功: {type(optimizer).__name__}")
            return optimizer

        except Exception as e:
            # 降级到默认优化器
            from src.optimizers.standard.adamw import AdamW
            optimizer = AdamW(self.architecture.parameters(), lr=1e-3)
            self.logger_instance.warning(f"优化器配置失败，使用默认AdamW: {e}")
            return optimizer

    def _create_scheduler(self, optimizer):
        """创建学习率调度器"""
        try:
            # 检查是否需要预热
            warmup_epochs = getattr(self.scheduler_cfg, 'warmup_epochs', 0)
            if warmup_epochs > 0:
                # 实现预热调度器逻辑
                warmup_start_lr = getattr(self.scheduler_cfg, 'warmup_start_lr', 1e-6)

                # 创建主调度器配置（移除预热参数）
                from omegaconf import OmegaConf
                main_scheduler_cfg = OmegaConf.structured(self.scheduler_cfg)
                if hasattr(main_scheduler_cfg, 'warmup_epochs'):
                    delattr(main_scheduler_cfg, 'warmup_epochs')
                if hasattr(main_scheduler_cfg, 'warmup_start_lr'):
                    delattr(main_scheduler_cfg, 'warmup_start_lr')

                # 创建主调度器
                main_scheduler = hydra.utils.instantiate(
                    main_scheduler_cfg,
                    optimizer=optimizer,
                    _partial_=False
                )

                # 创建预热调度器
                from torch.optim.lr_scheduler import LinearLR, SequentialLR

                warmup_scheduler = LinearLR(
                    optimizer,
                    start_factor=warmup_start_lr / optimizer.param_groups[0]['lr'],
                    end_factor=1.0,
                    total_iters=warmup_epochs
                )

                # 组合调度器
                scheduler = SequentialLR(
                    optimizer,
                    schedulers=[warmup_scheduler, main_scheduler],
                    milestones=[warmup_epochs]
                )

                self.logger_instance.info(f"预热调度器创建成功: {type(scheduler).__name__}")
                return scheduler

            # 使用Hydra配置方式创建调度器
            scheduler = hydra.utils.instantiate(
                self.scheduler_cfg,
                optimizer=optimizer,
                _partial_=False
            )
            self.logger_instance.info(f"调度器创建成功: {type(scheduler).__name__}")

            # 验证调度器对象
            if not hasattr(scheduler, 'optimizer'):
                raise ValueError("调度器对象缺少 optimizer 属性")

            return scheduler

        except Exception as e:
            # 降级到默认调度器
            from src.schedulers.standard.cosine_annealing import CosineAnnealingLR
            scheduler = CosineAnnealingLR(optimizer, T_max=100, eta_min=1e-6)
            self.logger_instance.warning(f"调度器配置失败，使用默认CosineAnnealingLR: {e}")
            return scheduler

    def on_train_epoch_start(self) -> None:
        """训练epoch开始时的处理"""
        self.logger_instance.info(f"开始训练 Epoch {self.current_epoch + 1}")

    def on_validation_epoch_start(self) -> None:
        """验证epoch开始时的处理"""
        self.logger_instance.info(f"开始验证 Epoch {self.current_epoch + 1}")


    def get_model_summary(self) -> str:
        """获取模型摘要信息"""
        if hasattr(self.architecture, 'get_model_info'):
            model_info = self.architecture.get_model_info()
            summary = f"Model: {model_info.get('name', self.model_name)}\n"
            summary += f"Parameters: {model_info.get('num_parameters', 'Unknown'):,}\n"
            summary += f"Model Size: {model_info.get('model_size_mb', 'Unknown'):.2f} MB\n"
            summary += f"Classes: {self.num_classes}\n"
            return summary
        else:
            return f"Model: {self.model_name}, Classes: {self.num_classes}"