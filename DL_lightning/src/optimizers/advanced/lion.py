#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Lion优化器实现
基于Google Research的Lion: Evolved Sign Momentum
论文: https://arxiv.org/abs/2302.06675
"""

import torch
from torch.optim.optimizer import Optimizer
from typing import Any, Dict, Optional, Union, Iterable


class Lion(Optimizer):
    """
    Lion优化器 - Evolved Sign Momentum
    
    Lion是Google Research提出的新型优化器，具有以下特点：
    - 内存效率高（只需要动量状态）
    - 计算简单（主要是符号操作）
    - 在大规模训练中表现优异
    - 对学习率敏感，通常需要比Adam小3-10倍的学习率
    
    Args:
        params: 模型参数
        lr: 学习率 (默认: 1e-4, 比Adam小)
        betas: 动量系数 (默认: (0.9, 0.99))
        weight_decay: 权重衰减 (默认: 0.0)
        maximize: 是否最大化目标函数 (默认: False)
        foreach: 是否使用foreach实现 (默认: None)
        differentiable: 是否支持高阶导数 (默认: False)
    """
    
    def __init__(
        self,
        params: Iterable[torch.Tensor],
        lr: float = 1e-4,
        betas: tuple = (0.9, 0.99),
        weight_decay: float = 0.0,
        maximize: bool = False,
        foreach: Optional[bool] = None,
        differentiable: bool = False,
        **kwargs
    ):
        if not 0.0 <= lr:
            raise ValueError(f"Invalid learning rate: {lr}")
        if not 0.0 <= betas[0] < 1.0:
            raise ValueError(f"Invalid beta parameter at index 0: {betas[0]}")
        if not 0.0 <= betas[1] < 1.0:
            raise ValueError(f"Invalid beta parameter at index 1: {betas[1]}")
        if not 0.0 <= weight_decay:
            raise ValueError(f"Invalid weight_decay value: {weight_decay}")
        
        defaults = dict(
            lr=lr,
            betas=betas,
            weight_decay=weight_decay,
            maximize=maximize,
            foreach=foreach,
            differentiable=differentiable
        )
        super().__init__(params, defaults)
    
    def __setstate__(self, state):
        super().__setstate__(state)
        for group in self.param_groups:
            group.setdefault('maximize', False)
            group.setdefault('foreach', None)
            group.setdefault('differentiable', False)
    
    def _init_group(self, group, params_with_grad, grads, exp_avgs):
        """初始化参数组"""
        for p in group['params']:
            if p.grad is None:
                continue
            params_with_grad.append(p)
            if p.grad.dtype in {torch.float16, torch.bfloat16}:
                grads.append(p.grad)
            else:
                grads.append(p.grad)
            
            state = self.state[p]
            # 延迟状态初始化
            if len(state) == 0:
                # Lion只需要一个动量状态
                state['exp_avg'] = torch.zeros_like(p, memory_format=torch.preserve_format)
            
            exp_avgs.append(state['exp_avg'])
    
    def step(self, closure: Optional[callable] = None) -> Optional[float]:
        """执行单步优化"""
        loss = None
        if closure is not None:
            with torch.enable_grad():
                loss = closure()
        
        for group in self.param_groups:
            params_with_grad = []
            grads = []
            exp_avgs = []
            beta1, beta2 = group['betas']
            
            self._init_group(group, params_with_grad, grads, exp_avgs)
            
            # 执行Lion更新
            self._lion_update(
                params_with_grad,
                grads,
                exp_avgs,
                beta1=beta1,
                beta2=beta2,
                lr=group['lr'],
                weight_decay=group['weight_decay'],
                maximize=group['maximize'],
                foreach=group['foreach'],
                differentiable=group['differentiable']
            )
        
        return loss
    
    def _lion_update(
        self,
        params,
        grads,
        exp_avgs,
        *,
        beta1: float,
        beta2: float,
        lr: float,
        weight_decay: float,
        maximize: bool,
        foreach: Optional[bool],
        differentiable: bool
    ):
        """Lion更新逻辑"""
        if foreach is None:
            foreach = False  # 简化实现
        
        if foreach and torch.jit.is_scripting():
            raise RuntimeError('torch.jit.script not supported with foreach optimizers')
        
        if foreach and not torch.jit.is_scripting():
            func = self._multi_tensor_lion
        else:
            func = self._single_tensor_lion
        
        func(
            params,
            grads,
            exp_avgs,
            beta1=beta1,
            beta2=beta2,
            lr=lr,
            weight_decay=weight_decay,
            maximize=maximize,
            differentiable=differentiable
        )
    
    def _single_tensor_lion(
        self,
        params,
        grads,
        exp_avgs,
        *,
        beta1: float,
        beta2: float,
        lr: float,
        weight_decay: float,
        maximize: bool,
        differentiable: bool
    ):
        """单张量Lion更新"""
        for i, param in enumerate(params):
            grad = grads[i] if not maximize else -grads[i]
            exp_avg = exp_avgs[i]
            
            # 权重衰减
            if weight_decay != 0:
                param.mul_(1 - lr * weight_decay)
            
            # Lion的核心更新规则
            # 1. 计算更新方向：sign(beta1 * m + (1-beta1) * g)
            update = exp_avg.mul(beta1).add_(grad, alpha=1 - beta1)
            param.add_(torch.sign(update), alpha=-lr)
            
            # 2. 更新动量：beta2 * m + (1-beta2) * g
            exp_avg.mul_(beta2).add_(grad, alpha=1 - beta2)
    
    def _multi_tensor_lion(self, *args, **kwargs):
        """多张量Lion更新（简化版本）"""
        # 为了简化，直接调用单张量版本
        self._single_tensor_lion(*args, **kwargs)
