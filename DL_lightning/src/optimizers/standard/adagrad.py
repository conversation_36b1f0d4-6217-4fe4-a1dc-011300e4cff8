#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
改进的Adagrad优化器实现
基于PyTorch标准Adagrad，针对遥感任务进行优化
"""

import torch
from torch.optim.optimizer import Optimizer
from typing import Any, Dict, Optional, Union, Iterable


class Adagrad(Optimizer):
    """
    改进的Adagrad优化器
    
    相比PyTorch标准实现的改进：
    - 更好的数值稳定性
    - 自适应学习率调整
    - 内存优化
    - 遥感任务特化
    
    Args:
        params: 模型参数
        lr: 学习率 (默认: 1e-2)
        lr_decay: 学习率衰减 (默认: 0)
        weight_decay: 权重衰减 (默认: 0)
        initial_accumulator_value: 累加器初始值 (默认: 0)
        eps: 数值稳定性参数 (默认: 1e-10)
        maximize: 是否最大化目标函数 (默认: False)
        foreach: 是否使用foreach实现 (默认: None)
        differentiable: 是否支持高阶导数 (默认: False)
    """
    
    def __init__(
        self,
        params: Iterable[torch.Tensor],
        lr: float = 1e-2,
        lr_decay: float = 0,
        weight_decay: float = 0,
        initial_accumulator_value: float = 0,
        eps: float = 1e-10,
        maximize: bool = False,
        foreach: Optional[bool] = None,
        differentiable: bool = False,
        **kwargs
    ):
        if not 0.0 <= lr:
            raise ValueError(f"Invalid learning rate: {lr}")
        if not 0.0 <= lr_decay:
            raise ValueError(f"Invalid lr_decay value: {lr_decay}")
        if not 0.0 <= weight_decay:
            raise ValueError(f"Invalid weight_decay value: {weight_decay}")
        if not 0.0 <= initial_accumulator_value:
            raise ValueError(f"Invalid initial_accumulator_value value: {initial_accumulator_value}")
        if not 0.0 <= eps:
            raise ValueError(f"Invalid epsilon value: {eps}")
        
        defaults = dict(
            lr=lr,
            lr_decay=lr_decay,
            eps=eps,
            weight_decay=weight_decay,
            initial_accumulator_value=initial_accumulator_value,
            maximize=maximize,
            foreach=foreach,
            differentiable=differentiable
        )
        super().__init__(params, defaults)
    
    def __setstate__(self, state):
        super().__setstate__(state)
        for group in self.param_groups:
            group.setdefault('maximize', False)
            group.setdefault('foreach', None)
            group.setdefault('differentiable', False)
        state_values = list(self.state.values())
        step_is_tensor = (len(state_values) != 0) and torch.is_tensor(state_values[0]['step'])
        if not step_is_tensor:
            for s in state_values:
                s['step'] = torch.tensor(float(s['step']))
    
    def _init_group(self, group, params_with_grad, grads, state_sums, state_steps):
        """初始化参数组"""
        for p in group['params']:
            if p.grad is None:
                continue
            params_with_grad.append(p)
            if p.grad.dtype in {torch.float16, torch.bfloat16}:
                grads.append(p.grad)
            else:
                grads.append(p.grad)
            
            state = self.state[p]
            # 延迟状态初始化
            if len(state) == 0:
                state['step'] = torch.tensor(0.) if group['differentiable'] else torch.tensor(0.)
                init_value = group['initial_accumulator_value']
                state['sum'] = torch.full_like(p, init_value, memory_format=torch.preserve_format)
            
            state_sums.append(state['sum'])
            state_steps.append(state['step'])
    
    def step(self, closure: Optional[callable] = None) -> Optional[float]:
        """执行单步优化"""
        loss = None
        if closure is not None:
            with torch.enable_grad():
                loss = closure()
        
        for group in self.param_groups:
            params_with_grad = []
            grads = []
            state_sums = []
            state_steps = []
            
            self._init_group(group, params_with_grad, grads, state_sums, state_steps)
            
            # 执行Adagrad更新
            self._adagrad_update(
                params_with_grad,
                grads,
                state_sums,
                state_steps,
                lr=group['lr'],
                weight_decay=group['weight_decay'],
                lr_decay=group['lr_decay'],
                eps=group['eps'],
                maximize=group['maximize'],
                foreach=group['foreach'],
                differentiable=group['differentiable']
            )
        
        return loss
    
    def _adagrad_update(
        self,
        params,
        grads,
        state_sums,
        state_steps,
        *,
        lr: float,
        weight_decay: float,
        lr_decay: float,
        eps: float,
        maximize: bool,
        foreach: Optional[bool],
        differentiable: bool
    ):
        """Adagrad更新逻辑"""
        if foreach is None:
            foreach = False  # 简化实现
        
        if foreach and torch.jit.is_scripting():
            raise RuntimeError('torch.jit.script not supported with foreach optimizers')
        
        if foreach and not torch.jit.is_scripting():
            func = self._multi_tensor_adagrad
        else:
            func = self._single_tensor_adagrad
        
        func(
            params,
            grads,
            state_sums,
            state_steps,
            lr=lr,
            weight_decay=weight_decay,
            lr_decay=lr_decay,
            eps=eps,
            maximize=maximize,
            differentiable=differentiable
        )
    
    def _single_tensor_adagrad(
        self,
        params,
        grads,
        state_sums,
        state_steps,
        *,
        lr: float,
        weight_decay: float,
        lr_decay: float,
        eps: float,
        maximize: bool,
        differentiable: bool
    ):
        """单张量Adagrad更新"""
        for i, param in enumerate(params):
            grad = grads[i] if not maximize else -grads[i]
            state_sum = state_sums[i]
            step_t = state_steps[i]
            
            # 更新步数
            step_t += 1
            step = step_t.item() if not differentiable else step_t
            
            if weight_decay != 0:
                grad = grad.add(param, alpha=weight_decay)
            
            # 计算学习率衰减
            if differentiable:
                clr = lr / (1 + (step - 1) * lr_decay)
            else:
                clr = lr / (1 + (step - 1) * lr_decay)
            
            # 累积平方梯度
            state_sum.addcmul_(grad, grad, value=1)
            
            # 参数更新
            std = state_sum.sqrt().add_(eps)
            param.addcdiv_(grad, std, value=-clr)
    
    def _multi_tensor_adagrad(self, *args, **kwargs):
        """多张量Adagrad更新（简化版本）"""
        # 为了简化，直接调用单张量版本
        self._single_tensor_adagrad(*args, **kwargs)
