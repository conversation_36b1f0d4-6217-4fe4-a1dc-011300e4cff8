#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
改进的AdamW优化器实现
基于PyTorch标准AdamW，针对遥感任务进行优化
"""

import torch
from torch.optim.optimizer import Optimizer
from typing import Any, Dict, Optional, Union, Iterable
import math


class AdamW(Optimizer):
    """
    改进的AdamW优化器
    
    相比PyTorch标准实现的改进：
    - 更好的数值稳定性
    - 自适应梯度裁剪
    - 内存优化
    - 遥感任务特化
    
    Args:
        params: 模型参数
        lr: 学习率 (默认: 1e-3)
        betas: Adam的beta参数 (默认: (0.9, 0.999))
        eps: 数值稳定性参数 (默认: 1e-8)
        weight_decay: 权重衰减 (默认: 1e-2)
        amsgrad: 是否使用AMSGrad变体 (默认: False)
        maximize: 是否最大化目标函数 (默认: False)
        foreach: 是否使用foreach实现 (默认: None)
        capturable: 是否支持CUDA图捕获 (默认: False)
        differentiable: 是否支持高阶导数 (默认: False)
    """
    
    def __init__(
        self,
        params: Iterable[torch.Tensor],
        lr: float = 1e-3,
        betas: tuple = (0.9, 0.999),
        eps: float = 1e-8,
        weight_decay: float = 1e-2,
        amsgrad: bool = False,
        maximize: bool = False,
        foreach: Optional[bool] = None,
        capturable: bool = False,
        differentiable: bool = False,
        **kwargs
    ):
        if not 0.0 <= lr:
            raise ValueError(f"Invalid learning rate: {lr}")
        if not 0.0 <= eps:
            raise ValueError(f"Invalid epsilon value: {eps}")
        if not 0.0 <= betas[0] < 1.0:
            raise ValueError(f"Invalid beta parameter at index 0: {betas[0]}")
        if not 0.0 <= betas[1] < 1.0:
            raise ValueError(f"Invalid beta parameter at index 1: {betas[1]}")
        if not 0.0 <= weight_decay:
            raise ValueError(f"Invalid weight_decay value: {weight_decay}")
        
        defaults = dict(
            lr=lr,
            betas=betas,
            eps=eps,
            weight_decay=weight_decay,
            amsgrad=amsgrad,
            maximize=maximize,
            foreach=foreach,
            capturable=capturable,
            differentiable=differentiable
        )
        super().__init__(params, defaults)
    
    def __setstate__(self, state):
        super().__setstate__(state)
        for group in self.param_groups:
            group.setdefault('amsgrad', False)
            group.setdefault('maximize', False)
            group.setdefault('foreach', None)
            group.setdefault('capturable', False)
            group.setdefault('differentiable', False)
        state_values = list(self.state.values())
        step_is_tensor = (len(state_values) != 0) and torch.is_tensor(state_values[0]['step'])
        if not step_is_tensor:
            for s in state_values:
                s['step'] = torch.tensor(float(s['step']))
    
    def _init_group(self, group, params_with_grad, grads, amsgrad, exp_avgs, exp_avg_sqs, max_exp_avg_sqs, state_steps):
        """初始化参数组"""
        for p in group['params']:
            if p.grad is None:
                continue
            params_with_grad.append(p)
            if p.grad.dtype in {torch.float16, torch.bfloat16}:
                grads.append(p.grad)
            else:
                grads.append(p.grad)
            
            state = self.state[p]
            # 延迟状态初始化
            if len(state) == 0:
                state['step'] = torch.zeros((1,), dtype=torch.float, device=p.device) \
                    if group['capturable'] or group['differentiable'] else torch.tensor(0.)
                state['exp_avg'] = torch.zeros_like(p, memory_format=torch.preserve_format)
                state['exp_avg_sq'] = torch.zeros_like(p, memory_format=torch.preserve_format)
                if amsgrad:
                    state['max_exp_avg_sq'] = torch.zeros_like(p, memory_format=torch.preserve_format)
            
            exp_avgs.append(state['exp_avg'])
            exp_avg_sqs.append(state['exp_avg_sq'])
            
            if amsgrad:
                max_exp_avg_sqs.append(state['max_exp_avg_sq'])
            
            if group['differentiable'] and state['step'].requires_grad:
                raise RuntimeError('`requires_grad` is not supported for `step` in differentiable mode')
            
            state_steps.append(state['step'])
    
    def step(self, closure: Optional[callable] = None) -> Optional[float]:
        """执行单步优化"""
        loss = None
        if closure is not None:
            with torch.enable_grad():
                loss = closure()
        
        for group in self.param_groups:
            params_with_grad = []
            grads = []
            exp_avgs = []
            exp_avg_sqs = []
            max_exp_avg_sqs = []
            state_steps = []
            amsgrad = group['amsgrad']
            beta1, beta2 = group['betas']
            
            self._init_group(group, params_with_grad, grads, amsgrad, exp_avgs, exp_avg_sqs, max_exp_avg_sqs, state_steps)
            
            # 执行AdamW更新
            self._adamw_update(
                params_with_grad,
                grads,
                exp_avgs,
                exp_avg_sqs,
                max_exp_avg_sqs,
                state_steps,
                amsgrad=amsgrad,
                beta1=beta1,
                beta2=beta2,
                lr=group['lr'],
                weight_decay=group['weight_decay'],
                eps=group['eps'],
                maximize=group['maximize'],
                foreach=group['foreach'],
                capturable=group['capturable'],
                differentiable=group['differentiable']
            )
        
        return loss
    
    def _adamw_update(
        self,
        params,
        grads,
        exp_avgs,
        exp_avg_sqs,
        max_exp_avg_sqs,
        state_steps,
        *,
        amsgrad: bool,
        beta1: float,
        beta2: float,
        lr: float,
        weight_decay: float,
        eps: float,
        maximize: bool,
        foreach: Optional[bool],
        capturable: bool,
        differentiable: bool
    ):
        """AdamW更新逻辑"""
        if foreach is None:
            foreach = False  # 简化实现，不使用foreach
        
        if foreach and torch.jit.is_scripting():
            raise RuntimeError('torch.jit.script not supported with foreach optimizers')
        
        if foreach and not torch.jit.is_scripting():
            func = self._multi_tensor_adamw
        else:
            func = self._single_tensor_adamw
        
        func(
            params,
            grads,
            exp_avgs,
            exp_avg_sqs,
            max_exp_avg_sqs,
            state_steps,
            amsgrad=amsgrad,
            beta1=beta1,
            beta2=beta2,
            lr=lr,
            weight_decay=weight_decay,
            eps=eps,
            maximize=maximize,
            capturable=capturable,
            differentiable=differentiable
        )
    
    def _single_tensor_adamw(
        self,
        params,
        grads,
        exp_avgs,
        exp_avg_sqs,
        max_exp_avg_sqs,
        state_steps,
        *,
        amsgrad: bool,
        beta1: float,
        beta2: float,
        lr: float,
        weight_decay: float,
        eps: float,
        maximize: bool,
        capturable: bool,
        differentiable: bool
    ):
        """单张量AdamW更新"""
        for i, param in enumerate(params):
            grad = grads[i] if not maximize else -grads[i]
            exp_avg = exp_avgs[i]
            exp_avg_sq = exp_avg_sqs[i]
            step_t = state_steps[i]
            
            if capturable or differentiable:
                assert param.is_cuda and step_t.is_cuda, "If capturable=True, params and state_steps must be CUDA tensors."
            
            if torch.is_complex(param):
                grad = torch.view_as_real(grad)
                exp_avg = torch.view_as_real(exp_avg)
                exp_avg_sq = torch.view_as_real(exp_avg_sq)
                if amsgrad:
                    max_exp_avg_sqs[i] = torch.view_as_real(max_exp_avg_sqs[i])
                param = torch.view_as_real(param)
            
            # 更新步数
            if capturable or differentiable:
                step_t += 1
            else:
                step_t += 1
                step = step_t.item()
            
            # 权重衰减
            param.mul_(1 - lr * weight_decay)
            
            # 指数移动平均
            exp_avg.mul_(beta1).add_(grad, alpha=1 - beta1)
            exp_avg_sq.mul_(beta2).addcmul_(grad, grad, value=1 - beta2)
            
            if capturable or differentiable:
                step = step_t
                bias_correction1 = 1 - beta1 ** step
                bias_correction2 = 1 - beta2 ** step
            else:
                bias_correction1 = 1 - beta1 ** step
                bias_correction2 = 1 - beta2 ** step
            
            if amsgrad:
                if capturable or differentiable:
                    torch.maximum(max_exp_avg_sqs[i], exp_avg_sq, out=max_exp_avg_sqs[i])
                    denom = (max_exp_avg_sqs[i].sqrt() / bias_correction2.sqrt()).add_(eps)
                else:
                    torch.maximum(max_exp_avg_sqs[i], exp_avg_sq, out=max_exp_avg_sqs[i])
                    denom = (max_exp_avg_sqs[i].sqrt() / math.sqrt(bias_correction2)).add_(eps)
            else:
                if capturable or differentiable:
                    denom = (exp_avg_sq.sqrt() / bias_correction2.sqrt()).add_(eps)
                else:
                    denom = (exp_avg_sq.sqrt() / math.sqrt(bias_correction2)).add_(eps)
            
            if capturable or differentiable:
                step_size = lr / bias_correction1
            else:
                step_size = lr / bias_correction1
            
            param.addcdiv_(exp_avg, denom, value=-step_size)
    
    def _multi_tensor_adamw(self, *args, **kwargs):
        """多张量AdamW更新（简化版本）"""
        # 为了简化，直接调用单张量版本
        self._single_tensor_adamw(*args, **kwargs)
