#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基于Loguru的训练进度回调

功能特性:
- 替代Lightning默认进度条
- 集成系统资源监控  
- 统一文本格式输出
- 训练生命周期管理
- 实时指标更新
"""

import time
from typing import Any, Dict, Optional

import lightning.pytorch as pl
from lightning.pytorch.callbacks import Callback
from loguru import logger

try:
    from ..utils.system_monitor import SystemMonitor
    SYSTEM_MONITOR_AVAILABLE = True
except ImportError:
    SYSTEM_MONITOR_AVAILABLE = False


class LoguruProgressCallback(Callback):
    """基于Loguru的训练进度回调"""
    
    def __init__(self, 
                 refresh_rate: int = 10,
                 enable_system_monitor: bool = True,
                 show_system_stats: bool = True,
                 leave_progress: bool = True):
        """初始化Loguru进度回调
        
        Args:
            refresh_rate: 进度条刷新频率
            enable_system_monitor: 是否启用系统监控
            show_system_stats: 是否显示系统统计
            leave_progress: 完成后是否保留进度条
        """
        super().__init__()
        self.refresh_rate = refresh_rate
        self.enable_system_monitor = enable_system_monitor
        self.show_system_stats = show_system_stats
        self.leave_progress = leave_progress
        
        # 系统监控
        self.system_monitor = None
        if enable_system_monitor and SYSTEM_MONITOR_AVAILABLE:
            try:
                self.system_monitor = SystemMonitor()
            except Exception:
                self.system_monitor = None
        
        # 训练状态
        self.train_batch_idx = 0
        self.val_batch_idx = 0
        self.current_epoch = 0
        self.start_time = None
        self.epoch_start_time = None
        
        # 指标缓存
        self.last_metrics = {}
    
    def on_fit_start(self, trainer: pl.Trainer, pl_module: pl.LightningModule):
        """训练开始"""
        self.start_time = time.time()
        logger.info("=" * 60)
        logger.info("开始训练")
        logger.info("=" * 60)
        logger.info(f"最大轮数: {trainer.max_epochs}")
        logger.info(f"设备: {trainer.strategy.root_device}")
        
        # 显示模型信息
        if hasattr(pl_module, 'model'):
            total_params = sum(p.numel() for p in pl_module.parameters())
            trainable_params = sum(p.numel() for p in pl_module.parameters() if p.requires_grad)
            logger.info(f"模型参数: {total_params:,} (可训练: {trainable_params:,})")
        
        logger.info("=" * 60)
    
    def on_train_epoch_start(self, trainer: pl.Trainer, pl_module: pl.LightningModule):
        """训练epoch开始"""
        self.current_epoch = trainer.current_epoch
        self.epoch_start_time = time.time()
        self.train_batch_idx = 0
        
        logger.info(f"Epoch {self.current_epoch + 1}/{trainer.max_epochs} - 训练开始")
    
    def on_train_batch_end(self, trainer: pl.Trainer, pl_module: pl.LightningModule, 
                          outputs: Any, batch: Any, batch_idx: int):
        """训练batch结束"""
        self.train_batch_idx = batch_idx
        
        # 按刷新频率更新进度
        if batch_idx % self.refresh_rate == 0:
            self._log_training_progress(trainer, pl_module, outputs, batch_idx)
    
    def on_validation_epoch_start(self, trainer: pl.Trainer, pl_module: pl.LightningModule):
        """验证epoch开始"""
        self.val_batch_idx = 0
        logger.info(f"Epoch {self.current_epoch + 1}/{trainer.max_epochs} - 验证开始")
    
    def on_validation_batch_end(self, trainer: pl.Trainer, pl_module: pl.LightningModule,
                               outputs: Any, batch: Any, batch_idx: int):
        """验证batch结束"""
        self.val_batch_idx = batch_idx
        
        # 按刷新频率更新进度
        if batch_idx % self.refresh_rate == 0:
            self._log_validation_progress(trainer, pl_module, outputs, batch_idx)
    
    def on_train_epoch_end(self, trainer: pl.Trainer, pl_module: pl.LightningModule):
        """训练epoch结束"""
        if self.epoch_start_time:
            epoch_time = time.time() - self.epoch_start_time
            logger.info(f"Epoch {self.current_epoch + 1} 训练完成 - 耗时: {epoch_time:.2f}秒")
    
    def on_validation_epoch_end(self, trainer: pl.Trainer, pl_module: pl.LightningModule):
        """验证epoch结束"""
        # 记录验证指标
        if hasattr(trainer, 'callback_metrics'):
            metrics = trainer.callback_metrics
            self._log_epoch_metrics(metrics)
    
    def on_fit_end(self, trainer: pl.Trainer, pl_module: pl.LightningModule):
        """训练结束"""
        if self.start_time:
            total_time = time.time() - self.start_time
            logger.info("=" * 60)
            logger.success(f"训练完成! 总耗时: {total_time:.2f}秒")
            
            # 显示最终指标
            if hasattr(trainer, 'callback_metrics'):
                final_metrics = trainer.callback_metrics
                self._log_final_metrics(final_metrics)
            
            logger.info("=" * 60)
    
    def _log_training_progress(self, trainer: pl.Trainer, pl_module: pl.LightningModule, 
                              outputs: Any, batch_idx: int):
        """记录训练进度"""
        # 计算进度
        if hasattr(trainer, 'num_training_batches'):
            total_batches = trainer.num_training_batches
            progress_pct = (batch_idx / total_batches) * 100 if total_batches > 0 else 0
        else:
            progress_pct = 0
            total_batches = "?"
        
        # 获取损失值
        loss_value = "N/A"
        if outputs and isinstance(outputs, dict) and 'loss' in outputs:
            loss_value = f"{outputs['loss']:.4f}"
        elif hasattr(pl_module, 'training_step_outputs') and pl_module.training_step_outputs:
            last_output = pl_module.training_step_outputs[-1]
            if isinstance(last_output, dict) and 'loss' in last_output:
                loss_value = f"{last_output['loss']:.4f}"
        
        # 获取学习率
        lr_value = "N/A"
        if hasattr(pl_module, 'optimizers'):
            optimizers = pl_module.optimizers()
            if not isinstance(optimizers, list):
                optimizers = [optimizers]
            if optimizers and len(optimizers) > 0:
                lr_value = f"{optimizers[0].param_groups[0]['lr']:.2e}"
        
        # 构建进度消息
        progress_msg = f"Epoch {self.current_epoch + 1}/{trainer.max_epochs} - 训练: {progress_pct:.1f}% ({batch_idx}/{total_batches})"
        if loss_value != "N/A":
            progress_msg += f" | Loss: {loss_value}"
        if lr_value != "N/A":
            progress_msg += f" | LR: {lr_value}"
        
        # 添加系统统计
        if self.show_system_stats and self.system_monitor:
            try:
                stats = self.system_monitor.get_current_stats()
                stats_dict = {
                    'cpu_percent': stats.cpu_percent,
                    'memory_percent': stats.memory_percent,
                    'gpu_stats': stats.gpu_stats
                }
                sys_msg = self._format_system_stats(stats_dict)
                if sys_msg:
                    progress_msg += f" | {sys_msg}"
            except Exception:
                pass  # 忽略系统监控错误
        
        logger.info(f"[PROGRESS] {progress_msg}")
    
    def _log_validation_progress(self, trainer: pl.Trainer, pl_module: pl.LightningModule,
                                outputs: Any, batch_idx: int):
        """记录验证进度"""
        # 计算进度
        if hasattr(trainer, 'num_val_batches'):
            total_batches = trainer.num_val_batches[0] if isinstance(trainer.num_val_batches, list) else trainer.num_val_batches
            progress_pct = (batch_idx / total_batches) * 100 if total_batches > 0 else 0
        else:
            progress_pct = 0
            total_batches = "?"
        
        progress_msg = f"Epoch {self.current_epoch + 1}/{trainer.max_epochs} - 验证: {progress_pct:.1f}% ({batch_idx}/{total_batches})"
        logger.info(f"[PROGRESS] {progress_msg}")
    
    def _log_epoch_metrics(self, metrics: Dict[str, Any]):
        """记录epoch指标"""
        if not metrics:
            return
        
        # 过滤并格式化指标
        formatted_metrics = []
        for key, value in metrics.items():
            if isinstance(value, (int, float)):
                if 'loss' in key.lower():
                    formatted_metrics.append(f"{key}: {value:.4f}")
                elif 'acc' in key.lower() or 'iou' in key.lower():
                    formatted_metrics.append(f"{key}: {value:.4f}")
                else:
                    formatted_metrics.append(f"{key}: {value:.4f}")
        
        if formatted_metrics:
            metrics_str = " | ".join(formatted_metrics)
            logger.info(f"[METRICS] {metrics_str}")
    
    def _log_final_metrics(self, metrics: Dict[str, Any]):
        """记录最终指标"""
        if not metrics:
            return
        
        logger.info("最终指标:")
        for key, value in metrics.items():
            if isinstance(value, (int, float)):
                logger.info(f"  {key}: {value:.4f}")
    
    def _format_system_stats(self, stats: Dict[str, Any]) -> str:
        """格式化系统统计信息"""
        parts = []
        
        if 'cpu_percent' in stats:
            parts.append(f"CPU: {stats['cpu_percent']:.1f}%")
        
        if 'memory_percent' in stats:
            parts.append(f"RAM: {stats['memory_percent']:.1f}%")
        
        if 'gpu_stats' in stats and stats['gpu_stats']:
            gpu_stats = stats['gpu_stats']
            if isinstance(gpu_stats, list) and len(gpu_stats) > 0:
                gpu_util = gpu_stats[0].get('utilization', 0)
                gpu_memory = gpu_stats[0].get('memory_percent', 0)
                parts.append(f"GPU: {gpu_util:.1f}%")
                parts.append(f"VRAM: {gpu_memory:.1f}%")
        
        return " | ".join(parts) if parts else ""


class LoguruModelSummaryCallback(Callback):
    """基于Loguru的模型摘要回调"""
    
    def on_fit_start(self, trainer: pl.Trainer, pl_module: pl.LightningModule):
        """显示模型结构摘要"""
        logger.info("=" * 60)
        logger.info("模型结构摘要")
        logger.info("=" * 60)
        
        # 显示模型基本信息
        model_name = pl_module.__class__.__name__
        logger.info(f"模型类型: {model_name}")
        
        # 计算参数数量
        total_params = sum(p.numel() for p in pl_module.parameters())
        trainable_params = sum(p.numel() for p in pl_module.parameters() if p.requires_grad)
        
        logger.info(f"总参数数: {total_params:,}")
        logger.info(f"可训练参数: {trainable_params:,}")
        logger.info(f"冻结参数: {total_params - trainable_params:,}")
        
        # 显示模型大小估算
        param_size_mb = total_params * 4 / (1024 * 1024)  # 假设float32
        logger.info(f"模型大小估算: {param_size_mb:.2f} MB")
        
        logger.info("=" * 60)
