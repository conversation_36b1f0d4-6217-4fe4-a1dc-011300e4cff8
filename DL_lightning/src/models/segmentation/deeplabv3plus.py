"""
DeepLabV3+语义分割模型实现 - Lightning版本
基于PyTorch的DeepLabV3+模型，支持多种backbone网络
适配PyTorch Lightning + Hydra配置系统
"""

from typing import Dict, List, Optional

import torch
import torch.nn as nn
import torch.nn.functional as F

# 将 torchvision 作为本模块的强制依赖在顶部导入
# 如果用户使用此模型，则必须安装 torchvision
import torchvision.models as models
from torchvision.models import ResNet50_Weights, ResNet101_Weights


class ASPP(nn.Module):
    """
    Atrous Spatial Pyramid Pooling模块
    用于提取多尺度特征的空洞卷积金字塔池化模块
    """
    def __init__(self, in_channels: int, out_channels: int, rates: List[int] = [6, 12, 18]):
        super(ASPP, self).__init__()
        
        # 1x1卷积
        self.aspp0 = nn.Sequential(
            nn.Conv2d(in_channels, out_channels, 1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
        
        # 带有不同空洞率的3x3卷积
        self.aspp1 = self._make_atrous_branch(in_channels, out_channels, rate=rates[0])
        self.aspp2 = self._make_atrous_branch(in_channels, out_channels, rate=rates[1])
        self.aspp3 = self._make_atrous_branch(in_channels, out_channels, rate=rates[2])
        
        # 全局平均池化分支
        self.global_avg_pool = nn.Sequential(
            nn.AdaptiveAvgPool2d((1, 1)),
            nn.Conv2d(in_channels, out_channels, 1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
        
        # 合并所有分支的输出
        self.output = nn.Sequential(
            nn.Conv2d(out_channels * 5, out_channels, 1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5)
        )
    
    def _make_atrous_branch(self, in_channels: int, out_channels: int, rate: int) -> nn.Sequential:
        """创建空洞卷积分支"""
        return nn.Sequential(
            nn.Conv2d(in_channels, out_channels, 3, padding=rate, dilation=rate, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x):
        size = x.size()
        
        x0 = self.aspp0(x)
        x1 = self.aspp1(x)
        x2 = self.aspp2(x)
        x3 = self.aspp3(x)
        
        # 全局特征
        x4 = self.global_avg_pool(x)
        x4 = F.interpolate(x4, size=size[2:], mode='bilinear', align_corners=True)
        
        # 合并所有特征
        x = torch.cat((x0, x1, x2, x3, x4), dim=1)
        x = self.output(x)
        
        return x


class DeepLabV3Plus(nn.Module):
    """
    DeepLabV3+语义分割模型 - Lightning版本
    
    支持的backbone:
    - resnet50: ResNet-50
    - resnet101: ResNet-101
    
    Args:
        num_classes: 分割类别数
        in_channels: 输入图像通道数，默认3
        backbone: 骨干网络类型，默认'resnet50'
        pretrained: 是否使用预训练权重，默认True
    """
    
    def __init__(
        self, 
        num_classes: int, 
        in_channels: int = 3, 
        backbone: str = 'resnet50', 
        pretrained: bool = True
    ):
        super(DeepLabV3Plus, self).__init__()
        
        self.num_classes = num_classes
        self.in_channels = in_channels
        self.backbone_name = backbone
        
        # 创建骨干网络
        self.backbone = self._create_backbone(backbone, pretrained)
        
        # 获取骨干网络的通道数
        if backbone == 'resnet50':
            low_level_channels = 256  # layer1输出通道数
            high_level_channels = 2048  # layer4输出通道数
        elif backbone == 'resnet101':
            low_level_channels = 256
            high_level_channels = 2048
        else:
            raise ValueError(f"不支持的backbone: {backbone}")
        
        # ASPP模块
        self.aspp = ASPP(high_level_channels, 256)
        
        # 最终分类器
        self.classifier = nn.Conv2d(256, num_classes, 1)
        
        # 如果输入通道数不是3，需要调整第一层
        if in_channels != 3:
            self._modify_input_layer(in_channels)
    
    def _create_backbone(self, backbone: str, pretrained: bool):
        """创建骨干网络"""
        if backbone == 'resnet50':
            if pretrained:
                model = models.resnet50(weights=ResNet50_Weights.IMAGENET1K_V2)
            else:
                model = models.resnet50(weights=None)
        elif backbone == 'resnet101':
            if pretrained:
                model = models.resnet101(weights=ResNet101_Weights.IMAGENET1K_V2)
            else:
                model = models.resnet101(weights=None)
        else:
            raise ValueError(f"不支持的backbone: {backbone}")
        
        # 移除最后的全连接层和平均池化层
        return nn.ModuleDict({
            'conv1': model.conv1,
            'bn1': model.bn1,
            'relu': model.relu,
            'maxpool': model.maxpool,
            'layer1': model.layer1,
            'layer2': model.layer2,
            'layer3': model.layer3,
            'layer4': model.layer4,
        })
    
    def _modify_input_layer(self, in_channels: int):
        """修改输入层以适应不同的输入通道数"""
        old_conv = self.backbone['conv1']
        self.backbone['conv1'] = nn.Conv2d(
            in_channels, 
            old_conv.out_channels,
            kernel_size=old_conv.kernel_size,
            stride=old_conv.stride,
            padding=old_conv.padding,
            bias=old_conv.bias
        )
        
        # 如果使用预训练权重，需要调整权重
        if hasattr(old_conv, 'weight'):
            with torch.no_grad():
                if in_channels == 1:
                    # 单通道：取RGB权重的平均值
                    self.backbone['conv1'].weight = nn.Parameter(
                        old_conv.weight.mean(dim=1, keepdim=True)
                    )
                elif in_channels > 3:
                    # 多通道：复制RGB权重
                    new_weight = old_conv.weight.repeat(1, in_channels // 3 + 1, 1, 1)
                    self.backbone['conv1'].weight = nn.Parameter(
                        new_weight[:, :in_channels, :, :]
                    )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        input_size = x.size()[2:]
        
        # 骨干网络前向传播
        x = self.backbone['conv1'](x)
        x = self.backbone['bn1'](x)
        x = self.backbone['relu'](x)
        x = self.backbone['maxpool'](x)
        
        # 保存低层次特征
        low_level_features = self.backbone['layer1'](x)
        
        # 继续前向传播
        x = self.backbone['layer2'](low_level_features)
        x = self.backbone['layer3'](x)
        x = self.backbone['layer4'](x)
        
        # ASPP特征提取
        x = self.aspp(x)
        
        # 最终分类
        x = self.classifier(x)
        
        # 上采样到输入尺寸
        x = F.interpolate(x, size=input_size, mode='bilinear', align_corners=True)
        
        return x
    
    def get_model_info(self) -> Dict[str, any]:
        """获取模型信息"""
        num_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        model_size = sum(p.numel() * p.element_size() for p in self.parameters()) / (1024 * 1024)
        
        return {
            'name': 'DeepLabV3Plus',
            'backbone': self.backbone_name,
            'num_classes': self.num_classes,
            'in_channels': self.in_channels,
            'num_parameters': num_params,
            'model_size_mb': model_size
        }
