"""
组合损失函数 - Lightning版本
支持多个损失函数组合使用，如CE+Dice+Focal
适配PyTorch Lightning 2.3+ 和 Hydra 1.3.2+
"""

import logging
from typing import Any, Dict, List, Optional, Union, Tuple

import torch
import torch.nn as nn

from .base import BaseLoss


class CombinedLoss(BaseLoss):
    """
    组合损失函数 - Lightning版本

    支持多个损失函数的加权组合，常用组合包括：
    - CrossEntropy + Dice
    - CrossEntropy + Focal
    - CrossEntropy + Dice + Focal
    - 支持动态类别权重更新
    """

    def __init__(
        self,
        loss_weights: Optional[Dict[str, float]] = None,
        ignore_index: int = 255,
        use_dynamic_weights: bool = True,
        **kwargs
    ):
        """
        初始化组合损失函数

        Args:
            loss_weights: 各损失函数的权重，如 {'ce': 0.5, 'dice': 0.3, 'focal': 0.2}
            ignore_index: 忽略的标签索引
            use_dynamic_weights: 是否启用子损失函数的动态类别权重更新
            **kwargs: 其他参数传递给子损失函数
        """
        super().__init__()
        self.supports_class_weights = use_dynamic_weights

        if loss_weights is None:
            loss_weights = {'ce': 0.5, 'dice': 0.5}

        self.loss_weights = loss_weights
        self.ignore_index = ignore_index

        # 初始化各个损失函数
        self.losses = nn.ModuleDict()

        # 从kwargs中提取各损失函数的特定参数
        num_classes = kwargs.get('num_classes', 14)

        if 'ce' in loss_weights:
            from .cross_entropy_loss import CrossEntropyLoss
            self.losses['ce'] = CrossEntropyLoss(
                num_classes=num_classes,
                ignore_index=ignore_index,
                use_dynamic_weights=use_dynamic_weights,
                **kwargs.get('ce_params', {})
            )

        if 'dice' in loss_weights:
            from .dice_loss import DiceLoss
            self.losses['dice'] = DiceLoss(
                num_classes=num_classes,
                ignore_index=ignore_index,
                **kwargs.get('dice_params', {})
            )

        if 'focal' in loss_weights:
            from .focal_loss import FocalLoss
            self.losses['focal'] = FocalLoss(
                num_classes=num_classes,
                ignore_index=ignore_index,
                use_dynamic_weights=use_dynamic_weights,
                **kwargs.get('focal_params', {})
            )

        if 'lovasz' in loss_weights:
            from .lovasz_loss import LovaszLoss
            self.losses['lovasz'] = LovaszLoss(
                num_classes=num_classes,
                ignore_index=ignore_index,
                **kwargs.get('lovasz_params', {})
            )

        # 记录支持类别权重的损失函数
        self.weight_supported_losses = {}
        for name, loss in self.losses.items():
            if hasattr(loss, 'supports_class_weights') and loss.supports_class_weights:
                self.weight_supported_losses[name] = loss

        # 验证配置
        self._validate_config()

        # 记录初始化信息
        self.logger.info(f"组合损失: 包含 {len(self.losses)} 个损失函数")
        for name, loss in self.losses.items():
            self.logger.info(f"  - {name}: 组合权重={self.loss_weights[name]}, "
                           f"类型={type(loss).__name__}, "
                           f"支持类别权重: {hasattr(loss, 'supports_class_weights') and loss.supports_class_weights}")

        if self.weight_supported_losses:
            self.logger.info(f"组合损失中有 {len(self.weight_supported_losses)} 个损失函数支持动态类别权重更新")

        if use_dynamic_weights and not self.weight_supported_losses:
            self.logger.warning("组合损失启用了动态类别权重，但没有子损失函数支持权重更新")

    def _validate_config(self):
        """验证配置是否合理"""
        if not self.losses:
            raise ValueError("组合损失必须至少包含一个损失函数")

        for name in self.loss_weights:
            if name not in self.losses:
                self.logger.warning(f"损失权重中指定的 {name} 损失函数未初始化")

    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        计算组合损失

        Args:
            inputs: 模型预测，形状为 (N, C, H, W)
            targets: 真实标签，形状为 (N, H, W)

        Returns:
            torch.Tensor: 组合损失值
        """
        losses = {}
        total_loss = 0.0

        for name, loss_fn in self.losses.items():
            if name in self.loss_weights:
                weight = self.loss_weights[name]
                loss_value = loss_fn(inputs, targets)
                losses[name] = loss_value.item() if isinstance(loss_value, torch.Tensor) else loss_value
                total_loss = total_loss + weight * loss_value

        # 保存分项损失
        self.last_losses = losses
        self.last_loss = total_loss

        return total_loss

    def update_weights(
        self,
        class_counts: torch.Tensor,
        epoch: Optional[int] = None,
        alpha: float = 0.8
    ) -> bool:
        """
        更新支持类别权重的子损失函数的类别权重

        Args:
            class_counts: 类别统计数据
            epoch: 当前训练轮次
            alpha: 指数移动平均系数

        Returns:
            bool: 是否至少有一个子损失函数更新了权重
        """
        if not self.supports_class_weights or not self.weight_supported_losses:
            return False

        updated = False

        # 获取当前设备
        device = next(self.parameters()).device if list(self.parameters()) else torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # 确保类别计数在正确设备上
        if device.type != 'cpu' and class_counts.device.type == 'cpu':
            class_counts = class_counts.to(device)

        # 更新每个支持权重的子损失函数
        for name, loss in self.weight_supported_losses.items():
            if loss.update_weights(class_counts, epoch, alpha):
                updated = True
                self.logger.info(f"更新了 {name} 的类别权重")

        # 保存最后更新的轮次
        if updated:
            self.last_update_epoch = epoch

        return updated

    def get_losses(self) -> Union[Dict[str, float], None]:
        """获取分项损失"""
        if hasattr(self, 'last_losses'):
            return self.last_losses
        return None

    def get_weight_info(self) -> Union[Dict[str, str], str]:
        """获取子损失函数的类别权重信息"""
        weight_info = {}
        for name, loss in self.weight_supported_losses.items():
            if hasattr(loss, 'get_weight_info'):
                info = loss.get_weight_info()
                weight_info[name] = str(info)  # 确保转换为字符串

        if not weight_info:
            return "无支持权重的损失函数"

        return weight_info
