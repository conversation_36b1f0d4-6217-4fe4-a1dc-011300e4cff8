"""
Focal损失函数 - Lightning版本
专为处理类别不平衡问题设计，支持动态类别权重
适配PyTorch Lightning 2.3+ 和 Hydra 1.3.2+
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Union, Tuple

from .base import BaseLoss


class FocalLoss(BaseLoss):
    """
    Focal损失函数 - Lightning版本
    
    专为处理类别不平衡问题设计，支持动态类别权重
    基于论文: "Focal Loss for Dense Object Detection"
    """
    
    def __init__(
        self, 
        num_classes: int = 14, 
        alpha: Union[float, torch.Tensor] = 0.25, 
        gamma: float = 2.0, 
        ignore_index: int = 255, 
        weight: Optional[torch.Tensor] = None, 
        reduction: str = 'mean', 
        use_dynamic_weights: bool = True, 
        weight_method: str = 'inverse'
    ):
        """
        初始化Focal损失
        
        Args:
            num_classes: 类别数量
            alpha: 平衡因子，可以是标量或向量
            gamma: 聚焦参数，控制难易样本的权重
            ignore_index: 忽略的标签值
            weight: 初始类别权重
            reduction: 损失聚合方式 ('mean', 'sum', 'none')
            use_dynamic_weights: 是否使用动态类别权重
            weight_method: 权重计算方法 ('inverse', 'sqrt_inverse', 'log_inverse')
        """
        super().__init__()
        self.num_classes = num_classes
        self.alpha = alpha
        self.gamma = gamma
        self.ignore_index = ignore_index
        self.reduction = reduction
        self.supports_class_weights = use_dynamic_weights
        self.weights = weight
        self.weight_method = weight_method
        
        # 记录初始化信息
        self.logger.info(f"Focal损失: 类别数={num_classes}, alpha={alpha}, gamma={gamma}, 忽略索引={ignore_index}")
        
        if weight is not None:
            self.logger.info(f"初始类别权重: {weight}")
        else:
            self.logger.info("初始化无类别权重")
            
        if use_dynamic_weights:
            self.logger.info(f"启用动态类别权重更新, 方法: {weight_method}")
    
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        计算Focal损失
        
        Args:
            inputs: 模型输出，形状为 (N, C, H, W)
            targets: 真实标签，形状为 (N, H, W)
            
        Returns:
            torch.Tensor: Focal损失值
        """
        # 准备忽略掩码
        ignore_mask = targets != self.ignore_index
        
        # 重塑张量以便计算
        if inputs.dim() > 2:
            inputs = inputs.permute(0, 2, 3, 1).contiguous().view(-1, self.num_classes)
            targets = targets.view(-1)
            ignore_mask = ignore_mask.view(-1)
            
        # 过滤掉要忽略的位置
        if self.ignore_index is not None:
            valid_idx = torch.nonzero(ignore_mask, as_tuple=True)[0]
            if valid_idx.numel() == 0:
                return torch.tensor(0.0, device=inputs.device, requires_grad=True)
            inputs = inputs[valid_idx]
            targets = targets[valid_idx]
        
        # 计算交叉熵损失
        ce_loss = F.cross_entropy(
            inputs, targets, 
            weight=self.weights,
            reduction='none'
        )
        
        # 计算概率
        pt = torch.exp(-ce_loss)
        
        # 计算Focal项
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        # 应用alpha平衡因子
        if self.alpha is not None:
            if isinstance(self.alpha, (float, int)):
                # 标量alpha
                alpha_t = torch.ones_like(targets, device=inputs.device, dtype=torch.float) * self.alpha
                alpha_t = torch.where(targets > 0, alpha_t, 1 - self.alpha)
            else:
                # 向量alpha
                alpha_t = torch.ones_like(targets, device=inputs.device, dtype=torch.float)
                for cls in range(self.num_classes):
                    alpha_t[targets == cls] = self.alpha[cls]
            
            focal_loss = alpha_t * focal_loss
        
        # 应用reduction
        if self.reduction == 'mean':
            self.last_loss = focal_loss.mean()
        elif self.reduction == 'sum':
            self.last_loss = focal_loss.sum()
        else:
            self.last_loss = focal_loss
            
        return self.last_loss
    
    def update_weights(
        self, 
        class_counts: torch.Tensor, 
        epoch: Optional[int] = None, 
        alpha: float = 0.8
    ) -> bool:
        """
        更新类别权重
        
        Args:
            class_counts: 类别统计数据，形状为 (num_classes,)
            epoch: 当前训练轮次
            alpha: 指数移动平均系数
            
        Returns:
            bool: 是否成功更新权重
        """
        if not self.supports_class_weights:
            return False
        
        # 获取当前设备
        device = next(self.parameters()).device if list(self.parameters()) else torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 确保类别计数在正确设备上
        if device.type != 'cpu' and class_counts.device.type == 'cpu':
            class_counts = class_counts.to(device)
        
        # 计算新的权重
        new_weights = self._calculate_class_weights(class_counts)
        new_weights = new_weights.to(device)
        
        # 记录更新前的权重状态
        old_weight_info = self.get_weight_info()
        
        # 使用指数移动平均更新权重
        if hasattr(self, 'weights') and self.weights is not None:
            self.weights = alpha * self.weights + (1 - alpha) * new_weights
        else:
            self.weights = new_weights
        
        # 记录更新后的权重状态
        new_weight_info = self.get_weight_info()
        
        # 记录日志
        self.logger.info(f"Focal损失类别权重更新: {old_weight_info} -> {new_weight_info}")
        if epoch is not None:
            self.logger.info(f"Epoch {epoch}: Focal损失类别权重已更新")
        
        # 保存最后更新的轮次
        self.last_update_epoch = epoch
        return True
    
    def _calculate_class_weights(self, class_counts: torch.Tensor) -> torch.Tensor:
        """
        计算类别权重
        
        Args:
            class_counts: 类别统计数据
            
        Returns:
            torch.Tensor: 计算得到的类别权重
        """
        # 避免除零
        class_counts = class_counts.float()
        class_counts = torch.clamp(class_counts, min=1.0)
        
        if self.weight_method == 'inverse':
            # 反比例权重
            total_samples = class_counts.sum()
            weights = total_samples / (self.num_classes * class_counts)
        elif self.weight_method == 'sqrt_inverse':
            # 平方根反比例权重
            total_samples = class_counts.sum()
            weights = torch.sqrt(total_samples / (self.num_classes * class_counts))
        elif self.weight_method == 'log_inverse':
            # 对数反比例权重
            weights = torch.log(class_counts.sum() / class_counts)
        else:
            # 默认使用反比例
            total_samples = class_counts.sum()
            weights = total_samples / (self.num_classes * class_counts)
        
        # 归一化权重
        weights = weights / weights.sum() * self.num_classes
        
        return weights
    
    def get_losses(self) -> Union[float, Tuple[float, ...], None]:
        """获取分项损失"""
        if hasattr(self, 'last_loss'):
            if self.last_loss.dim() == 0:  # scalar
                return self.last_loss.item()
            else:  # per-sample losses
                return tuple(self.last_loss.detach().cpu().tolist())
        return None
    
    def get_weight_info(self) -> str:
        """获取权重信息"""
        if not hasattr(self, 'weights') or self.weights is None:
            return "未初始化权重"
        
        weights = self.weights.detach().cpu()
        return f"权重范围: [{weights.min():.4f}, {weights.max():.4f}], 均值: {weights.mean():.4f}"
