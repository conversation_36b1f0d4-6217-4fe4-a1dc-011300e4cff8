"""
损失函数基类 - Lightning版本
定义所有损失函数共享的接口和基本功能
适配PyTorch Lightning 2.3+ 和 Hydra 1.3.2+
"""

import logging
from typing import Any, Dict, List, Optional, Tuple, TypeVar, Union, cast

import torch
import torch.nn as nn
from torch.nn.modules.module import _IncompatibleKeys


class BaseLoss(nn.Module):
    """
    损失函数基类，定义通用接口
    
    适配PyTorch Lightning训练流程，支持动态类别权重更新
    """
    
    def __init__(self):
        """初始化基类"""
        super().__init__()
        self.supports_class_weights = False  # 是否支持类别权重
        self.logger = logging.getLogger(__name__)
        self.last_update_epoch: Optional[int] = None  # 最后更新权重的轮次
    
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        前向传播计算损失
        
        Args:
            inputs: 模型输出，形状为 (N, C, H, W)
            targets: 真实标签，形状为 (N, H, W)
            
        Returns:
            torch.Tensor: 损失值
        """
        raise NotImplementedError("子类必须实现forward方法")
    
    def update_weights(
        self, 
        class_counts: torch.Tensor, 
        epoch: Optional[int] = None, 
        alpha: float = 0.8
    ) -> bool:
        """
        更新类别权重（如果支持）
        
        Args:
            class_counts: 类别统计数据，形状为 (num_classes,)
            epoch: 当前训练轮次
            alpha: 指数移动平均系数
            
        Returns:
            bool: 是否成功更新权重
        """
        if not self.supports_class_weights:
            return False
        
        # 记录最后更新的轮次
        self.last_update_epoch = epoch
        return True
    
    def get_losses(self) -> Union[float, Tuple[float, ...], None]:
        """
        获取分项损失（如果有）
        
        Returns:
            单个损失值、损失值元组或None
        """
        return None
    
    def get_weight_info(self) -> str:
        """
        获取权重信息（用于日志记录）
        
        Returns:
            str: 权重信息字符串
        """
        return "不支持类别权重"
    
    def state_dict(self, *args, **kwargs) -> Dict[str, Any]:
        """重写state_dict以保存类别权重"""
        state = super().state_dict(*args, **kwargs)
        
        # 添加额外状态
        if hasattr(self, 'weights') and self.weights is not None:
            prefix = kwargs.get('prefix', '')
            state[prefix + 'class_weights'] = self.weights
        if hasattr(self, 'last_update_epoch') and self.last_update_epoch is not None:
            prefix = kwargs.get('prefix', '')
            state[prefix + 'last_update_epoch'] = self.last_update_epoch
            
        return state
    
    def load_state_dict(
        self, 
        state_dict: Dict[str, Any], 
        strict: bool = True
    ) -> _IncompatibleKeys:
        """
        重写load_state_dict以加载类别权重
        
        Args:
            state_dict: 包含参数的字典
            strict: 是否严格匹配键
            
        Returns:
            包含缺失和意外键的命名元组
        """
        # 创建状态字典的副本，以避免修改原始字典
        local_state_dict = state_dict.copy()
        
        # 处理额外状态
        if 'class_weights' in local_state_dict:
            self.weights = local_state_dict.pop('class_weights')
        if 'last_update_epoch' in local_state_dict:
            self.last_update_epoch = local_state_dict.pop('last_update_epoch')
            
        return super().load_state_dict(local_state_dict, strict)
