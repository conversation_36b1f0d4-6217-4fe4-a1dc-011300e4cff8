#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
配置驱动的超参数优化工具
完全无需注册器，通过配置文件定义搜索空间
"""

import ray.tune as tune
from omegaconf import DictConfig, OmegaConf
from typing import Dict, Any, Union, List
import logging


class ConfigDrivenSearchSpaceBuilder:
    """配置驱动的搜索空间构建器"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def build_search_space(self, search_config: DictConfig) -> Dict[str, Any]:
        """
        从配置构建Ray Tune搜索空间
        
        Args:
            search_config: 搜索空间配置
            
        Returns:
            Ray Tune搜索空间字典
        """
        search_space = {}
        
        for key, config in search_config.items():
            if key.endswith('_search'):
                # 移除_search后缀作为参数名
                param_name = key.replace('_search', '')
                search_space[param_name] = self._parse_search_config(config)
        
        return search_space
    
    def _parse_search_config(self, config: Union[DictConfig, Dict]) -> Any:
        """解析单个搜索配置"""
        
        if isinstance(config, dict) and 'type' in config:
            search_type = config['type']
            
            if search_type == 'choice':
                choices = config['choices']
                # 递归处理choices中的嵌套配置
                processed_choices = []
                for choice in choices:
                    if isinstance(choice, (dict, DictConfig)):
                        processed_choices.append(self._process_nested_config(choice))
                    else:
                        processed_choices.append(choice)
                return tune.choice(processed_choices)
            
            elif search_type == 'uniform':
                return tune.uniform(config['lower'], config['upper'])
            
            elif search_type == 'loguniform':
                return tune.loguniform(config['lower'], config['upper'])
            
            elif search_type == 'normal':
                return tune.normal(config['mean'], config['std'])
            
            elif search_type == 'lognormal':
                return tune.lognormal(config['mean'], config['std'])
            
            elif search_type == 'randint':
                return tune.randint(config['lower'], config['upper'])
            
            elif search_type == 'quniform':
                return tune.quniform(config['lower'], config['upper'], config['q'])
            
            elif search_type == 'qloguniform':
                return tune.qloguniform(config['lower'], config['upper'], config['q'])
            
            else:
                raise ValueError(f"未知的搜索类型: {search_type}")
        
        elif isinstance(config, (dict, DictConfig)):
            # 处理嵌套配置
            return self._process_nested_config(config)
        
        else:
            # 固定值
            return config
    
    def _process_nested_config(self, config: Union[Dict, DictConfig]) -> Dict[str, Any]:
        """处理嵌套配置"""
        processed = {}
        
        for key, value in config.items():
            if isinstance(value, (dict, DictConfig)) and 'type' in value:
                # 这是一个搜索配置
                processed[key] = self._parse_search_config(value)
            elif isinstance(value, (dict, DictConfig)):
                # 递归处理嵌套字典
                processed[key] = self._process_nested_config(value)
            else:
                # 固定值
                processed[key] = value
        
        return processed


class ConfigDrivenHPORunner:
    """配置驱动的HPO运行器"""
    
    def __init__(self, cfg: DictConfig):
        self.cfg = cfg
        self.search_builder = ConfigDrivenSearchSpaceBuilder()
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def create_search_space(self) -> Dict[str, Any]:
        """创建搜索空间"""
        if 'search_spaces' not in self.cfg:
            raise ValueError("配置中缺少search_spaces定义")
        
        return self.search_builder.build_search_space(self.cfg.search_spaces)
    
    def create_scheduler(self):
        """创建Ray Tune调度器"""
        scheduler_config = self.cfg.hpo_config.scheduler
        
        if scheduler_config.type == 'asha':
            from ray.tune.schedulers import ASHAScheduler
            return ASHAScheduler(
                metric=scheduler_config.metric,
                mode=scheduler_config.mode,
                max_t=scheduler_config.max_t,
                grace_period=scheduler_config.grace_period,
                reduction_factor=scheduler_config.reduction_factor
            )
        
        elif scheduler_config.type == 'hyperband':
            from ray.tune.schedulers import HyperBandScheduler
            return HyperBandScheduler(
                metric=scheduler_config.metric,
                mode=scheduler_config.mode,
                max_t=scheduler_config.max_t
            )
        
        elif scheduler_config.type == 'median_stopping':
            from ray.tune.schedulers import MedianStoppingRule
            return MedianStoppingRule(
                metric=scheduler_config.metric,
                mode=scheduler_config.mode
            )
        
        else:
            raise ValueError(f"未知的调度器类型: {scheduler_config.type}")
    
    def create_search_algorithm(self):
        """创建搜索算法"""
        if 'search_algorithm' not in self.cfg.hpo_config:
            return None
        
        algo_config = self.cfg.hpo_config.search_algorithm
        
        if algo_config.type == 'optuna':
            from ray.tune.search.optuna import OptunaSearch
            return OptunaSearch()
        
        elif algo_config.type == 'hyperopt':
            from ray.tune.search.hyperopt import HyperOptSearch
            return HyperOptSearch()
        
        elif algo_config.type == 'bayesopt':
            from ray.tune.search.bayesopt import BayesOptSearch
            return BayesOptSearch()
        
        else:
            return None
    
    def create_stopper(self):
        """创建早停器"""
        if 'stopper' not in self.cfg.hpo_config:
            return None
        
        stopper_config = self.cfg.hpo_config.stopper
        
        if stopper_config.type == 'plateau':
            from ray.tune.stopper import PlateauStopper
            return PlateauStopper(
                metric=stopper_config.metric,
                mode=stopper_config.mode,
                patience=stopper_config.patience,
                min_delta=stopper_config.min_delta
            )
        
        elif stopper_config.type == 'timeout':
            from ray.tune.stopper import TimeoutStopper
            return TimeoutStopper(stopper_config.timeout_s)
        
        else:
            return None
    
    def merge_trial_config(self, trial_config: Dict[str, Any]) -> DictConfig:
        """合并试验配置与基础配置"""
        # 从基础配置开始
        merged_cfg = OmegaConf.merge(self.cfg.base_config, trial_config)
        
        # 添加其他必要的配置
        merged_cfg.num_classes = self.cfg.num_classes
        
        return merged_cfg
    
    def run_hpo(self, train_fn):
        """运行超参数优化"""
        
        # 创建搜索空间
        search_space = self.create_search_space()
        self.logger.info(f"搜索空间: {search_space}")
        
        # 创建调度器
        scheduler = self.create_scheduler()
        
        # 创建搜索算法
        search_alg = self.create_search_algorithm()
        
        # 创建早停器
        stopper = self.create_stopper()
        
        # 包装训练函数
        def wrapped_train_fn(config):
            merged_cfg = self.merge_trial_config(config)
            return train_fn(merged_cfg)
        
        # 创建Tuner
        tuner = tune.Tuner(
            wrapped_train_fn,
            param_space=search_space,
            tune_config=tune.TuneConfig(
                num_samples=self.cfg.hpo_config.num_samples,
                max_concurrent_trials=self.cfg.hpo_config.get('max_concurrent_trials', 1),
                scheduler=scheduler,
                search_alg=search_alg,
                metric=self.cfg.hpo_config.scheduler.metric,
                mode=self.cfg.hpo_config.scheduler.mode
            ),
            run_config=train.RunConfig(
                name=f"{self.cfg.project_name}_config_driven_hpo",
                stop=stopper,
                callbacks=self._create_callbacks()
            )
        )
        
        # 运行优化
        results = tuner.fit()
        
        return results
    
    def _create_callbacks(self):
        """创建回调函数"""
        callbacks = []
        
        # WandB回调
        if 'wandb_config' in self.cfg:
            from ray.tune.integration.wandb import WandbLoggerCallback
            callbacks.append(
                WandbLoggerCallback(
                    project=self.cfg.wandb_config.project,
                    entity=self.cfg.wandb_config.get('entity'),
                    group=self.cfg.wandb_config.get('group'),
                    job_type=self.cfg.wandb_config.get('job_type'),
                    tags=list(self.cfg.wandb_config.get('tags', [])),
                    log_config=self.cfg.wandb_config.get('log_config', True)
                )
            )
        
        return callbacks


# 使用示例
def example_usage():
    """使用示例"""
    
    # 加载配置
    cfg = OmegaConf.load("configs/hpo_advanced.yaml")
    
    # 创建HPO运行器
    hpo_runner = ConfigDrivenHPORunner(cfg)
    
    # 定义训练函数
    def train_fn(merged_cfg):
        # 这里是你的训练逻辑
        # merged_cfg 包含了基础配置和试验配置的合并结果
        
        # 示例：使用统一的控制台管理器
        from .loguru_console_manager import get_console_manager
        console_manager = get_console_manager()

        console_manager.print_info(f"模型: {merged_cfg.model._target_}", "🤖")
        console_manager.print_info(f"优化器: {merged_cfg.optimizer._target_}", "⚙️")
        console_manager.print_info(f"学习率: {merged_cfg.optimizer.lr}", "📈")
        
        # 返回指标
        return {"val_loss": 0.5, "val_accuracy": 0.8}
    
    # 运行HPO
    results = hpo_runner.run_hpo(train_fn)
    
    # 获取最佳结果
    from .loguru_console_manager import get_console_manager
    console_manager = get_console_manager()

    best_result = results.get_best_result("val_loss", "min")
    console_manager.print_success(f"最佳配置: {best_result.config}", "🏆")
    console_manager.print_success(f"最佳指标: {best_result.metrics}", "📊")


if __name__ == "__main__":
    example_usage()
