{"defaults": "{'project_name': 'SuiDe_RemoteSensing', 'experiment_name': 'baseline', 'run_name': 'baseline_2025-07-26_14-57-33', 'wandb': {'project': 'SuiDe_RemoteSensing', 'name': 'baseline_2025-07-26_14-57-33', 'tags': ['baseline', 'unet'], 'notes': 'A baseline run for segmentation.'}, 'logger': {'project': 'SuiDe_RemoteSensing', 'tags': ['hpo', 'raytune', 'unet'], 'notes': 'Ray Tune hyperparameter optimization'}, 'trainer': {'_target_': 'lightning.pytorch.Trainer', 'accelerator': 'auto', 'devices': 'auto', 'precision': '16-mixed', 'max_epochs': 5, 'check_val_every_n_epoch': 1, 'log_every_n_steps': 10, 'enable_progress_bar': True}, 'model': {'_target_': 'src.modules.segmentation_module.SegmentationModule', 'optimizer_cfg': {'_target_': 'torch.optim.AdamW', 'lr': 0.0001, 'weight_decay': 0.0001, 'betas': [0.9, 0.999], 'eps': 1e-08}, 'scheduler_cfg': {'_target_': 'torch.optim.lr_scheduler.CosineAnnealingLR', 'T_max': 50, 'eta_min': 1e-06}, 'loss_cfg': {'_target_': 'src.losses.dice_loss.CEDiceLoss', 'ce_weight': 0.5, 'dice_weight': 0.5, 'class_weights': None, 'name': 'baseline_2025-07-26_14-57-33', 'save_dir': '/home/<USER>/DeepLearing/SuiDe_Project/DL_lightning/outputs/2025-07-26/14-57-33', 'log_model': False, 'tags': ['baseline', 'unet'], 'notes': 'A baseline run for segmentation.'}, 'num_classes': 14, 'architecture': {'_target_': 'src.models.architectures.unet.UNet', 'n_channels': 3, 'n_classes': 14, 'bilinear': True}}, 'data': {'_target_': 'src.data.suide_datamodule.SuiDeDataModule', 'dataset_config': {'data_root': '/home/<USER>/DeepLearing/SuiDe_Project/Dataset_v2.2', 'training_level': 2, 'num_classes': 14, 'ignore_index': 255, 'auto_detect_classes': False, 'training_scales': {'1:1': 0.7, '1:2': 0.2, '1:0.5': 0.1}, 'validation_scales': ['1:1'], 'test_scales': ['1:1'], 'augmentation': {'enabled': True, 'horizontal_flip': 0.5, 'vertical_flip': 0.5, 'rotation': 0.3, 'brightness': 0.2, 'contrast': 0.2, 'saturation': 0.2, 'hue': 0.1}}, 'dataloader_config': {'batch_size': 16, 'val_batch_size': 32, 'test_batch_size': 32, 'num_workers': 4, 'pin_memory': True, 'persistent_workers': True, 'prefetch_factor': 2}}, 'optimizer': {'_target_': 'torch.optim.AdamW', 'lr': 0.0001, 'weight_decay': 0.0001, 'betas': [0.9, 0.999], 'eps': 1e-08}, 'scheduler': {'_target_': 'torch.optim.lr_scheduler.CosineAnnealingLR', 'T_max': 50, 'eta_min': 1e-06}, 'loss': {'_target_': 'src.losses.dice_loss.CEDiceLoss', 'ce_weight': 0.5, 'dice_weight': 0.5, 'class_weights': None, 'name': 'baseline_2025-07-26_14-57-33', 'save_dir': '/home/<USER>/DeepLearing/SuiDe_Project/DL_lightning/outputs/2025-07-26/14-57-33', 'log_model': False, 'tags': ['baseline', 'unet'], 'notes': 'A baseline run for segmentation.'}, 'callbacks': {'model_checkpoint': {'_target_': 'lightning.pytorch.callbacks.ModelCheckpoint', 'dirpath': '/home/<USER>/DeepLearing/SuiDe_Project/DL_lightning/outputs/2025-07-26/14-57-33/checkpoints', 'filename': 'epoch_{epoch:03d}-iou_{val/iou:.4f}', 'monitor': 'val/iou', 'mode': 'max', 'save_top_k': 1, 'auto_insert_metric_name': False, 'save_last': True}, 'learning_rate_monitor': {'_target_': 'lightning.pytorch.callbacks.LearningRateMonitor', 'logging_interval': 'step'}, 'loguru_model_summary': {'_target_': 'src.callbacks.loguru_progress_callback.LoguruModelSummaryCallback'}}, 'hpo': {'experiment_name': 'unet_baseline_hpo', 'run_params': {'name': 'unet_hpo_basic', 'num_samples': 1, 'local_dir': '/home/<USER>/DeepLearing/SuiDe_Project/DL_lightning/experiments_output/ray_results'}, 'ray_init_args': {'_target_': 'ray.init', 'num_cpus': 4, 'num_gpus': 1}, 'resources_per_trial': {'use_gpu': True, 'CPU': 1, 'GPU': 1}, 'scheduler': {'_target_': 'ray.tune.schedulers.ASHAScheduler', 'metric': 'val_iou', 'mode': 'max', 'max_t': 5, 'grace_period': 1, 'reduction_factor': 2}, 'search_space': {'lr': {'min': 1e-05, 'max': 0.01}, 'batch_size': [16, 32]}}}", "hyperparams": {"batch_size": 16, "lr": 1.7499544398202007e-05}}