Failure # 1 (occurred at 2025-07-26_15-03-19)
[36mray::ImplicitFunc.train()[39m (pid=1874922, ip=**************, actor_id=b1cfac0ef02d4eda59f7dd6501000000, repr=tune_function)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: AdamW.__init__() missing 1 required positional argument: 'params'

The above exception was the direct cause of the following exception:

[36mray::ImplicitFunc.train()[39m (pid=1874922, ip=**************, actor_id=b1cfac0ef02d4eda59f7dd6501000000, repr=tune_function)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/SuiDe/lib/python3.12/site-packages/ray/tune/trainable/trainable.py", line 330, in train
    raise skipped from exception_cause(skipped)
  File "/home/<USER>/anaconda3/envs/SuiDe/lib/python3.12/site-packages/ray/air/_internal/util.py", line 107, in run
    self._ret = self._target(*self._args, **self._kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/SuiDe/lib/python3.12/site-packages/ray/tune/trainable/function_trainable.py", line 45, in <lambda>
    training_func=lambda: self._trainable_func(self.config),
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/SuiDe/lib/python3.12/site-packages/ray/tune/trainable/function_trainable.py", line 261, in _trainable_func
    output = fn()
             ^^^^
  File "/home/<USER>/DeepLearing/SuiDe_Project/DL_lightning/scripts/tune.py", line 31, in tune_function
    base_cfg = hydra.utils.instantiate(config["defaults"])
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/SuiDe/lib/python3.12/site-packages/hydra/_internal/instantiate/_instantiate2.py", line 226, in instantiate
    return instantiate_node(
           ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/SuiDe/lib/python3.12/site-packages/hydra/_internal/instantiate/_instantiate2.py", line 366, in instantiate_node
    cfg[key] = instantiate_node(
               ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/SuiDe/lib/python3.12/site-packages/hydra/_internal/instantiate/_instantiate2.py", line 342, in instantiate_node
    value = instantiate_node(
            ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/SuiDe/lib/python3.12/site-packages/hydra/_internal/instantiate/_instantiate2.py", line 347, in instantiate_node
    return _call_target(_target_, partial, args, kwargs, full_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/anaconda3/envs/SuiDe/lib/python3.12/site-packages/hydra/_internal/instantiate/_instantiate2.py", line 97, in _call_target
    raise InstantiationException(msg) from e
hydra.errors.InstantiationException: Error in call to target 'torch.optim.adamw.AdamW':
TypeError("AdamW.__init__() missing 1 required positional argument: 'params'")
full_key: model.optimizer_cfg
