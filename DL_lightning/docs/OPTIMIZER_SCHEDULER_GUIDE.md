# 🔧 优化器和学习率调度器使用指南

## 📋 概述

DL_lightning项目提供了灵活且强大的优化器和学习率调度器管理系统，支持：

- **标准PyTorch组件**: 直接使用PyTorch原生优化器和调度器
- **自定义实现**: 针对遥感分割任务的专用优化器和调度器
- **工厂函数**: 统一的创建接口，支持多种配置方式
- **预设组合**: 常用的优化器+调度器组合配置
- **超参数搜索**: 与Ray Tune集成的自动搜索功能

## 🚀 快速开始

### 基础使用

```bash
# 使用默认配置（AdamW + 余弦退火）
python scripts/train.py

# 指定优化器和调度器
python scripts/train.py optimizer=sgd scheduler=step

# 使用预设组合
python scripts/train.py optimization=remote_sensing_preset
```

### 参数覆盖

```bash
# 覆盖学习率
python scripts/train.py optimizer.lr=0.01

# 覆盖调度器参数
python scripts/train.py scheduler.T_max=200

# 同时覆盖多个参数
python scripts/train.py optimizer.lr=0.01 optimizer.weight_decay=0.05 scheduler.T_max=150
```

## 🔧 可用组件

### 优化器

| 名称 | 类型 | 描述 |
|------|------|------|
| `adamw` | 标准 | PyTorch AdamW优化器 |
| `sgd` | 标准 | PyTorch SGD优化器 |
| `adam` | 标准 | PyTorch Adam优化器 |
| `rmsprop` | 标准 | PyTorch RMSprop优化器 |
| `adagrad` | 标准 | PyTorch Adagrad优化器 |
| `remote_sensing_adamw` | 自定义 | 遥感专用AdamW（自适应梯度裁剪） |

### 调度器

| 名称 | 类型 | 描述 |
|------|------|------|
| `cosine` | 标准 | 余弦退火调度器 |
| `step` | 标准 | 阶梯式学习率衰减 |
| `poly` | 标准 | 多项式学习率衰减 |
| `exponential` | 标准 | 指数衰减调度器 |
| `plateau` | 标准 | 基于验证损失的调度器 |
| `cosine_warm_restarts` | 标准 | 带重启的余弦退火 |
| `multiscale` | 自定义 | 多尺度训练调度器 |
| `adaptive_cosine` | 自定义 | 自适应余弦退火调度器 |

## 📦 预设组合

### 1. 遥感分割专用预设
```bash
python scripts/train.py optimization=remote_sensing_preset
```
- **优化器**: 遥感专用AdamW（自适应梯度裁剪和权重衰减）
- **调度器**: 多尺度调度器（根据图像尺度动态调整学习率）
- **适用**: 遥感图像分割任务，多尺度训练

### 2. 快速训练预设
```bash
python scripts/train.py optimization=fast_training_preset
```
- **优化器**: AdamW（较高学习率）
- **调度器**: 余弦退火（快速收敛）
- **适用**: 快速实验，原型开发

### 3. 稳定训练预设
```bash
python scripts/train.py optimization=stable_training_preset
```
- **优化器**: AdamW（保守参数）
- **调度器**: 带重启的余弦退火
- **适用**: 生产环境，长时间稳定训练

### 4. 自适应训练预设
```bash
python scripts/train.py optimization=adaptive_preset
```
- **优化器**: AdamW
- **调度器**: 自适应余弦调度器（根据验证损失动态调整）
- **适用**: 不确定最优超参数的场景

## 🏭 工厂函数使用

### Python代码中使用

```python
from src.optimizers.factory import create_optimizer
from src.schedulers.factory import create_scheduler

# 创建优化器
optimizer = create_optimizer('adamw', model.parameters(), lr=1e-3)

# 创建调度器
scheduler = create_scheduler('cosine', optimizer, T_max=100)

# 使用配置字典
optimizer_config = {
    '_target_': 'torch.optim.AdamW',
    'lr': 1e-3,
    'weight_decay': 1e-2
}
optimizer = create_optimizer(optimizer_config, model.parameters())
```

### 预热调度器

```python
from src.schedulers.factory import create_scheduler_with_warmup

# 创建带预热的调度器
scheduler = create_scheduler_with_warmup(
    'cosine', optimizer, 
    warmup_epochs=5,
    warmup_start_lr=1e-6,
    T_max=100
)
```

## 🔍 超参数搜索

### 使用Ray Tune搜索

```bash
# 使用预定义的搜索空间
python scripts/tune.py --config-path configs/hpo --config-name optimizer_scheduler_search

# 自定义搜索参数
python scripts/tune.py hpo.num_samples=50 hpo.max_concurrent_trials=8
```

### 搜索空间定义

```python
from src.optimizers.search_spaces import get_optimizer_search_space
from src.schedulers.search_spaces import get_scheduler_search_space

# 获取优化器搜索空间
adamw_space = get_optimizer_search_space('adamw')

# 获取调度器搜索空间
cosine_space = get_scheduler_search_space('cosine')
```

## 🛠️ 自定义扩展

### 添加自定义优化器

1. **实现优化器类**:
```python
# src/optimizers/my_optimizer.py
import torch
from torch.optim.optimizer import Optimizer

class MyOptimizer(Optimizer):
    def __init__(self, params, lr=1e-3, **kwargs):
        defaults = dict(lr=lr, **kwargs)
        super().__init__(params, defaults)
    
    def step(self, closure=None):
        # 实现优化步骤
        pass
```

2. **注册优化器**:
```python
# src/optimizers/__init__.py
from .my_optimizer import MyOptimizer

AVAILABLE_OPTIMIZERS['my_optimizer'] = MyOptimizer
```

3. **创建配置文件**:
```yaml
# configs/optimizer/my_optimizer.yaml
_target_: src.optimizers.my_optimizer.MyOptimizer
lr: 1e-3
```

### 添加自定义调度器

类似的步骤，继承`torch.optim.lr_scheduler._LRScheduler`基类。

## 🧪 示例配置激活

```bash
# 查看可用示例
python scripts/activate_examples.py --action list

# 激活所有示例
python scripts/activate_examples.py --action activate --type all

# 激活特定示例
python scripts/activate_examples.py --action activate --config remote_sensing_adamw

# 停用示例
python scripts/activate_examples.py --action deactivate --config remote_sensing_adamw
```

## 📚 最佳实践

### 1. 选择合适的优化器
- **AdamW**: 大多数情况下的首选
- **SGD**: 需要更好泛化性能时
- **RemoteSensingAdamW**: 遥感分割任务专用

### 2. 选择合适的调度器
- **CosineAnnealing**: 平滑的学习率衰减
- **StepLR**: 简单的阶梯式衰减
- **MultiScale**: 多尺度训练场景
- **AdaptiveCosine**: 需要自动调整时

### 3. 参数调优建议
- **学习率**: 从1e-3开始，根据收敛情况调整
- **权重衰减**: 通常在1e-4到1e-2之间
- **调度器参数**: 根据总训练轮数设置

### 4. 监控和调试
- 使用WandB监控学习率变化
- 观察训练损失和验证指标
- 注意梯度范数和参数更新幅度

## 🔧 故障排除

### 常见问题

1. **优化器创建失败**
   - 检查配置文件语法
   - 确认参数名称正确
   - 查看错误日志

2. **学习率过高/过低**
   - 观察损失变化趋势
   - 使用学习率搜索功能
   - 参考类似任务的设置

3. **调度器不生效**
   - 确认调度器配置正确
   - 检查Lightning集成设置
   - 验证监控指标名称

### 调试命令

```bash
# 测试优化器和调度器
python scripts/test_optimizer_scheduler.py

# 查看可用组件
python -c "from src.optimizers import AVAILABLE_OPTIMIZERS; print(AVAILABLE_OPTIMIZERS)"
python -c "from src.schedulers import AVAILABLE_SCHEDULERS; print(AVAILABLE_SCHEDULERS)"
```

---

更多详细信息请参考：
- [优化器README](../src/optimizers/README.md)
- [调度器README](../src/schedulers/README.md)
- [项目完整文档](../README.md)
