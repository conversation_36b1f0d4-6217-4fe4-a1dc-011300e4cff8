# 环境变量配置指南

## 📋 概述

本项目支持通过环境变量来配置数据路径，提高配置的灵活性和可移植性。

## 🔧 支持的环境变量

### 数据路径配置

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `SUIDE_DATA_DIR` | `../Data_SRC/Dataset_v2.1` | SuiDe数据集根目录 |
| `LOVEDA_DATA_DIR` | `../data/LoveDA` | LoveDA数据集根目录 |

## 🚀 使用方法

### 方式1: 临时设置（当前会话）

```bash
# 设置SuiDe数据路径
export SUIDE_DATA_DIR="/path/to/your/suide/data"

# 设置LoveDA数据路径
export LOVEDA_DATA_DIR="/path/to/your/loveda/data"

# 运行训练
python train.py
```

### 方式2: 永久设置（添加到shell配置）

```bash
# 编辑 ~/.bashrc 或 ~/.zshrc
echo 'export SUIDE_DATA_DIR="/path/to/your/suide/data"' >> ~/.bashrc
echo 'export LOVEDA_DATA_DIR="/path/to/your/loveda/data"' >> ~/.bashrc

# 重新加载配置
source ~/.bashrc
```

### 方式3: 使用.env文件

创建项目根目录下的`.env`文件：

```bash
# .env
SUIDE_DATA_DIR=/path/to/your/suide/data
LOVEDA_DATA_DIR=/path/to/your/loveda/data
```

然后在Python代码中加载：

```python
from dotenv import load_dotenv
load_dotenv()
```

### 方式4: Docker环境

```dockerfile
# Dockerfile
ENV SUIDE_DATA_DIR=/data/suide
ENV LOVEDA_DATA_DIR=/data/loveda
```

或在docker-compose.yml中：

```yaml
# docker-compose.yml
services:
  training:
    environment:
      - SUIDE_DATA_DIR=/data/suide
      - LOVEDA_DATA_DIR=/data/loveda
```

## 📁 数据目录结构要求

### SuiDe数据集结构

```
${SUIDE_DATA_DIR}/
├── annotations/
│   ├── train.json
│   ├── val.json
│   └── test.json
├── images/
│   ├── scale_1_0.5/
│   ├── scale_1_1/
│   └── scale_1_2/
├── masks/
│   ├── scale_1_0.5/
│   ├── scale_1_1/
│   └── scale_1_2/
└── metadata/
    └── class_info.json
```

### LoveDA数据集结构

```
${LOVEDA_DATA_DIR}/
├── Train/
│   ├── Rural/
│   │   ├── images_png/
│   │   └── masks_png/
│   └── Urban/
│       ├── images_png/
│       └── masks_png/
├── Val/
│   ├── Rural/
│   │   ├── images_png/
│   │   └── masks_png/
│   └── Urban/
│       ├── images_png/
│       └── masks_png/
└── Test/
    ├── Rural/
    │   ├── images_png/
    │   └── masks_png/
    └── Urban/
        ├── images_png/
        └── masks_png/
```

## ✅ 验证配置

### 检查环境变量

```bash
# 检查当前设置的环境变量
echo "SUIDE_DATA_DIR: $SUIDE_DATA_DIR"
echo "LOVEDA_DATA_DIR: $LOVEDA_DATA_DIR"
```

### 验证数据路径

```python
import os
from pathlib import Path

# 检查SuiDe数据路径
suide_path = Path(os.getenv('SUIDE_DATA_DIR', '../Data_SRC/Dataset_v2.1'))
print(f"SuiDe数据路径: {suide_path}")
print(f"路径存在: {suide_path.exists()}")

# 检查LoveDA数据路径
loveda_path = Path(os.getenv('LOVEDA_DATA_DIR', '../data/LoveDA'))
print(f"LoveDA数据路径: {loveda_path}")
print(f"路径存在: {loveda_path.exists()}")
```

### 运行测试验证

```bash
# 运行数据加载测试
cd tests
python data/test_real_data_validation.py
```

## 🔍 故障排除

### 常见问题

1. **路径不存在**
   ```
   FileNotFoundError: Split directory not found: /path/to/data
   ```
   解决：检查环境变量设置和数据目录结构

2. **权限问题**
   ```
   PermissionError: [Errno 13] Permission denied
   ```
   解决：检查数据目录的读取权限

3. **相对路径问题**
   ```
   数据加载失败
   ```
   解决：使用绝对路径或确保相对路径正确

### 调试技巧

1. **打印实际使用的路径**
   ```python
   from omegaconf import OmegaConf
   config = OmegaConf.load('configs/data/suide_v2.1_new.yaml')
   print(f"实际数据路径: {config.dataset_config.data_dir}")
   ```

2. **检查Hydra配置解析**
   ```python
   import hydra
   from omegaconf import OmegaConf
   
   config = OmegaConf.load('configs/data/suide_v2.1_new.yaml')
   resolved_config = OmegaConf.to_yaml(config, resolve=True)
   print(resolved_config)
   ```

## 📝 最佳实践

1. **使用绝对路径**: 避免相对路径带来的问题
2. **统一环境**: 团队成员使用相同的环境变量名
3. **文档化**: 在README中说明必需的环境变量
4. **默认值**: 为环境变量提供合理的默认值
5. **验证**: 在程序启动时验证数据路径的有效性

## 🔗 相关文档

- [数据模块使用指南](data_usage_guide.md)
- [配置文件参考](config_reference.md)
- [故障排除指南](troubleshooting.md)
