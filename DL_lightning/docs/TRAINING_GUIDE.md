# 🚀 DL_lightning 训练指南

## 📋 目录

- [快速开始](#快速开始)
- [训练模式详解](#训练模式详解)
- [配置系统使用](#配置系统使用)
- [自动化脚本](#自动化脚本)
- [性能优化](#性能优化)
- [问题排查](#问题排查)
- [结果分析](#结果分析)

---

## 🚀 快速开始

### 环境准备

```bash
# 1. 激活环境
conda activate your_env

# 2. 检查GPU状态
nvidia-smi

# 3. 验证环境
python -c "import torch; print(f'PyTorch: {torch.__version__}, CUDA: {torch.cuda.is_available()}')"
```

### 第一次训练

```bash
# 进入项目目录
cd /home/<USER>/DeepLearing/SuiDe_Project/DL_lightning

# 快速验证训练
bash scripts/train_debug.sh

# 基础训练
bash scripts/train_basic.sh
```

---

## 🎯 训练模式详解

### 1. 单次训练模式

#### 🔧 基础训练
```bash
# 使用默认配置
python scripts/train.py

# 指定模型和数据集
python scripts/train.py model=unet data=loveda

# 自定义训练参数
python scripts/train.py \
  trainer.max_epochs=100 \
  optimizer.lr=0.001 \
  data.dataloader_config.batch_size=16
```

#### 🎛️ 实验配置
```bash
# 使用预定义实验配置
python scripts/train.py experiment=baseline_unet

# 组合多个配置
python scripts/train.py \
  model=deeplabv3 \
  data=suide \
  loss=combined \
  optimizer=adamw \
  scheduler=cosine
```

### 2. WandB 模式切换

#### 🌐 在线模式（实时同步）
```bash
# 方法1: 使用配置文件
python scripts/train.py logger=wandb_online

# 方法2: 参数覆盖
python scripts/train.py wandb.mode=online

# 方法3: 环境变量
export WANDB_MODE=online
python scripts/train.py
```

#### 💾 离线模式（本地存储）
```bash
# 离线训练
python scripts/train.py logger=wandb_offline

# 或使用脚本
bash scripts/train_offline.sh

# 后续同步数据
python scripts/sync_wandb.py
```

#### 🚫 禁用模式（调试用）
```bash
# 完全禁用WandB
python scripts/train.py logger=wandb_disabled

# 或
python scripts/train.py wandb.mode=disabled
```

#### 🤖 自动模式（推荐）
```bash
# 智能检测网络状况
python scripts/train.py  # 默认就是auto模式
```

### 3. 超参数优化

#### 🔍 基础HPO
```bash
# 使用默认HPO配置
python scripts/tune.py

# 或使用脚本
bash scripts/train_hpo.sh
```

#### 🎯 自定义HPO
```bash
# 指定搜索空间
python scripts/tune.py \
  hpo.run_params.num_samples=50 \
  hpo.search_space.lr.min=1e-5 \
  hpo.search_space.lr.max=1e-2

# 调整资源分配
python scripts/tune.py \
  hpo.resources_per_trial.GPU=0.5 \
  hpo.resources_per_trial.CPU=2
```

---

## ⚙️ 配置系统使用

### 配置文件组合

#### 🎯 基础组合
```bash
# 模型 + 数据集组合
python scripts/train.py model=unet data=suide
python scripts/train.py model=deeplabv3 data=loveda

# 优化器 + 调度器组合
python scripts/train.py optimizer=adam scheduler=poly
python scripts/train.py optimizer=adamw scheduler=cosine
```

#### 🔧 损失函数组合
```bash
# 单一损失
python scripts/train.py loss=dice
python scripts/train.py loss=focal

# 组合损失
python scripts/train.py loss=combined
```

### 参数覆盖策略

#### 📊 训练器参数
```bash
# GPU设置
python scripts/train.py trainer.devices=2 trainer.accelerator=gpu

# 训练控制
python scripts/train.py \
  trainer.max_epochs=200 \
  trainer.check_val_every_n_epoch=5 \
  trainer.log_every_n_steps=50

# 精度设置
python scripts/train.py trainer.precision=16-mixed
```

#### 🎛️ 数据加载参数
```bash
# 批次大小和工作进程
python scripts/train.py \
  data.dataloader_config.batch_size=32 \
  data.dataloader_config.num_workers=8

# 数据增强
python scripts/train.py \
  data.transform_config.resize_size=512 \
  data.transform_config.crop_size=480
```

#### 🔍 模型参数
```bash
# 模型架构
python scripts/train.py \
  model.backbone=resnet50 \
  model.num_classes=6

# 预训练权重
python scripts/train.py model.pretrained=true
```

---

## 🛠️ 自动化脚本

### 脚本概览

| 脚本 | 用途 | 适用场景 | 状态 |
|------|------|----------|------|
| `train_debug.sh` | 快速验证 | 开发调试 | ✅ 已完成 |
| `train_basic.sh` | 基础训练 | 日常实验 | ✅ 已完成 |
| `train_advanced.sh` | 高级训练 | 生产环境 | ✅ 已完成 |
| `train_hpo.sh` | 超参优化 | 模型调优 | ✅ 已完成 |
| `train_offline.sh` | 离线训练 | 网络受限环境 | ✅ 已完成 |

**所有脚本特性：**
- ✅ 完整的参数解析和帮助系统
- ✅ 环境检查和错误处理
- ✅ 可配置的训练参数
- ✅ 训练完成后的结果总结
- ✅ 已设置可执行权限

### 使用示例

#### 🔧 调试脚本
```bash
# 快速验证环境和配置
bash scripts/train_debug.sh

# 自定义调试参数
bash scripts/train_debug.sh --model unet --data loveda
```

#### 🚀 基础训练
```bash
# 默认配置训练
bash scripts/train_basic.sh

# 指定参数
bash scripts/train_basic.sh \
  --model deeplabv3 \
  --data suide \
  --epochs 100 \
  --lr 0.001
```

#### 🏋️ 高级训练
```bash
# 多GPU长时间训练
bash scripts/train_advanced.sh \
  --gpus 2 \
  --epochs 300 \
  --precision 16 \
  --batch-size 64
```

#### 🔍 超参数优化
```bash
# 启动HPO
bash scripts/train_hpo.sh

# 自定义HPO参数
bash scripts/train_hpo.sh \
  --trials 100 \
  --gpu-per-trial 0.5 \
  --cpu-per-trial 4
```

#### 💾 离线训练
```bash
# 离线环境训练
bash scripts/train_offline.sh

# 训练完成后同步数据
python scripts/sync_wandb.py
```

---

## ⚡ 性能优化

### 硬件优化

#### 🖥️ GPU优化
```bash
# 混合精度训练
python scripts/train.py trainer.precision=16-mixed

# 多GPU训练
python scripts/train.py trainer.devices=2 trainer.strategy=ddp

# GPU内存优化
python scripts/train.py \
  data.dataloader_config.batch_size=16 \
  trainer.accumulate_grad_batches=4
```

#### 💾 内存优化
```bash
# 减少数据加载器工作进程
python scripts/train.py data.dataloader_config.num_workers=4

# 启用内存固定
python scripts/train.py data.dataloader_config.pin_memory=true

# 减少验证频率
python scripts/train.py trainer.check_val_every_n_epoch=10
```

### 数据加载优化

#### 📊 批次大小调优
```bash
# 根据GPU内存调整
# RTX 3090 (24GB): batch_size=32-64
# RTX 4090 (24GB): batch_size=32-64  
# A100 (40GB): batch_size=64-128

python scripts/train.py data.dataloader_config.batch_size=32
```

#### 🔄 数据预处理优化
```bash
# 启用数据预取
python scripts/train.py data.dataloader_config.prefetch_factor=2

# 优化图像尺寸
python scripts/train.py \
  data.transform_config.resize_size=512 \
  data.transform_config.crop_size=480
```

### 训练策略优化

#### 📈 学习率调优
```bash
# 学习率查找
python scripts/train.py \
  trainer.auto_lr_find=true \
  optimizer.lr=auto

# 余弦退火调度
python scripts/train.py scheduler=cosine scheduler.T_max=100

# 多步调度
python scripts/train.py scheduler=multistep scheduler.milestones=[50,80]
```

#### 🎯 早停和检查点
```bash
# 早停配置
python scripts/train.py \
  callbacks.early_stopping.patience=20 \
  callbacks.early_stopping.monitor=val/iou

# 检查点保存
python scripts/train.py \
  callbacks.model_checkpoint.save_top_k=3 \
  callbacks.model_checkpoint.monitor=val/iou
```

---

## 🔍 问题排查

### 常见错误及解决方案

#### ❌ CUDA内存不足
```bash
# 错误信息: RuntimeError: CUDA out of memory

# 解决方案1: 减少批次大小
python scripts/train.py data.dataloader_config.batch_size=8

# 解决方案2: 启用梯度累积
python scripts/train.py \
  data.dataloader_config.batch_size=8 \
  trainer.accumulate_grad_batches=4

# 解决方案3: 使用混合精度
python scripts/train.py trainer.precision=16-mixed
```

#### ❌ 数据加载错误
```bash
# 错误信息: FileNotFoundError 或 数据路径问题

# 检查数据路径
python -c "
import os
from omegaconf import OmegaConf
cfg = OmegaConf.load('configs/data/suide.yaml')
print(f'Data dir: {cfg.dataset_config.data_dir}')
print(f'Exists: {os.path.exists(cfg.dataset_config.data_dir)}')
"

# 设置环境变量
export SUIDE_DATA_DIR=/path/to/your/data
python scripts/train.py
```

#### ❌ WandB连接问题
```bash
# 错误信息: wandb connection failed

# 解决方案1: 切换到离线模式
python scripts/train.py wandb.mode=offline

# 解决方案2: 禁用WandB
python scripts/train.py wandb.mode=disabled

# 解决方案3: 检查网络和API密钥
wandb login
python scripts/sync_wandb.py --status-only
```

#### ❌ 配置文件错误
```bash
# 错误信息: Hydra configuration error

# 检查配置语法
python scripts/train.py --cfg job

# 验证配置文件
python -c "
from omegaconf import OmegaConf
cfg = OmegaConf.load('configs/config.yaml')
print(OmegaConf.to_yaml(cfg))
"
```

### 调试技巧

#### 🔧 快速验证
```bash
# 最小化训练验证
python scripts/train.py \
  trainer.fast_dev_run=true \
  trainer.limit_train_batches=1 \
  trainer.limit_val_batches=1

# 单个epoch验证
python scripts/train.py \
  trainer.max_epochs=1 \
  trainer.limit_train_batches=10
```

#### 📊 性能分析
```bash
# 启用性能分析
python scripts/train.py \
  trainer.profiler=simple

# 详细性能分析
python scripts/train.py \
  trainer.profiler=advanced
```

#### 🔍 日志调试
```bash
# 增加日志详细程度
python scripts/train.py \
  trainer.log_every_n_steps=1 \
  wandb.silent=false

# 保存详细日志
python scripts/train.py > training.log 2>&1
```

---

## 📊 结果分析

### 训练监控

#### 📈 实时监控
```bash
# WandB在线监控
# 访问: https://wandb.ai/your-username/SuiDe_RemoteSensing

# 本地TensorBoard监控
tensorboard --logdir outputs/

# 终端实时监控
watch -n 1 'tail -n 20 training.log'
```

#### 📊 关键指标
- **训练损失**: 应该稳定下降
- **验证IoU**: 主要评估指标，越高越好
- **验证损失**: 应该与训练损失趋势一致
- **学习率**: 观察调度器效果
- **GPU利用率**: 应该保持在80%以上

### 模型评估

#### 🎯 验证指标
```python
# 主要指标说明
val/iou          # 平均交并比 (主要指标)
val/accuracy     # 像素准确率
val/precision    # 精确率
val/recall       # 召回率
val/f1_score     # F1分数
val/loss         # 验证损失
```

#### 📋 结果对比
```bash
# 使用WandB对比不同实验
# 1. 登录WandB dashboard
# 2. 选择项目: SuiDe_RemoteSensing  
# 3. 对比不同runs的指标曲线
# 4. 分析最佳超参数组合
```

### 模型选择

#### 🏆 最佳模型标准
1. **验证IoU最高**: 主要选择标准
2. **训练稳定性**: 损失曲线平滑
3. **收敛速度**: 达到目标性能的epoch数
4. **资源效率**: GPU内存和训练时间

#### 💾 模型保存
```bash
# 检查保存的模型
ls outputs/checkpoints/

# 加载最佳模型进行推理
python scripts/predict.py \
  --checkpoint outputs/checkpoints/best_model.ckpt \
  --input /path/to/test/images \
  --output /path/to/predictions
```

---

## 📚 进阶使用

### 自定义配置

#### 🔧 创建新实验配置
```yaml
# configs/experiment/my_experiment.yaml
# @package _global_

experiment_name: "my_custom_experiment"

model:
  backbone: "resnet101"
  
optimizer:
  lr: 0.0005
  weight_decay: 0.001

trainer:
  max_epochs: 150
  
wandb:
  tags: ["custom", "resnet101", "long_training"]
```

#### 🎯 使用自定义配置
```bash
python scripts/train.py experiment=my_experiment
```

### 生产环境部署

#### 🚀 批量训练
```bash
# 创建批量训练脚本
for model in unet deeplabv3 segformer; do
  for data in suide loveda; do
    echo "Training $model on $data"
    python scripts/train.py \
      model=$model \
      data=$data \
      experiment_name="${model}_${data}" \
      wandb.tags=["batch_training", "$model", "$data"]
  done
done
```

#### 📊 结果汇总
```bash
# 同步所有离线数据
python scripts/sync_wandb.py

# 生成训练报告
python scripts/generate_report.py \
  --project SuiDe_RemoteSensing \
  --output training_report.html
```

---

## 🔗 相关资源

- [项目README](../README.md)
- [WandB模式切换指南](WANDB_MODE_SWITCHING_GUIDE.md)
- [配置系统文档](CONFIG_SYSTEM.md)
- [API参考文档](API_REFERENCE.md)

---

## 💡 最佳实践总结

1. **开发阶段**: 使用 `train_debug.sh` 快速验证
2. **实验对比**: 使用不同的 `experiment` 配置
3. **超参调优**: 使用 `train_hpo.sh` 自动搜索
4. **生产训练**: 使用 `train_advanced.sh` 稳定训练
5. **离线环境**: 使用 `train_offline.sh` + 后续同步
6. **性能监控**: 始终关注GPU利用率和内存使用
7. **结果管理**: 合理使用WandB标签和分组功能

---

*📝 文档版本: v1.0 | 最后更新: 2024-01-15*
