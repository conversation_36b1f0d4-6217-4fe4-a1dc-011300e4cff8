# LoveDA数据集使用指南

本文档展示如何在方案A分层抽象架构中使用新集成的LoveDA数据集。

## 📋 目录

1. [快速开始](#快速开始)
2. [配置文件使用](#配置文件使用)
3. [代码示例](#代码示例)
4. [数据集切换](#数据集切换)
5. [高级配置](#高级配置)
6. [常见问题](#常见问题)

## 🚀 快速开始

### 1. 基本使用

```python
import hydra
from omegaconf import OmegaConf

# 加载LoveDA配置
config = OmegaConf.load('configs/data/loveda.yaml')

# 创建DataModule
datamodule = hydra.utils.instantiate(config)

# 设置数据集
datamodule.setup('fit')

# 获取数据加载器
train_loader = datamodule.train_dataloader()
val_loader = datamodule.val_dataloader()

# 使用数据
for batch in train_loader:
    images = batch['image']  # [B, 3, 1024, 1024]
    masks = batch['mask']    # [B, 1024, 1024]
    break
```

### 2. 直接创建DataModule

```python
from src.data.datamodules import LoveDADataModule
from omegaconf import DictConfig

# 创建配置
dataset_config = DictConfig({
    'data_dir': '/path/to/loveda/data',
    'num_classes': 7,
    'ignore_index': 0,
    'ignore_nodata': True,
    'include_background': True
})

dataloader_config = DictConfig({
    'batch_size': 8,
    'num_workers': 8,
    'pin_memory': True
})

transform_config = DictConfig({
    'image_size': 1024,
    'mean': [0.485, 0.456, 0.406],
    'std': [0.229, 0.224, 0.225]
})

# 创建DataModule
datamodule = LoveDADataModule(
    dataset_config=dataset_config,
    dataloader_config=dataloader_config,
    transform_config=transform_config
)
```

## ⚙️ 配置文件使用

### 1. 默认配置

```yaml
# configs/data/loveda.yaml
_target_: src.data.datamodules.LoveDADataModule

dataset_config:
  data_dir: ${oc.env:LOVEDA_DATA_DIR, ../data/LoveDA}
  num_classes: 7
  ignore_index: 0
  ignore_nodata: true
  include_background: true

dataloader_config:
  batch_size: 8
  num_workers: 8
  pin_memory: true

transform_config:
  image_size: 1024
  mean: [0.485, 0.456, 0.406]
  std: [0.229, 0.224, 0.225]
```

### 2. 自定义配置

```yaml
# configs/data/loveda_custom.yaml
_target_: src.data.datamodules.LoveDADataModule

dataset_config:
  data_dir: /custom/path/to/loveda
  num_classes: 6          # 不包含背景类
  ignore_index: 0
  ignore_nodata: true
  include_background: false  # 关键：不包含背景类

dataloader_config:
  batch_size: 4           # 更小的批次
  val_batch_size: 8       # 验证时使用更大的批次
  num_workers: 4

transform_config:
  image_size: 512         # 更小的图像尺寸
  augmentation:
    random_crop: true
    flip: true
    transpose: true
    cutout: true
    cutout_max_size: 0.03  # 更小的cutout区域
```

## 💻 代码示例

### 1. 训练脚本集成

```python
import lightning.pytorch as pl
import hydra
from omegaconf import DictConfig

@hydra.main(version_base=None, config_path="configs", config_name="train")
def main(cfg: DictConfig):
    # 创建DataModule
    datamodule = hydra.utils.instantiate(cfg.data)
    
    # 创建模型
    model = hydra.utils.instantiate(cfg.model)
    
    # 创建Trainer
    trainer = pl.Trainer(**cfg.trainer)
    
    # 开始训练
    trainer.fit(model, datamodule)

if __name__ == "__main__":
    main()
```

### 2. 数据探索脚本

```python
from src.data.datamodules import LoveDADataModule
from omegaconf import DictConfig
import matplotlib.pyplot as plt

# 创建DataModule
config = {
    'dataset_config': DictConfig({
        'data_dir': '/path/to/loveda',
        'num_classes': 7,
        'ignore_index': 0
    }),
    'dataloader_config': DictConfig({'batch_size': 1, 'num_workers': 0}),
    'transform_config': DictConfig({'image_size': 512})
}

datamodule = LoveDADataModule(**config)
datamodule.setup('fit')

# 获取一个样本
train_loader = datamodule.train_dataloader()
batch = next(iter(train_loader))

image = batch['image'][0]  # [3, 512, 512]
mask = batch['mask'][0]    # [512, 512]

# 可视化
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))

# 显示图像
ax1.imshow(image.permute(1, 2, 0))
ax1.set_title('LoveDA Image')

# 显示掩膜
ax2.imshow(mask, cmap='tab10')
ax2.set_title('LoveDA Mask')

plt.show()
```

## 🔄 数据集切换

### 1. 从SuiDe切换到LoveDA

```python
# 原来的SuiDe配置
# config = OmegaConf.load('configs/data/suide_v2.1_new.yaml')

# 切换到LoveDA
config = OmegaConf.load('configs/data/loveda.yaml')

# 其余代码保持不变
datamodule = hydra.utils.instantiate(config)
```

### 2. 运行时切换

```python
def create_datamodule(dataset_name: str):
    """根据数据集名称创建对应的DataModule"""
    
    if dataset_name == 'suide':
        config = OmegaConf.load('configs/data/suide_v2.1_new.yaml')
    elif dataset_name == 'loveda':
        config = OmegaConf.load('configs/data/loveda.yaml')
    else:
        raise ValueError(f"Unknown dataset: {dataset_name}")
    
    return hydra.utils.instantiate(config)

# 使用
datamodule = create_datamodule('loveda')
```

## 🔧 高级配置

### 1. 向后兼容的参数传递

```python
from src.data.datamodules import LoveDADataModule

# 使用旧式参数传递（会显示弃用警告）
datamodule = LoveDADataModule.from_legacy_config(
    data_dir='/path/to/loveda',
    image_size=1024,
    batch_size=8,
    num_classes=7,
    ignore_index=0,
    ignore_nodata=True,
    include_background=True,
    random_crop=True,
    flip=True,
    transpose=True
)
```

### 2. 配置覆盖

```python
import hydra
from omegaconf import OmegaConf

# 加载基础配置
config = OmegaConf.load('configs/data/loveda.yaml')

# 覆盖特定配置
config.dataloader_config.batch_size = 16
config.transform_config.image_size = 512
config.dataset_config.include_background = False
config.dataset_config.num_classes = 6

# 创建DataModule
datamodule = hydra.utils.instantiate(config)
```

### 3. 多GPU训练配置

```yaml
# configs/data/loveda_multi_gpu.yaml
_target_: src.data.datamodules.LoveDADataModule

dataset_config:
  data_dir: ${oc.env:LOVEDA_DATA_DIR}
  num_classes: 7
  ignore_index: 0

dataloader_config:
  batch_size: 4           # 每个GPU的批次大小
  num_workers: 8          # 每个GPU的工作进程数
  pin_memory: true
  persistent_workers: true

transform_config:
  image_size: 1024
  augmentation:
    random_crop: true
    flip: true
    transpose: true
```

## ❓ 常见问题

### Q1: LoveDA数据集的类别映射是什么？

A: LoveDA数据集有7个类别：
- 0: Background (背景)
- 1: Building (建筑)
- 2: Road (道路)
- 3: Water (水体)
- 4: Barren (裸地)
- 5: Forest (森林)
- 6: Agriculture (农业用地)

注意：原始标签中的nodata区域（值为0）会被忽略。

### Q2: 如何不包含背景类？

A: 设置 `include_background: false` 和 `num_classes: 6`：

```yaml
dataset_config:
  include_background: false
  num_classes: 6
```

### Q3: 如何调整图像尺寸？

A: 修改 `transform_config.image_size`：

```yaml
transform_config:
  image_size: 512  # 或其他尺寸
```

### Q4: 如何优化内存使用？

A: 可以调整以下参数：

```yaml
dataloader_config:
  batch_size: 4        # 减小批次大小
  num_workers: 4       # 减少工作进程
  pin_memory: false    # 关闭内存锁定

transform_config:
  image_size: 512      # 使用更小的图像尺寸
```

### Q5: 如何添加自定义数据增强？

A: 在 `transform_config.augmentation` 中添加：

```yaml
transform_config:
  augmentation:
    random_crop: true
    flip: true
    transpose: true
    random_brightness_contrast: true
    brightness_limit: 0.1
    contrast_limit: 0.1
    cutout: true
    cutout_max_size: 0.05
```

## 📊 性能建议

1. **批次大小**: LoveDA图像较大(1024x1024)，建议使用较小的批次大小(4-8)
2. **工作进程**: 使用8个工作进程可以获得较好的I/O性能
3. **内存优化**: 如果内存不足，可以减小图像尺寸到512x512
4. **数据增强**: 适度使用数据增强，避免过度变换影响遥感图像的真实性

## 🔗 相关链接

- [SuiDe数据集使用指南](suide_usage_examples.md)
- [方案A架构文档](architecture_guide.md)
- [配置文件参考](config_reference.md)
