# 🚀 优化器和调度器完整指南

本指南提供了项目中所有可用的优化器和调度器配置，以及在语义分割任务中的最佳实践。

## 📋 目录

- [可用优化器](#可用优化器)
- [可用调度器](#可用调度器)
- [语义分割最佳实践](#语义分割最佳实践)
- [使用方法](#使用方法)
- [性能对比](#性能对比)

## 🔧 可用优化器

### 1. AdamW (推荐 ⭐⭐⭐⭐⭐)
```yaml
# configs/optimizer/adamw.yaml
_target_: torch.optim.AdamW
lr: 1e-4
weight_decay: 1e-2
betas: [0.9, 0.999]
```

**适用场景：**
- ✅ Transformer架构 (Swin-UNet, SegFormer)
- ✅ 现代CNN + 注意力机制
- ✅ 大模型训练
- ✅ 需要强正则化的任务

**优势：** 解耦权重衰减，收敛稳定，现代架构首选

### 2. SGD (经典选择 ⭐⭐⭐⭐)
```yaml
# configs/optimizer/sgd.yaml
_target_: torch.optim.SGD
lr: 1e-2
momentum: 0.9
nesterov: true
```

**适用场景：**
- ✅ 传统CNN架构 (ResNet, DeepLabV3+)
- ✅ 需要稳定训练的场景
- ✅ 计算资源有限的情况

**优势：** 稳定可靠，内存占用小，经过充分验证

### 3. Adam (通用选择 ⭐⭐⭐)
```yaml
# configs/optimizer/adam.yaml
_target_: torch.optim.Adam
lr: 0.001
betas: [0.9, 0.999]
weight_decay: 0.0001
```

**适用场景：**
- ✅ 快速原型验证
- ✅ 初期实验
- ✅ 通用深度学习任务

### 4. RMSprop (特殊场景 ⭐⭐)
```yaml
# configs/optimizer/rmsprop.yaml
_target_: torch.optim.RMSprop
lr: 0.001
alpha: 0.99
```

**适用场景：**
- ✅ 梯度不稳定的场景
- ✅ 在线学习
- ✅ 循环神经网络

### 5. Adamax (稀疏数据 ⭐⭐)
```yaml
# configs/optimizer/adamax.yaml
_target_: torch.optim.Adamax
lr: 0.002
betas: [0.9, 0.999]
```

**适用场景：**
- ✅ 稀疏梯度
- ✅ 大规模数据
- ✅ 对学习率不敏感的场景

## 📈 可用调度器

### 1. Cosine Annealing (现代首选 ⭐⭐⭐⭐⭐)
```yaml
# configs/scheduler/cosine.yaml
_target_: torch.optim.lr_scheduler.CosineAnnealingLR
T_max: 100
eta_min: 1e-6
```

**特点：** 平滑下降，避免震荡，现代深度学习首选

### 2. Polynomial (分割经典 ⭐⭐⭐⭐)
```yaml
# configs/scheduler/polynomial.yaml
_target_: torch.optim.lr_scheduler.PolynomialLR
total_iters: 100
power: 0.9
```

**特点：** 语义分割任务的经典选择，DeepLab系列标配

### 3. Warmup + Cosine (大模型专用 ⭐⭐⭐⭐⭐)
```yaml
# configs/scheduler/warmup_cosine.yaml
_target_: src.schedulers.warmup_scheduler.WarmupCosineAnnealingLR
warmup_epochs: 10
max_epochs: 100
```

**特点：** 结合warmup和cosine的优势，适合Transformer

### 4. Reduce on Plateau (自适应 ⭐⭐⭐)
```yaml
# configs/scheduler/reduce_on_plateau.yaml
_target_: torch.optim.lr_scheduler.ReduceLROnPlateau
mode: 'min'
patience: 10
factor: 0.5
```

**特点：** 根据验证指标自动调整，适合长时间训练

## 🎯 语义分割最佳实践

### Transformer架构组合
```bash
# Swin-UNet, SegFormer等
python train.py \
  model=swin_unet \
  optimizer=adamw \
  scheduler=warmup_cosine \
  optimizer.lr=1e-4 \
  optimizer.weight_decay=0.05 \
  scheduler.warmup_epochs=10
```

### CNN架构组合
```bash
# DeepLabV3+, UNet等
python train.py \
  model=deeplabv3 \
  optimizer=sgd \
  scheduler=polynomial \
  optimizer.lr=0.01 \
  optimizer.momentum=0.9 \
  scheduler.power=0.9
```

### 混合架构组合
```bash
# EfficientNet-UNet等
python train.py \
  model=efficientnet_unet \
  optimizer=adamw \
  scheduler=cosine \
  optimizer.lr=5e-4 \
  optimizer.weight_decay=0.01
```

## 📊 数据集特定调整

### 遥感数据 (如SuiDe项目)
```bash
# 需要更小的学习率和更强的正则化
python train.py \
  data=suide \
  optimizer=adamw \
  optimizer.lr=5e-5 \
  optimizer.weight_decay=0.02 \
  scheduler=warmup_cosine \
  scheduler.warmup_epochs=20
```

### 医学图像
```bash
# 需要更保守的设置
python train.py \
  optimizer=adamw \
  optimizer.lr=3e-5 \
  optimizer.weight_decay=0.015 \
  scheduler=cosine
```

### 自然场景
```bash
# 标准设置即可
python train.py \
  optimizer=adamw \
  optimizer.lr=1e-4 \
  scheduler=cosine
```

## 🚀 使用方法

### 基本使用
```bash
# 使用默认配置
python train.py

# 指定优化器和调度器
python train.py optimizer=adamw scheduler=cosine

# 覆盖参数
python train.py optimizer=sgd optimizer.lr=0.02
```

### 实验配置
```bash
# 使用预定义的实验配置
python train.py experiment=transformer_segmentation

# 多参数搜索
python train.py -m optimizer=adamw,sgd scheduler=cosine,polynomial
```

### 高级用法
```bash
# 自定义warmup
python train.py \
  scheduler=warmup_cosine \
  scheduler.warmup_epochs=15 \
  scheduler.max_epochs=${trainer.max_epochs}

# 动态学习率
python train.py \
  scheduler=reduce_on_plateau \
  scheduler.monitor=val_miou \
  scheduler.mode=max
```

## 📈 性能对比

| 架构类型 | 推荐优化器 | 推荐调度器 | 收敛速度 | 最终性能 | 稳定性 |
|---------|-----------|-----------|---------|---------|--------|
| Transformer | AdamW | Warmup+Cosine | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| CNN | SGD | Polynomial | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 混合架构 | AdamW | Cosine | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| 轻量级 | Adam | Step | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ |

## 🔍 故障排除

### 常见问题

1. **学习率过大导致训练不稳定**
   ```bash
   # 解决方案：降低学习率或使用warmup
   python train.py optimizer.lr=1e-5 scheduler=warmup_cosine
   ```

2. **收敛过慢**
   ```bash
   # 解决方案：增加学习率或改用Adam系列
   python train.py optimizer=adamw optimizer.lr=5e-4
   ```

3. **过拟合严重**
   ```bash
   # 解决方案：增加权重衰减
   python train.py optimizer.weight_decay=0.05
   ```

4. **验证集性能停滞**
   ```bash
   # 解决方案：使用自适应调度器
   python train.py scheduler=reduce_on_plateau
   ```

## 📚 参考资料

- [PyTorch优化器文档](https://pytorch.org/docs/stable/optim.html)
- [Lightning调度器指南](https://pytorch-lightning.readthedocs.io/en/stable/common/optimizers.html)
- [语义分割最佳实践论文](https://paperswithcode.com/task/semantic-segmentation)

---

💡 **提示：** 建议从推荐的组合开始，然后根据具体任务和数据集特点进行微调。
