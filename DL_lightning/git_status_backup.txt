位于分支 feature/remove-factory-pattern
尚未暂存以备提交的变更：
  （使用 "git add/rm <文件>..." 更新要提交的内容）
  （使用 "git restore <文件>..." 丢弃工作区的改动）
	删除：     PROJECT_COMPLETION_REPORT.md
	删除：     PROJECT_MIGRATION_PLAN.md
	修改：     README.md
	删除：     analysis_workspace/COMPREHENSIVE_PROJECT_EVALUATION.md
	删除：     analysis_workspace/EXECUTIVE_SUMMARY.md
	删除：     analysis_workspace/OPTIMIZER_SCHEDULER_ANALYSIS.md
	删除：     analysis_workspace/OPTIMIZER_SCHEDULER_SUMMARY.md
	删除：     analysis_workspace/SUCCESS_VALIDATION_REPORT.md
	删除：     analysis_workspace/architecture_diagrams/system_architecture.md
	删除：     analysis_workspace/config_audit/code_logic_analysis.md
	删除：     analysis_workspace/config_audit/config_conflict_report.json
	删除：     analysis_workspace/config_audit/config_conflict_scanner.py
	删除：     analysis_workspace/dependency_analysis/compatibility_report.json
	删除：     analysis_workspace/dependency_analysis/dependency_check.py
	删除：     analysis_workspace/final_report.md
	删除：     analysis_workspace/fix_implementations/backups/cosine.yaml.backup
	删除：     analysis_workspace/fix_implementations/backups/deeplabv3.yaml.backup
	删除：     analysis_workspace/fix_implementations/backups/modular_deeplabv3plus.yaml.backup
	删除：     analysis_workspace/fix_implementations/backups/poly.yaml.backup
	删除：     analysis_workspace/fix_implementations/backups/segmentation_module.py.backup
	删除：     analysis_workspace/fix_implementations/backups/swin_unet.yaml.backup
	删除：     analysis_workspace/fix_implementations/backups/unet.yaml.backup
	删除：     analysis_workspace/fix_implementations/backups/unetpp.yaml.backup
	删除：     analysis_workspace/fix_implementations/scheduler_compatibility_fix.py
	删除：     analysis_workspace/fix_implementations/segmentation_module_fixed.py
	删除：     analysis_workspace/test_scripts/fix_validation_test.py
	修改：     configs/callbacks/default.yaml
	修改：     configs/config.yaml
	修改：     configs/data/suide.yaml
	删除：     configs/experiment/baseline_unet.yaml
	删除：     configs/experiment/hpo_search_space.yaml
	修改：     configs/hpo/tune_basic.yaml
	修改：     configs/logger/wandb.yaml
	修改：     configs/loss/combined.yaml
	修改：     configs/loss/cross_entropy.yaml
	修改：     configs/loss/dice.yaml
	修改：     configs/loss/focal.yaml
	修改：     configs/loss/lovasz.yaml
	修改：     configs/model/deeplabv3.yaml
	修改：     configs/model/modular_deeplabv3plus.yaml
	修改：     configs/model/swin_unet.yaml
	修改：     configs/model/unet.yaml
	修改：     configs/model/unetpp.yaml
	删除：     configs/optimization/README.md
	删除：     configs/optimization/adaptive_preset.yaml
	删除：     configs/optimization/fast_training_preset.yaml
	删除：     configs/optimization/remote_sensing_preset.yaml
	删除：     configs/optimization/stable_training_preset.yaml
	删除：     configs/optimization/warmup_training_preset.yaml
	删除：     configs/optimizer/EXAMPLE_custom_optimizer.yaml
	删除：     configs/optimizer/EXAMPLE_remote_sensing_adamw.yaml
	修改：     configs/optimizer/adamw.yaml
	删除：     configs/optimizer/custom_optimizer.yaml
	删除：     configs/optimizer/remote_sensing_adamw.yaml
	修改：     configs/optimizer/sgd.yaml
	删除：     configs/scheduler/EXAMPLE_multiscale_scheduler.yaml
	修改：     configs/scheduler/cosine.yaml
	删除：     configs/scheduler/multiscale_scheduler.yaml
	修改：     configs/trainer/default.yaml
	删除：     docs/data_architecture_clean.md
	删除：     docs/data_migration_guide.md
	删除：     docs/model_migration_guide.md
	删除：     experiments_output/ray_results/unet_hpo_basic/.validate_storage_marker
	删除：     experiments_output/ray_results/unet_hpo_basic/tuner.pkl
	删除：     experiments_outputs/.gitkeep
	删除：     outputs/2025-07-10/20-19-22/.hydra/config.yaml
	删除：     outputs/2025-07-10/20-19-22/.hydra/hydra.yaml
	删除：     outputs/2025-07-10/20-19-22/.hydra/overrides.yaml
	删除：     outputs/2025-07-10/20-19-22/test_data_migration.log
	删除：     outputs/2025-07-14/14-51-21/.hydra/config.yaml
	删除：     outputs/2025-07-14/14-51-21/.hydra/hydra.yaml
	删除：     outputs/2025-07-14/14-51-21/.hydra/overrides.yaml
	删除：     outputs/2025-07-14/14-51-21/test_data_migration.log
	删除：     outputs/2025-07-14/15-14-11/.hydra/config.yaml
	删除：     outputs/2025-07-14/15-14-11/.hydra/hydra.yaml
	删除：     outputs/2025-07-14/15-14-11/.hydra/overrides.yaml
	删除：     outputs/2025-07-14/15-14-11/train.log
	删除：     outputs/2025-07-14/15-14-49/.hydra/config.yaml
	删除：     outputs/2025-07-14/15-14-49/.hydra/hydra.yaml
	删除：     outputs/2025-07-14/15-14-49/.hydra/overrides.yaml
	删除：     outputs/2025-07-14/15-14-49/train.log
	删除：     outputs/2025-07-14/15-20-06/.hydra/config.yaml
	删除：     outputs/2025-07-14/15-20-06/.hydra/hydra.yaml
	删除：     outputs/2025-07-14/15-20-06/.hydra/overrides.yaml
	删除：     outputs/2025-07-14/15-20-06/train.log
	删除：     outputs/2025-07-14/15-23-12/.hydra/config.yaml
	删除：     outputs/2025-07-14/15-23-12/.hydra/hydra.yaml
	删除：     outputs/2025-07-14/15-23-12/.hydra/overrides.yaml
	删除：     outputs/2025-07-14/15-23-12/train.log
	删除：     outputs/2025-07-14/16-07-00/.hydra/config.yaml
	删除：     outputs/2025-07-14/16-07-00/.hydra/hydra.yaml
	删除：     outputs/2025-07-14/16-07-00/.hydra/overrides.yaml
	删除：     outputs/2025-07-14/16-07-00/train.log
	删除：     outputs/2025-07-14/16-09-48/.hydra/config.yaml
	删除：     outputs/2025-07-14/16-09-48/.hydra/hydra.yaml
	删除：     outputs/2025-07-14/16-09-48/.hydra/overrides.yaml
	删除：     outputs/2025-07-14/16-09-48/train.log
	删除：     outputs/2025-07-14/18-25-58/.hydra/config.yaml
	删除：     outputs/2025-07-14/18-25-58/.hydra/hydra.yaml
	删除：     outputs/2025-07-14/18-25-58/.hydra/overrides.yaml
	删除：     outputs/2025-07-14/18-25-58/train.log
	删除：     outputs/2025-07-14/20-33-05/.hydra/config.yaml
	删除：     outputs/2025-07-14/20-33-05/.hydra/hydra.yaml
	删除：     outputs/2025-07-14/20-33-05/.hydra/overrides.yaml
	删除：     outputs/2025-07-14/20-33-05/train.log
	删除：     outputs/2025-07-14/21-01-34/.hydra/config.yaml
	删除：     outputs/2025-07-14/21-01-34/.hydra/hydra.yaml
	删除：     outputs/2025-07-14/21-01-34/.hydra/overrides.yaml
	删除：     outputs/2025-07-14/21-01-34/train.log
	删除：     outputs/2025-07-15/08-58-29/.hydra/config.yaml
	删除：     outputs/2025-07-15/08-58-29/.hydra/hydra.yaml
	删除：     outputs/2025-07-15/08-58-29/.hydra/overrides.yaml
	删除：     outputs/2025-07-15/08-58-29/train.log
	删除：     outputs/2025-07-15/09-42-35/.hydra/config.yaml
	删除：     outputs/2025-07-15/09-42-35/.hydra/hydra.yaml
	删除：     outputs/2025-07-15/09-42-35/.hydra/overrides.yaml
	删除：     outputs/2025-07-15/09-42-35/train.log
	删除：     outputs/2025-07-15/10-30-14/.hydra/config.yaml
	删除：     outputs/2025-07-15/10-30-14/.hydra/hydra.yaml
	删除：     outputs/2025-07-15/10-30-14/.hydra/overrides.yaml
	删除：     outputs/2025-07-15/10-30-14/train.log
	删除：     outputs/2025-07-15/10-42-17/.hydra/config.yaml
	删除：     outputs/2025-07-15/10-42-17/.hydra/hydra.yaml
	删除：     outputs/2025-07-15/10-42-17/.hydra/overrides.yaml
	删除：     outputs/2025-07-15/10-42-17/train.log
	删除：     outputs/2025-07-15/10-42-43/.hydra/config.yaml
	删除：     outputs/2025-07-15/10-42-43/.hydra/hydra.yaml
	删除：     outputs/2025-07-15/10-42-43/.hydra/overrides.yaml
	删除：     outputs/2025-07-15/10-42-43/train.log
	删除：     outputs/2025-07-15/10-51-33/.hydra/config.yaml
	删除：     outputs/2025-07-15/10-51-33/.hydra/hydra.yaml
	删除：     outputs/2025-07-15/10-51-33/.hydra/overrides.yaml
	删除：     outputs/2025-07-15/10-51-33/train.log
	删除：     outputs/2025-07-15/11-19-28/.hydra/config.yaml
	删除：     outputs/2025-07-15/11-19-28/.hydra/hydra.yaml
	删除：     outputs/2025-07-15/11-19-28/.hydra/overrides.yaml
	删除：     outputs/2025-07-15/11-19-28/train.log
	删除：     outputs/2025-07-15/11-22-50/.hydra/config.yaml
	删除：     outputs/2025-07-15/11-22-50/.hydra/hydra.yaml
	删除：     outputs/2025-07-15/11-22-50/.hydra/overrides.yaml
	删除：     outputs/2025-07-15/11-22-50/tune.log
	删除：     outputs/2025-07-15/11-23-38/.hydra/config.yaml
	删除：     outputs/2025-07-15/11-23-38/.hydra/hydra.yaml
	删除：     outputs/2025-07-15/11-23-38/.hydra/overrides.yaml
	删除：     outputs/2025-07-15/11-23-38/train.log
	删除：     outputs/2025-07-15/11-24-02/.hydra/config.yaml
	删除：     outputs/2025-07-15/11-24-02/.hydra/hydra.yaml
	删除：     outputs/2025-07-15/11-24-02/.hydra/overrides.yaml
	删除：     outputs/2025-07-15/11-24-02/train.log
	删除：     refactor_plan.md
	删除：     run.sh
	修改：     scripts/train.py
	修改：     scripts/tune.py
	修改：     src/__pycache__/__init__.cpython-312.pyc
	修改：     src/callbacks/__pycache__/__init__.cpython-312.pyc
	修改：     src/callbacks/__pycache__/rich_progress_callback.cpython-312.pyc
	修改：     src/callbacks/raytune_callback.py
	修改：     src/callbacks/rich_progress_callback.py
	修改：     src/callbacks/wandb_callbacks.py
	修改：     src/data/__pycache__/__init__.cpython-312.pyc
	修改：     src/data/__pycache__/loveda_datamodule.cpython-312.pyc
	修改：     src/data/__pycache__/suide_datamodule.cpython-312.pyc
	修改：     src/data/components/__pycache__/__init__.cpython-312.pyc
	修改：     src/data/components/__pycache__/transforms.cpython-312.pyc
	修改：     src/data/components/transforms.py
	修改：     src/data/datasets/__pycache__/__init__.cpython-312.pyc
	修改：     src/data/datasets/__pycache__/loveda_dataset.cpython-312.pyc
	修改：     src/data/datasets/__pycache__/suide_dataset.cpython-312.pyc
	修改：     src/data/datasets/loveda_dataset.py
	修改：     src/data/datasets/suide_dataset.py
	修改：     src/data/suide_datamodule.py
	修改：     src/models/__pycache__/__init__.cpython-312.pyc
	修改：     src/models/segmentation/__pycache__/__init__.cpython-312.pyc
	修改：     src/models/segmentation/__pycache__/deeplabv3plus.cpython-312.pyc
	修改：     src/models/segmentation/__pycache__/modular_adaptive_deeplabv3plus.cpython-312.pyc
	修改：     src/models/segmentation/__pycache__/swin_unet.cpython-312.pyc
	修改：     src/models/segmentation/__pycache__/unet.cpython-312.pyc
	修改：     src/modules/__pycache__/__init__.cpython-312.pyc
	修改：     src/modules/__pycache__/segmentation_module.cpython-312.pyc
	修改：     src/modules/segmentation_module.py
	修改：     src/optimizers/__init__.py
	修改：     src/optimizers/__pycache__/__init__.cpython-312.pyc
	删除：     src/optimizers/__pycache__/factory.cpython-312.pyc
	删除：     src/optimizers/examples/__pycache__/remote_sensing_adamw.cpython-312.pyc
	删除：     src/optimizers/examples/remote_sensing_adamw.py
	删除：     src/optimizers/factory.py
	删除：     src/optimizers/search_spaces.py
	修改：     src/schedulers/__init__.py
	修改：     src/schedulers/__pycache__/__init__.cpython-312.pyc
	删除：     src/schedulers/__pycache__/factory.cpython-312.pyc
	删除：     src/schedulers/examples/__pycache__/multiscale_scheduler.cpython-312.pyc
	删除：     src/schedulers/examples/multiscale_scheduler.py
	删除：     src/schedulers/factory.py
	删除：     src/schedulers/search_spaces.py
	修改：     src/utils/__pycache__/__init__.cpython-312.pyc
	修改：     src/utils/__pycache__/console_beautifier.cpython-312.pyc
	修改：     src/utils/__pycache__/hydra_resolvers.cpython-312.pyc
	修改：     src/utils/__pycache__/system_monitor.cpython-312.pyc
	修改：     src/utils/__pycache__/wandb_utils.cpython-312.pyc
	删除：     src/utils/console_beautifier.py
	修改：     src/utils/console_manager.py
	删除：     src/utils/raytune_console_manager.py
	修改：     src/utils/wandb_utils.py
	删除：     tests/COMPLETE_FIX_REPORT.md
	删除：     tests/PHASE1_FIX_REPORT.md
	修改：     tests/README.md
	删除：     tests/REAL_DATA_VALIDATION_REPORT.md
	删除：     tests/TEST_COMPLETION_REPORT.md
	修改：     tests/__pycache__/__init__.cpython-312.pyc
	修改：     tests/test_optimizer_factory.py
	修改：     tests/test_scheduler_factory.py

未跟踪的文件:
  （使用 "git add <文件>..." 以包含要提交的内容）
	configs/callbacks/loguru_progress.yaml
	configs/optimizer/adam.yaml
	configs/optimizer/adamax.yaml
	configs/optimizer/rmsprop.yaml
	configs/scheduler/reduce_on_plateau.yaml
	configs/ui/
	docs/AUTOMATION_SCRIPTS_COMPLETION.md
	docs/OPTIMIZERS_SCHEDULERS_GUIDE.md
	docs/TRAINING_GUIDE.md
	git_status_backup.txt
	requirements_backup.txt
	src/__pycache__/__init__.cpython-310.pyc
	src/optimizers/__pycache__/__init__.cpython-310.pyc
	src/optimizers/advanced/
	src/optimizers/remote_sensing/
	src/optimizers/standard/
	src/schedulers/__pycache__/warmup_scheduler.cpython-312.pyc
	src/schedulers/advanced/
	src/schedulers/remote_sensing/
	src/schedulers/standard/
	src/utils/__pycache__/console_manager.cpython-312.pyc
	src/utils/__pycache__/rich_theme.cpython-312.pyc
	src/utils/config_driven_hpo.py
	src/utils/rich_theme.py
	tests/__pycache__/test_losses.cpython-312-pytest-8.4.1.pyc
	tests/__pycache__/test_losses_hydra_simple.cpython-312-pytest-8.4.1.pyc
	tests/__pycache__/test_losses_hydra_standard.cpython-312-pytest-8.4.1.pyc
	tests/__pycache__/test_losses_integration.cpython-312-pytest-8.4.1.pyc
	tests/__pycache__/test_models_hydra_standard.cpython-312-pytest-8.4.1.pyc
	tests/__pycache__/test_optimizer_factory.cpython-312-pytest-8.4.1.pyc
	tests/__pycache__/test_scheduler_factory.cpython-312-pytest-8.4.1.pyc
	tests/test_hydra_config_standard.py
	tests/test_losses.py
	tests/test_losses_hydra_simple.py
	tests/test_losses_hydra_standard.py
	tests/test_losses_integration.py
	tests/test_models_hydra_standard.py
	tests/test_optimizers_schedulers_extended.py
	tests/test_wandb_raytune_integration.py

修改尚未加入提交（使用 "git add" 和/或 "git commit -a"）
