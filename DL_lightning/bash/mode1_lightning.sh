#!/bin/bash
# =============================================================================
# 模式一：Lightning模式运行脚本
# =============================================================================
# 功能：纯PyTorch Lightning训练，禁用WandB实验跟踪
# 特点：
# - 使用项目默认配置（config.yaml中的defaults）
# - 仅禁用WandB Logger组件，其他配置保持默认
# - Loguru日志系统自动检测为lightning模式
# - 系统监控：CPU、内存、GPU使用率实时显示
#
# 配置说明：
# - 依赖configs/config.yaml中的defaults配置
# - Loguru会自动应用training_modes.lightning配置
# - 控制台级别：INFO，文件级别：DEBUG，显示系统统计
#
# 使用方式：
#   chmod +x bash/mode1_lightning.sh
#   ./bash/mode1_lightning.sh
# =============================================================================

echo "🚀 启动模式一：Lightning模式训练"
echo "============================================================"
echo "配置信息："
echo "- 使用项目默认配置（config.yaml defaults）"
echo "- 禁用WandB Logger（logger=wandb_disabled）"
echo "- Loguru自动应用lightning模式配置"
echo "- 控制台级别：INFO，显示系统统计"
echo "============================================================"

# 进入项目根目录
cd "$(dirname "$0")/.."

# 执行训练命令 - 使用模式一专用配置文件
python scripts/train.py --config-name=config_mode1_lightning

echo "============================================================"
echo "✅ 模式一训练完成"
echo "============================================================"
