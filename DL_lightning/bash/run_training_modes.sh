#!/bin/bash
# =============================================================================
# DL_lightning 三种训练模式统一运行脚本
# =============================================================================
# 功能：提供统一入口来运行三种不同的训练模式
# 
# 三种模式说明：
# 1. Lightning模式：纯PyTorch Lightning训练，禁用WandB
# 2. Lightning+WandB模式：Lightning训练 + WandB实验跟踪
# 3. Lightning+WandB+RayTune模式：超参数优化训练
#
# 使用方式：
#   chmod +x bash/run_training_modes.sh
#   ./bash/run_training_modes.sh 1                    # 运行模式一
#   ./bash/run_training_modes.sh 2 offline            # 运行模式二（离线）
#   ./bash/run_training_modes.sh 3 10                 # 运行模式三（10个试验）
# =============================================================================

# 显示使用说明
show_usage() {
    echo "============================================================"
    echo "🚀 DL_lightning 训练模式选择"
    echo "============================================================"
    echo "使用方式: $0 <模式> [参数]"
    echo ""
    echo "可用模式："
    echo "  1  - Lightning模式（纯PyTorch Lightning训练）"
    echo "  2  - Lightning+WandB模式（带实验跟踪）"
    echo "  3  - Lightning+WandB+RayTune模式（超参数优化）"
    echo ""
    echo "模式参数："
    echo "  模式1: 无额外参数"
    echo "  模式2: [online|offline] - 可选择WandB模式"
    echo "  模式3: [试验数量] - 可指定HPO试验数量"
    echo ""
    echo "示例："
    echo "  $0 1                    # 运行Lightning模式"
    echo "  $0 2                    # 运行Lightning+WandB模式（自动）"
    echo "  $0 2 offline            # 运行Lightning+WandB模式（离线）"
    echo "  $0 3                    # 运行HPO模式（默认试验数）"
    echo "  $0 3 20                 # 运行HPO模式（20个试验）"
    echo "============================================================"
}

# 检查参数
if [ $# -eq 0 ] || [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_usage
    exit 0
fi

# 进入项目根目录
cd "$(dirname "$0")/.."

# 根据模式参数执行相应脚本
case "$1" in
    1)
        echo "🎯 启动模式一：Lightning模式"
        ./bash/mode1_lightning.sh
        ;;
    2)
        echo "🎯 启动模式二：Lightning+WandB模式"
        if [ -n "$2" ]; then
            ./bash/mode2_lightning_wandb.sh "$2"
        else
            ./bash/mode2_lightning_wandb.sh
        fi
        ;;
    3)
        echo "🎯 启动模式三：Lightning+WandB+RayTune模式"
        if [ -n "$2" ]; then
            ./bash/mode3_lightning_wandb_raytune.sh "$2"
        else
            ./bash/mode3_lightning_wandb_raytune.sh
        fi
        ;;
    *)
        echo "❌ 错误：无效的模式参数 '$1'"
        echo ""
        show_usage
        exit 1
        ;;
esac
