#!/bin/bash
# =============================================================================
# 模式三：Lightning+WandB+RayTune模式运行脚本
# =============================================================================
# 功能：超参数优化训练，支持并行试验和自动调优
# 特点：
# - 使用模式三专用配置文件（config_mode3_lightning_wandb_raytune.yaml）
# - 启用Ray Tune超参数搜索
# - WandB跟踪多个试验
# - Loguru自动应用lightning_wandb_raytune模式配置
#
# 配置说明：
# - 依赖config_mode3_lightning_wandb_raytune.yaml配置
# - 使用tune.py脚本而不是train.py
# - 控制台级别：WARNING（减少输出噪音）
# - 禁用系统统计（减少多进程开销）
#
# 使用方式：
#   chmod +x bash/mode3_lightning_wandb_raytune.sh
#   ./bash/mode3_lightning_wandb_raytune.sh                    # 使用默认HPO配置
#   ./bash/mode3_lightning_wandb_raytune.sh 10                 # 指定试验数量
# =============================================================================

echo "🚀 启动模式三：Lightning+WandB+RayTune模式训练"
echo "============================================================"
echo "配置信息："
echo "- 使用模式三专用配置（config_mode3_lightning_wandb_raytune.yaml）"
echo "- 启用Ray Tune超参数优化"
echo "- WandB跟踪多个试验"
echo "- Loguru自动应用lightning_wandb_raytune模式配置"
echo "- 控制台级别：WARNING，禁用系统统计"
echo "============================================================"

# 进入项目根目录
cd "$(dirname "$0")/.."

# 检查命令行参数，支持指定试验数量
if [ -n "$1" ] && [ "$1" -gt 0 ] 2>/dev/null; then
    echo "🔢 设置试验数量为: $1"
    python scripts/tune.py --config-name=config_mode3_lightning_wandb_raytune hpo.run_params.num_samples=$1
else
    echo "🤖 使用默认试验数量（配置文件中定义）"
    python scripts/tune.py --config-name=config_mode3_lightning_wandb_raytune
fi

echo "============================================================"
echo "✅ 模式三超参数优化完成"
echo "============================================================"
