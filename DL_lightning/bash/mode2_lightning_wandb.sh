#!/bin/bash
# =============================================================================
# 模式二：Lightning+WandB模式运行脚本
# =============================================================================
# 功能：PyTorch Lightning训练 + WandB实验跟踪
# 特点：
# - 使用模式二专用配置文件（config_mode2_lightning_wandb.yaml）
# - 启用WandB Logger组件（自动模式检测）
# - Loguru自动应用lightning_wandb模式配置
# - 控制台级别：INFO，显示系统统计，WandB集成
#
# 配置说明：
# - 依赖config_mode2_lightning_wandb.yaml配置
# - WandB自动检测网络状态选择最佳模式（在线/离线）
# - 支持通过参数强制指定WandB模式
#
# 使用方式：
#   chmod +x bash/mode2_lightning_wandb.sh
#   ./bash/mode2_lightning_wandb.sh                    # 自动模式
#   ./bash/mode2_lightning_wandb.sh offline            # 强制离线模式
#   ./bash/mode2_lightning_wandb.sh online             # 强制在线模式
# =============================================================================

echo "🚀 启动模式二：Lightning+WandB模式训练"
echo "============================================================"
echo "配置信息："
echo "- 使用模式二专用配置（config_mode2_lightning_wandb.yaml）"
echo "- 启用WandB Logger（自动模式检测）"
echo "- Loguru自动应用lightning_wandb模式配置"
echo "- 控制台级别：INFO，显示系统统计"
echo "============================================================"

# 进入项目根目录
cd "$(dirname "$0")/.."

# 检查命令行参数，支持强制指定WandB模式
if [ "$1" = "offline" ]; then
    echo "🔄 强制使用离线模式"
    python scripts/train.py --config-name=config_mode2_lightning_wandb logger=wandb_offline
elif [ "$1" = "online" ]; then
    echo "🌐 强制使用在线模式"
    python scripts/train.py --config-name=config_mode2_lightning_wandb logger=wandb_online
else
    echo "🤖 使用自动模式检测"
    python scripts/train.py --config-name=config_mode2_lightning_wandb
fi

echo "============================================================"
echo "✅ 模式二训练完成"
echo "============================================================"
