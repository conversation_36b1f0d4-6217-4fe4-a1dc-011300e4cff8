# =============================================================================
# 模式二：Lightning+WandB模式主配置文件
# =============================================================================
# 这是DL_lightning项目的第二种训练模式配置文件，集成了WandB实验跟踪功能
#
# 模式特点：
# - PyTorch Lightning训练框架 + Weights & Biases实验跟踪平台
# - 完整的Loguru日志系统，提供清晰的控制台输出和详细的文件记录
# - WandB智能模式检测：自动选择在线/离线模式，确保训练不中断
# - 实验跟踪功能：实时监控训练指标、损失曲线、学习率变化等
# - 系统资源监控：实时显示CPU、内存、GPU使用率
# - 可视化支持：训练过程可视化、超参数记录、模型对比等
#
# WandB模式说明：
# - auto: 自动检测网络状况，有网络时在线同步，无网络时离线保存
# - online: 强制在线模式，实时同步数据到WandB云端服务器
# - offline: 强制离线模式，数据保存到本地，可后续手动同步
#
# 适用场景：
# - 正式的研究实验和模型开发
# - 需要详细记录和对比实验结果
# - 团队协作和实验结果分享
# - 长期项目的实验管理和版本控制
#
# 使用方式：
#   python scripts/train.py --config-name=config_mode2_lightning_wandb
#   python scripts/train.py --config-name=config_mode2_lightning_wandb logger=wandb_offline  # 强制离线
#   python scripts/train.py --config-name=config_mode2_lightning_wandb logger=wandb_online   # 强制在线
#   ./bash/mode2_lightning_wandb.sh                              # 推荐使用bash脚本
#   ./bash/run_training_modes.sh 2                              # 通过统一脚本运行
# =============================================================================

defaults:
  - _self_                    # 当前文件的配置优先级最高
  - trainer: default          # 训练器配置：GPU设置、训练轮数、精度等
  - model: deeplabv3          # 模型配置：默认使用DeepLabV3+语义分割模型
  - data: suide               # 数据集配置：默认使用SuiDe遥感数据集
  - loss: combined            # 损失函数配置：默认使用组合损失函数(CE+Dice+Focal)
  - optimizer: adamw          # 优化器配置：默认使用AdamW优化器
  - scheduler: cosine         # 学习率调度器：默认使用余弦退火调度器
  - logger: wandb             # 关键差异：启用WandB Logger（自动模式）
  - callbacks: default        # 回调函数：默认使用标准回调(检查点、早停等)
  - ui: loguru_config         # UI配置：使用Loguru统一日志系统
  - experiment: null          # 实验配置：默认不使用预定义实验配置

# =============================================================================
# 模式二特定配置
# =============================================================================
# 全局项目设定
project_name: "SuiDe_RemoteSensing"
experiment_name: "lightning_wandb_mode"                               # 模式二专用实验名称
run_name: "${experiment_name}_${now:%Y-%m-%d_%H-%M-%S}"

# 训练模式标识（用于Loguru模式检测）
training_mode: "lightning_wandb"

# =============================================================================
# Weights & Biases (WandB) 实验跟踪配置
# =============================================================================
# WandB是一个强大的机器学习实验跟踪平台，提供以下功能：
# - 实时监控训练指标和损失曲线
# - 超参数记录和可视化
# - 模型版本管理和比较
# - 团队协作和实验分享
# - 自动模式切换 (在线/离线/禁用)
wandb:
  # ---------------------------------------------------------------------------
  # 基本配置
  # ---------------------------------------------------------------------------
  project: ${project_name}                    # WandB项目名称，引用上面定义的project_name
  name: ${run_name}                          # 运行名称，引用上面定义的run_name
  tags: ["lightning-wandb", "experiment-tracking", "${model.name}"]  # 标签列表，用于实验分类和筛选
  notes: "Lightning training with WandB experiment tracking and visualization."  # 实验描述，记录实验目的和特点

  # ---------------------------------------------------------------------------
  # 运行模式配置
  # ---------------------------------------------------------------------------
  # 四种模式说明：
  # - auto: 自动检测网络状况，有网络时在线，无网络时离线
  # - online: 强制在线模式，实时同步数据到WandB云端
  # - offline: 强制离线模式，数据保存到本地，可后续手动同步
  # - disabled: 完全禁用WandB，不记录任何实验数据
  mode: auto

  # ---------------------------------------------------------------------------
  # 存储配置
  # ---------------------------------------------------------------------------
  local_dir: null                            # 本地存储目录，null时使用Hydra输出目录下的wandb_local子目录

  # ---------------------------------------------------------------------------
  # 高级功能配置
  # ---------------------------------------------------------------------------
  log_model: false                           # 是否上传模型文件到WandB（大文件会影响同步速度）
  silent: true                               # 是否静默运行，减少WandB的控制台输出
  api_key: null                              # WandB API密钥，建议通过环境变量WANDB_API_KEY设置

  # ---------------------------------------------------------------------------
  # 网络连接配置
  # ---------------------------------------------------------------------------
  timeout: 10                                # 网络连接超时时间(秒)，适用于网络不稳定的环境
  retry_attempts: 3                          # 连接失败时的重试次数
