# =============================================================================
# 余弦退火学习率调度器配置
# =============================================================================
# 余弦退火调度器按照余弦函数曲线调整学习率，具有以下特点：
# - 学习率平滑下降，避免突然的跳跃
# - 在训练后期提供很小的学习率进行精细调优
# - 广泛应用于深度学习训练中
# - 有助于模型收敛到更好的局部最优解
#
# 学习率变化规律：
#   lr(t) = eta_min + (lr_initial - eta_min) * (1 + cos(π * t / T_max)) / 2
#
# 适用场景：
# - 大多数深度学习任务
# - 需要平滑学习率衰减的场景
# - 长时间训练的任务
# - 精细调优阶段
#
# 使用方式：
#   python scripts/train.py scheduler=cosine
#   python scripts/train.py scheduler=cosine scheduler.T_max=200
# =============================================================================

_target_: torch.optim.lr_scheduler.CosineAnnealingLR

# =============================================================================
# 核心参数
# =============================================================================
T_max: 100                                   # 余弦退火的半周期长度
                                             # 通常设置为总训练epoch数
                                             # 在T_max个epoch后，学习率从初始值降到eta_min
                                             # 建议设置为实际训练轮数
                                             # 可通过命令行覆盖: scheduler.T_max=${trainer.max_epochs}

eta_min: 1e-6                                # 最小学习率
                                             # 学习率的下界，防止学习率降到过低
                                             # 推荐范围: 1e-7 到 1e-5
                                             # - 过小: 训练后期几乎不更新参数
                                             # - 过大: 可能影响最终收敛精度
