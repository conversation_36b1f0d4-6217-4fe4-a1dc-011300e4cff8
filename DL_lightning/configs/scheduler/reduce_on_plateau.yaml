# @package _global_

# ReduceLROnPlateau学习率调度器配置
# 适用场景：自适应学习率调整，验证集指标停滞时使用
# 推荐用于：需要根据验证性能动态调整的场景

_target_: torch.optim.lr_scheduler.ReduceLROnPlateau
mode: 'min'  # 'min'表示指标越小越好，'max'表示指标越大越好
factor: 0.5  # 学习率衰减因子
patience: 10  # 容忍多少个epoch没有改善
threshold: 1e-4  # 改善的最小阈值
threshold_mode: 'rel'  # 'rel'表示相对阈值，'abs'表示绝对阈值
cooldown: 0  # 减少学习率后的冷却期
min_lr: 1e-7  # 最小学习率
eps: 1e-8  # 学习率衰减的最小值

# 使用说明：
# - 根据验证集指标自动调整学习率
# - mode: 根据监控指标选择'min'(loss)或'max'(accuracy)
# - patience: 指标不改善的容忍epoch数
# - 适合长时间训练且不确定最佳调度策略的场景
# - 在Lightning中通常监控'val_loss'或'val_miou'
