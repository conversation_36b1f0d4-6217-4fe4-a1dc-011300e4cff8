# 带预热的余弦退火调度器配置
# 支持学习率预热，适合大批量训练和复杂模型

_target_: torch.optim.lr_scheduler.CosineAnnealingLR

# 主要参数
T_max: 100              # 余弦退火的最大迭代次数
eta_min: 1e-6           # 最小学习率

# 预热参数
warmup_epochs: 5        # 预热轮数
warmup_start_lr: 1e-6   # 预热起始学习率

# 使用方式:
# python scripts/train.py scheduler=cosine_with_warmup

# 参数覆盖示例:
# python scripts/train.py scheduler=cosine_with_warmup scheduler.warmup_epochs=10 scheduler.T_max=200
