# =============================================================================
# 模式一：Lightning模式主配置文件
# =============================================================================
# 这是DL_lightning项目的第一种训练模式配置文件，专门用于纯PyTorch Lightning训练
#
# 模式特点：
# - 纯PyTorch Lightning训练框架，不使用任何外部实验跟踪工具
# - 完整的Loguru日志系统，提供清晰的控制台输出和详细的文件记录
# - 系统资源监控：实时显示CPU、内存、GPU使用率
# - 标准的训练流程：包括验证、检查点保存、早停等回调功能
# - 最简洁的配置，专注于模型训练本身
#
# 适用场景：
# - 快速原型开发和算法验证
# - 本地调试和模型测试
# - 不需要实验跟踪的简单训练任务
# - 教学和学习目的的训练演示
#
# 使用方式：
#   python scripts/train.py --config-name=config_mode1_lightning
#   ./bash/mode1_lightning.sh                                    # 推荐使用bash脚本
#   ./bash/run_training_modes.sh 1                              # 通过统一脚本运行
# =============================================================================

defaults:
  - _self_                    # 当前文件的配置优先级最高，确保模式特定设置不被覆盖
  - trainer: default          # 训练器配置：自动GPU检测、混合精度、50轮训练等标准设置
  - model: deeplabv3          # 模型配置：使用DeepLabV3+语义分割模型，ResNet50骨干网络
  - data: suide               # 数据集配置：SuiDe遥感数据集，14类语义分割任务
  - loss: combined            # 损失函数配置：组合损失(CrossEntropy+Dice+Focal)，平衡精度和收敛
  - optimizer: adamw          # 优化器配置：AdamW优化器，权重衰减1e-4，学习率1e-4
  - scheduler: cosine         # 学习率调度器：余弦退火调度，平滑降低学习率
  - logger: wandb_disabled    # 关键差异：完全禁用WandB Logger，不进行任何实验跟踪
  - callbacks: default        # 回调函数：模型检查点、学习率监控、Loguru进度显示等
  - ui: loguru_config         # UI配置：统一的Loguru日志系统，控制台和文件双重输出
  - experiment: null          # 实验配置：不使用预定义实验配置，保持配置简洁

# =============================================================================
# 模式一特定配置
# =============================================================================

# -----------------------------------------------------------------------------
# 全局项目设定
# -----------------------------------------------------------------------------
project_name: "SuiDe_RemoteSensing"                                   # 项目名称，用于组织和标识训练任务
experiment_name: "lightning_mode"                                     # 模式一专用实验名称，区别于其他训练模式
run_name: "${experiment_name}_${now:%Y-%m-%d_%H-%M-%S}"               # 运行名称，自动添加时间戳确保唯一性

# -----------------------------------------------------------------------------
# 训练模式标识
# -----------------------------------------------------------------------------
# 此字段用于Loguru控制台管理器的模式检测，确保应用正确的日志配置
# 对应loguru_config.yaml中training_modes.lightning的配置：
# - console_level: "INFO" - 控制台显示INFO级别及以上的日志
# - file_level: "DEBUG" - 文件记录DEBUG级别及以上的详细日志
# - show_system_stats: true - 显示CPU、内存、GPU等系统资源监控
training_mode: "lightning"
