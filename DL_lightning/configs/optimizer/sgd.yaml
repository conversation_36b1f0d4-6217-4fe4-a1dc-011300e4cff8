# =============================================================================
# SGD (随机梯度下降) 优化器配置
# =============================================================================
# SGD是最经典的优化算法，具有以下特点：
# - 简单直观，计算开销小
# - 在某些任务中能达到更好的泛化性能
# - 需要仔细调节学习率和动量参数
# - 在大批次训练中表现良好
#
# 适用场景：
# - 计算资源受限的环境
# - 需要最佳泛化性能的任务
# - 传统CNN架构(ResNet, VGG等)
# - 大批次训练
#
# 使用方式：
#   python scripts/train.py optimizer=sgd
#   python scripts/train.py optimizer=sgd optimizer.lr=0.01 optimizer.momentum=0.9
# =============================================================================

_target_: torch.optim.SGD                    # PyTorch SGD优化器
_partial_: true                              # 使用部分实例化，模型参数在运行时传入

# =============================================================================
# 核心超参数
# =============================================================================
lr: 1e-2                                     # 学习率 (Learning Rate)
                                             # SGD通常需要比Adam更大的学习率
                                             # 推荐范围: 1e-3 到 1e-1
                                             # - 过大: 训练不稳定，损失震荡
                                             # - 过小: 收敛极慢或陷入局部最优

momentum: 0.9                                # 动量系数 (Momentum)
                                             # 累积历史梯度信息，加速收敛
                                             # 推荐范围: 0.8 到 0.99
                                             # - 0: 标准SGD，无动量
                                             # - 接近1: 更强的动量效应

weight_decay: 1e-4                           # 权重衰减系数 (L2正则化)
                                             # 防止过拟合，促进模型泛化
                                             # 推荐范围: 1e-5 到 1e-3
                                             # - SGD中权重衰减直接作用于梯度

nesterov: false                              # 是否使用Nesterov动量
                                             # true: 使用Nesterov加速梯度(NAG)
                                             # false: 使用标准动量
                                             # NAG通常能提供更好的收敛性能