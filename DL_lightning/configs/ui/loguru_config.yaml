# =============================================================================
# DL_lightning项目统一Loguru UI配置
# =============================================================================
# 此配置文件定义了项目中所有Loguru组件的统一样式和行为
# 确保整个项目的视觉一致性和用户体验统一性
#
# 使用方式：
#   python scripts/train.py ui=loguru_config
#   python scripts/train.py ui.console.level=DEBUG  # 覆盖日志级别
# =============================================================================

# =============================================================================
# 控制台输出配置
# =============================================================================
console:
  # 基础设置
  format: "[{level}] {message}"              # 控制台简洁格式
  level: "INFO"                              # 控制台日志级别
  colorize: true                             # 启用颜色输出
  
  # 输出控制
  quiet: false                               # 是否静默模式
  stderr: false                              # 是否输出到stderr
  
  # 环境检测
  auto_disable_in_ci: true                   # 在CI环境中自动禁用颜色
  auto_disable_in_ray: true                  # 在Ray Tune中自动禁用颜色

# =============================================================================
# 文件输出配置
# =============================================================================
file:
  # 详细格式配置
  format: "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message}"
  level: "DEBUG"                             # 文件日志级别
  
  # 文件管理
  rotation: "100 MB"                         # 文件大小轮转
  retention: "30 days"                       # 保留时间
  compression: "zip"                         # 压缩格式
  
  # 异步配置
  enqueue: true                              # 启用异步队列
  serialize: false                           # 禁用序列化

# =============================================================================
# 进度显示配置
# =============================================================================
progress:
  # 刷新设置
  refresh_rate: 10                           # 进度条刷新频率
  
  # 显示控制
  show_system_stats: true                    # 显示系统统计
  show_eta: true                             # 显示预计完成时间
  show_speed: true                           # 显示处理速度
  
  # 格式设置
  bar_width: 40                              # 进度条宽度
  leave_progress: true                       # 完成后保留进度条

# =============================================================================
# 系统监控配置
# =============================================================================
system_monitor:
  # 监控开关
  enable_cpu: true                           # CPU监控
  enable_memory: true                        # 内存监控
  enable_gpu: true                           # GPU监控
  
  # 更新频率
  update_interval: 1.0                       # 更新间隔(秒)
  
  # 显示格式
  show_percentage: true                      # 显示百分比
  show_absolute: true                        # 显示绝对值
  precision: 1                               # 数值精度

# =============================================================================
# 文本前缀映射
# =============================================================================
text_prefixes:
  # 基础级别
  debug: "[DEBUG]"
  info: "[INFO]"
  success: "[SUCCESS]"
  warning: "[WARNING]"
  error: "[ERROR]"
  critical: "[CRITICAL]"
  
  # 功能级别
  progress: "[PROGRESS]"
  system: "[SYSTEM]"
  model: "[MODEL]"
  data: "[DATA]"
  config: "[CONFIG]"
  logger: "[LOGGER]"
  callback: "[CALLBACK]"
  checkpoint: "[CHECKPOINT]"
  
  # 训练级别
  training: "[TRAINING]"
  validation: "[VALIDATION]"
  testing: "[TESTING]"
  epoch: "[EPOCH]"
  batch: "[BATCH]"

# =============================================================================
# 训练模式配置
# =============================================================================
training_modes:
  # Lightning模式
  lightning:
    console_level: "INFO"
    file_level: "DEBUG"
    show_system_stats: true
    
  # Lightning+WandB模式
  lightning_wandb:
    console_level: "INFO"
    file_level: "DEBUG"
    show_system_stats: true
    wandb_integration: true
    
  # Lightning+WandB+RayTune模式
  lightning_wandb_raytune:
    console_level: "WARNING"                 # Ray Tune环境下减少输出
    file_level: "INFO"
    show_system_stats: false                 # 减少系统监控开销
    quiet_mode: true

# =============================================================================
# 性能优化配置
# =============================================================================
performance:
  # 异步设置
  async_logging: true                        # 启用异步日志
  queue_size: 1000                           # 队列大小
  
  # 缓存设置
  format_cache: true                         # 启用格式缓存
  level_cache: true                          # 启用级别缓存
  
  # 批处理设置
  batch_size: 10                             # 批处理大小
  flush_interval: 1.0                        # 刷新间隔

# =============================================================================
# 调试配置
# =============================================================================
debug:
  # 调试开关
  enable_trace: false                        # 启用跟踪
  enable_profiling: false                    # 启用性能分析
  
  # 详细输出
  show_caller_info: false                    # 显示调用者信息
  show_thread_info: false                    # 显示线程信息
  show_process_info: false                   # 显示进程信息

# =============================================================================
# 兼容性配置
# =============================================================================
compatibility:
  # Rich兼容
  rich_markup: false                         # 禁用Rich标记语法
  rich_emoji: false                          # 禁用Rich表情符号
  
  # 向后兼容
  legacy_api: true                           # 启用遗留API支持
  auto_migration: true                       # 自动迁移警告
