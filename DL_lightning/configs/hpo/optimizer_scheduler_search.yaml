# 优化器和调度器超参数搜索配置
# 使用Ray Tune进行优化器和调度器的联合搜索

# Ray Tune 基础配置
num_samples: 20
max_concurrent_trials: 4
grace_period: 10
reduction_factor: 2

# 搜索算法配置
search_alg:
  _target_: ray.tune.search.optuna.OptunaSearch
  metric: "val/iou"
  mode: "max"

# 调度器配置
scheduler:
  _target_: ray.tune.schedulers.ASHAScheduler
  metric: "val/iou"
  mode: "max"
  max_t: 100
  grace_period: 10
  reduction_factor: 2

# 搜索空间配置
search_space:
  # 优化器搜索空间
  optimizer:
    _target_: 
      _type_: "choice"
      _args_: 
        - "torch.optim.AdamW"
        - "src.optimizers.examples.remote_sensing_adamw.RemoteSensingAdamW"
    
    lr:
      _type_: "loguniform"
      _args_: [1e-5, 1e-2]
    
    weight_decay:
      _type_: "loguniform" 
      _args_: [1e-5, 1e-1]
    
    # 仅对remote_sensing_adamw有效的参数
    gradient_clip:
      _type_: "uniform"
      _args_: [0.5, 2.0]
    
    adaptive_wd:
      _type_: "choice"
      _args_: [true, false]
  
  # 调度器搜索空间
  scheduler:
    _target_:
      _type_: "choice"
      _args_:
        - "torch.optim.lr_scheduler.CosineAnnealingLR"
        - "src.schedulers.examples.multiscale_scheduler.MultiScaleScheduler"
    
    # 余弦退火参数
    T_max:
      _type_: "choice"
      _args_: [80, 100, 120, 150]
    
    eta_min:
      _type_: "loguniform"
      _args_: [1e-8, 1e-5]
    
    # 多尺度调度器参数
    base_epochs:
      _type_: "choice"
      _args_: [80, 100, 120, 150]
    
    warmup_epochs:
      _type_: "choice"
      _args_: [3, 5, 8, 10]
    
    min_lr_factor:
      _type_: "uniform"
      _args_: [0.001, 0.05]

# 资源配置
resources_per_trial:
  cpu: 2
  gpu: 0.5

# 停止条件
stop:
  training_iteration: 100
  val/iou: 0.95

# 使用方式:
# python scripts/tune.py --config-path configs/hpo --config-name optimizer_scheduler_search
