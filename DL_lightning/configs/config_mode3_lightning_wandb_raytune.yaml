# =============================================================================
# 模式三：Lightning+WandB+RayTune模式主配置文件
# =============================================================================
# 这是DL_lightning项目的第三种训练模式配置文件，专门用于超参数优化训练
#
# 模式特点：
# - PyTorch Lightning + Weights & Biases + Ray Tune 三重集成
# - 自动超参数搜索：支持网格搜索、随机搜索、贝叶斯优化等算法
# - 并行试验执行：充分利用多GPU资源，同时运行多个试验
# - 智能调度策略：ASHA调度器实现早停和资源高效分配
# - 实验跟踪集成：每个试验的结果都记录到WandB，便于对比分析
# - 优化的日志系统：减少输出噪音，专注于关键指标和试验状态
#
# Ray Tune调度器说明：
# - ASHA (Asynchronous Successive Halving Algorithm): 异步连续减半算法
# - 早停机制：自动淘汰表现差的试验，节省计算资源
# - 资源动态分配：表现好的试验获得更多训练时间
# - 高效搜索：在有限资源下找到最优超参数组合
#
# 适用场景：
# - 模型超参数调优和性能优化
# - 大规模实验和参数空间探索
# - 自动化机器学习 (AutoML) 流程
# - 研究新算法的最佳配置
#
# 使用方式：
#   python scripts/tune.py --config-name=config_mode3_lightning_wandb_raytune
#   ./bash/mode3_lightning_wandb_raytune.sh                      # 推荐使用bash脚本
#   ./bash/run_training_modes.sh 3                              # 通过统一脚本运行
#   ./bash/run_training_modes.sh 3 20                           # 指定20个试验
# =============================================================================

defaults:
  - _self_                    # 当前文件的配置优先级最高
  - model: unet               # 模型配置：HPO使用轻量模型
  - data: suide               # 数据集配置
  - loss: ce_dice             # 损失函数配置：HPO使用简化损失
  - ui: loguru_config         # UI配置：使用Loguru统一日志系统

# =============================================================================
# 模式三特定配置
# =============================================================================
# 全局项目设定
project_name: "SuiDe_RemoteSensing"
experiment_name: "lightning_wandb_raytune_mode"                       # 模式三专用实验名称
run_name: "${experiment_name}_${now:%Y-%m-%d_%H-%M-%S}"

# 训练模式标识（用于Loguru模式检测）
training_mode: "lightning_wandb_raytune"

# =============================================================================
# Ray Tune 超参数优化 (HPO) 配置
# =============================================================================
# Ray Tune是一个可扩展的超参数调优库，支持多种搜索算法和调度策略
# 本配置针对语义分割任务进行了优化，平衡了搜索效率和资源利用率

hpo:
  # ---------------------------------------------------------------------------
  # 实验基本信息
  # ---------------------------------------------------------------------------
  experiment_name: "unet_hpo_basic"                                   # HPO实验名称，用于标识和组织试验结果

  # ---------------------------------------------------------------------------
  # Ray Tune 运行参数
  # ---------------------------------------------------------------------------
  run_params:
    name: "unet_hpo_mode3"                                           # 试验运行名称，会在WandB中显示
    num_samples: 5                                                   # 试验数量，可通过命令行参数覆盖
    local_dir: "${get_project_root:}/experiments_output/ray_results" # 试验结果保存目录

  # ---------------------------------------------------------------------------
  # Ray 集群初始化配置
  # ---------------------------------------------------------------------------
  # Ray是分布式计算框架，需要初始化集群资源
  ray_init_args:
    _target_: ray.init                                               # 使用Hydra实例化Ray集群
    num_cpus: 4                                                      # 集群总CPU核心数
    num_gpus: 1                                                      # 集群总GPU数量

  # ---------------------------------------------------------------------------
  # 单个试验资源分配
  # ---------------------------------------------------------------------------
  # 每个试验分配的计算资源，支持资源共享以提高利用率
  resources_per_trial:
    use_gpu: true                                                    # 是否使用GPU训练
    CPU: 1                                                           # 每个试验分配的CPU核心数
    GPU: 0.5                                                         # 每个试验分配的GPU份额，0.5表示两个试验共享一个GPU

  # ---------------------------------------------------------------------------
  # ASHA 调度器配置
  # ---------------------------------------------------------------------------
  # Asynchronous Successive Halving Algorithm (ASHA) 是一种高效的早停调度器
  # 通过逐步淘汰表现差的试验，将资源集中在有希望的试验上
  scheduler:
    _target_: ray.tune.schedulers.ASHAScheduler                      # 使用ASHA调度器
    metric: "val_iou"                                                # 优化目标指标：验证集IoU
    mode: "max"                                                      # 优化方向：最大化IoU
    max_t: 10                                                        # 单个试验最大训练轮数
    grace_period: 2                                                  # 宽限期：前2轮不进行早停判断
    reduction_factor: 2                                              # 减半因子：每轮淘汰一半表现差的试验

  # ---------------------------------------------------------------------------
  # 超参数搜索空间定义
  # ---------------------------------------------------------------------------
  # 定义需要优化的超参数及其取值范围
  # 搜索空间设计原则：覆盖合理范围，避免过大导致搜索效率低下
  search_space:
    # 学习率搜索空间：对数均匀分布，覆盖常用的学习率范围
    lr:
      min: 1e-5                                                      # 最小学习率：0.00001
      max: 1e-2                                                      # 最大学习率：0.01

    # 批次大小搜索空间：离散选择，考虑GPU内存限制
    batch_size:
      - 8                                                            # 小批次：适合大模型或GPU内存有限
      - 16                                                           # 中等批次：平衡训练速度和稳定性
      - 32                                                           # 大批次：适合小模型或大GPU内存
