# =============================================================================
# Dice 损失函数配置
# =============================================================================
# Dice损失基于Dice系数(F1-score)，特别适合分割任务，具有以下特点：
# - 直接优化分割重叠度指标
# - 对类别不平衡相对鲁棒
# - 特别适合小目标分割
# - 与IoU指标高度相关
# - 可以处理多类别分割
#
# Dice系数公式：
#   Dice = 2 * |A ∩ B| / (|A| + |B|)
#   Dice Loss = 1 - Dice
#
# 适用场景：
# - 医学图像分割
# - 小目标检测和分割
# - 类别不平衡的分割任务
# - 需要精确边界的分割
#
# 使用方式：
#   python scripts/train.py loss=dice
#   python scripts/train.py loss=dice loss.smooth=2.0
# =============================================================================

_target_: src.losses.dice_loss.DiceLoss

# =============================================================================
# 基础配置
# =============================================================================
num_classes: ${data.dataset_config.num_classes}  # 分割类别数，自动从数据配置获取
ignore_index: 255                           # 忽略的标签索引
                                             # 通常用于未标注区域或边界像素
                                             # 这些像素不参与损失计算

# =============================================================================
# Dice 计算参数
# =============================================================================
smooth: 1.0                                  # 平滑参数
                                             # 防止除零错误，稳定训练
                                             # 较大值使损失更平滑
                                             # 推荐范围: 0.1-2.0

per_class: false                             # 是否返回每个类别的损失
                                             # true: 分别计算每类的Dice损失
                                             # false: 计算所有类别的平均Dice损失
                                             # 通常使用false以简化训练

square: false                                # 是否对预测和目标进行平方
                                             # true: 使用平方Dice损失
                                             # false: 使用标准Dice损失
                                             # 平方版本对异常值更敏感

# =============================================================================
# 损失聚合配置
# =============================================================================
reduction: 'mean'                            # 损失聚合方式
                                             # 'mean': 计算平均损失 (推荐)
                                             # 'sum': 计算总损失
                                             # 'none': 返回每个样本的损失
