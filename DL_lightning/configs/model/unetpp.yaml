_target_: src.modules.segmentation_module.SegmentationModule
ignore_index: 255
loss_cfg: ${loss}
model_name: unetpp
model_params:
  bilinear: true
  deep_supervision: false
  features:
  - 64
  - 128
  - 256
  - 512
  in_channels: 3
  pretrained: false
name: UNet++
num_classes: ${data.dataset_config.num_classes}
optimizer_cfg:
  _partial_: true
  _target_: torch.optim.AdamW
  betas:
  - 0.9
  - 0.999
  lr: 0.001
  weight_decay: 0.01
scheduler_cfg:
  T_0: 10
  T_mult: 2
  _target_: torch.optim.lr_scheduler.CosineAnnealingWarmRestarts
  eta_min: 1e-6
