# =============================================================================
# DeepLabV3+ 模型配置
# =============================================================================
# DeepLabV3+是一个先进的语义分割模型，具有以下特点：
# - 使用空洞卷积(Atrous Convolution)捕获多尺度信息
# - 结合编码器-解码器结构，保持细节信息
# - 支持多种骨干网络(ResNet, ResNeXt, EfficientNet等)
# - 在多个语义分割基准测试中表现优异
#
# 适用场景：
# - 高精度要求的语义分割任务
# - 遥感图像分割
# - 医学图像分割
# - 自动驾驶场景理解
# =============================================================================

_target_: src.modules.segmentation_module.SegmentationModule

# =============================================================================
# 基础模型配置
# =============================================================================
model_name: deeplabv3plus                    # 模型架构名称，对应AVAILABLE_ARCHITECTURES中的键
name: DeepLabV3Plus                          # 模型显示名称，用于日志和WandB标记
num_classes: ${data.dataset_config.num_classes}  # 分割类别数，自动从数据配置中获取
ignore_index: 255                           # 忽略的标签索引，通常用于未标注区域

# =============================================================================
# 模型架构参数
# =============================================================================
model_params:
  backbone: resnet50                         # 骨干网络选择
                                             # 可选项: resnet50, resnet101, resnet152
                                             #        resnext50_32x4d, resnext101_32x8d
                                             #        efficientnet-b0 到 efficientnet-b7

  in_channels: 3                             # 输入图像通道数
                                             # 3: RGB图像
                                             # 4: RGBA图像或多光谱图像
                                             # 1: 灰度图像

  pretrained: true                           # 是否使用预训练权重
                                             # true: 使用ImageNet预训练权重，收敛更快
                                             # false: 随机初始化，适合特殊领域数据

# =============================================================================
# 损失函数配置引用
# =============================================================================
loss_cfg: ${loss}                            # 引用全局损失函数配置
                                             # 支持: cross_entropy, dice, focal, combined等

# =============================================================================
# 优化器配置 (内嵌配置，会被全局optimizer配置覆盖)
# =============================================================================
optimizer_cfg:
  _target_: torch.optim.AdamW                # 优化器类型：AdamW
  _partial_: true                            # 使用部分实例化，参数会在运行时传入

  lr: 0.001                                  # 学习率
                                             # DeepLabV3+推荐范围: 1e-5 到 1e-3

  weight_decay: 0.01                         # 权重衰减(L2正则化)
                                             # 防止过拟合，推荐范围: 1e-4 到 1e-2

  betas: [0.9, 0.999]                        # Adam动量参数
                                             # beta1: 一阶矩估计的衰减率
                                             # beta2: 二阶矩估计的衰减率

# =============================================================================
# 学习率调度器配置 (内嵌配置，会被全局scheduler配置覆盖)
# =============================================================================
scheduler_cfg:
  _target_: torch.optim.lr_scheduler.CosineAnnealingLR  # 余弦退火调度器

  T_max: 100                                 # 余弦退火的半周期长度
                                             # 通常设置为总训练epoch数

  eta_min: 1e-6                              # 最小学习率
                                             # 防止学习率降到过低影响训练
