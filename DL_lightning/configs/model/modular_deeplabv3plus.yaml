_target_: src.modules.segmentation_module.SegmentationModule
ignore_index: 255
loss_cfg: ${loss}
model_name: modular_deeplabv3plus
model_params:
  backbone: resnet50
  in_channels: 3
  plugins_config:
    channel_attention:
      enabled: true
      reduction: 16
    pyramid_pooling:
      enabled: true
      pool_sizes:
      - 1
      - 2
      - 3
      - 6
    spatial_attention:
      enabled: true
      kernel_size: 7
  pretrained: true
name: ModularAdaptiveDeepLabV3Plus
num_classes: ${data.dataset_config.num_classes}
optimizer_cfg:
  _partial_: true
  _target_: torch.optim.AdamW
  betas:
  - 0.9
  - 0.999
  lr: 0.0005
  weight_decay: 0.01
scheduler_cfg:
  T_0: 10
  T_mult: 2
  _target_: torch.optim.lr_scheduler.CosineAnnealingWarmRestarts
  eta_min: 1e-7
