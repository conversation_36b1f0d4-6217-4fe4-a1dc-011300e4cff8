#!/usr/bin/env python3
"""
多尺度训练方法性能对比测试

对比数据集级多尺度 vs 数据增强级多尺度的性能差异
"""

import time
import numpy as np
import cv2
import random
from pathlib import Path
import torch
from typing import Tuple, List
import albumentations as A


class DatasetLevelMultiScale:
    """数据集级多尺度实现"""
    
    def __init__(self, data_dir: Path):
        self.data_dir = data_dir
        self.scales = ['1_0.5', '1_1', '1_2']
        self.scale_weights = {'1_0.5': 0.1, '1_1': 0.7, '1_2': 0.2}
        
    def load_sample(self, filename: str) -> Tuple[np.ndarray, np.ndarray]:
        """加载预处理好的多尺度数据"""
        # 根据权重选择尺度
        scale = random.choices(
            list(self.scale_weights.keys()),
            weights=list(self.scale_weights.values())
        )[0]
        
        # 直接加载对应尺度的数据
        image_path = self.data_dir / f"images/scale_{scale}/train/{filename}"
        mask_path = self.data_dir / f"masks/scale_{scale}/train/{filename}"
        
        # 模拟文件加载（实际项目中是真实的文件I/O）
        if scale == '1_0.5':
            image = np.random.randint(0, 255, (256, 256, 3), dtype=np.uint8)
            mask = np.random.randint(0, 14, (256, 256), dtype=np.uint8)
        elif scale == '1_1':
            image = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
            mask = np.random.randint(0, 14, (512, 512), dtype=np.uint8)
        else:  # scale_1_2
            image = np.random.randint(0, 255, (1024, 1024, 3), dtype=np.uint8)
            mask = np.random.randint(0, 14, (1024, 1024), dtype=np.uint8)
            
        return image, mask


class AugmentationLevelMultiScale:
    """数据增强级多尺度实现"""
    
    def __init__(self, data_dir: Path, target_size: int = 512):
        self.data_dir = data_dir
        self.target_size = target_size
        self.scale_range = (0.5, 2.0)
        
        # 创建数据增强管道
        self.transform = A.Compose([
            A.RandomScale(scale_limit=(-0.5, 1.0), p=1.0),  # 0.5-2.0倍缩放
            A.PadIfNeeded(min_height=target_size, min_width=target_size, 
                         border_mode=cv2.BORDER_CONSTANT, value=0),
            A.RandomCrop(height=target_size, width=target_size, p=1.0),
        ])
    
    def load_sample(self, filename: str) -> Tuple[np.ndarray, np.ndarray]:
        """加载原始数据并进行多尺度增强"""
        # 加载原始数据（固定尺寸）
        image = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
        mask = np.random.randint(0, 14, (512, 512), dtype=np.uint8)
        
        # 应用多尺度变换
        transformed = self.transform(image=image, mask=mask)
        return transformed['image'], transformed['mask']


def benchmark_loading_speed(method, num_samples: int = 1000) -> dict:
    """测试数据加载速度"""
    print(f"\n🔄 测试 {method.__class__.__name__} 加载速度...")
    
    # 预热
    for _ in range(10):
        method.load_sample("dummy.tif")
    
    # 正式测试
    start_time = time.time()
    for i in range(num_samples):
        image, mask = method.load_sample(f"sample_{i:04d}.tif")
    end_time = time.time()
    
    total_time = end_time - start_time
    avg_time = total_time / num_samples
    
    return {
        'total_time': total_time,
        'avg_time_per_sample': avg_time,
        'samples_per_second': num_samples / total_time
    }


def benchmark_memory_usage(method, num_samples: int = 100) -> dict:
    """测试内存使用情况"""
    print(f"\n💾 测试 {method.__class__.__name__} 内存使用...")
    
    import psutil
    import os
    
    process = psutil.Process(os.getpid())
    
    # 测试前内存
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    # 加载数据
    samples = []
    for i in range(num_samples):
        image, mask = method.load_sample(f"sample_{i:04d}.tif")
        samples.append((image, mask))
    
    # 测试后内存
    peak_memory = process.memory_info().rss / 1024 / 1024  # MB
    memory_used = peak_memory - initial_memory
    
    # 计算平均样本大小
    avg_sample_size = memory_used / num_samples
    
    return {
        'initial_memory_mb': initial_memory,
        'peak_memory_mb': peak_memory,
        'memory_used_mb': memory_used,
        'avg_sample_size_mb': avg_sample_size
    }


def analyze_scale_distribution(method, num_samples: int = 1000) -> dict:
    """分析尺度分布"""
    print(f"\n📊 分析 {method.__class__.__name__} 尺度分布...")
    
    scale_counts = {}
    
    for i in range(num_samples):
        image, mask = method.load_sample(f"sample_{i:04d}.tif")
        
        # 根据图像尺寸推断尺度
        h, w = image.shape[:2]
        if h <= 300:
            scale = "small (≤300px)"
        elif h <= 600:
            scale = "medium (300-600px)"
        else:
            scale = "large (>600px)"
            
        scale_counts[scale] = scale_counts.get(scale, 0) + 1
    
    # 计算分布比例
    total = sum(scale_counts.values())
    scale_distribution = {k: v/total for k, v in scale_counts.items()}
    
    return {
        'scale_counts': scale_counts,
        'scale_distribution': scale_distribution
    }


def compare_data_quality(method, num_samples: int = 100) -> dict:
    """比较数据质量"""
    print(f"\n🔍 分析 {method.__class__.__name__} 数据质量...")
    
    image_qualities = []
    mask_qualities = []
    
    for i in range(num_samples):
        image, mask = method.load_sample(f"sample_{i:04d}.tif")
        
        # 图像质量指标
        image_std = np.std(image)  # 标准差，反映图像细节丰富度
        image_mean = np.mean(image)  # 平均亮度
        
        # 标签质量指标
        unique_classes = len(np.unique(mask))  # 类别数量
        mask_entropy = -np.sum([p * np.log2(p + 1e-8) for p in 
                               np.bincount(mask.flatten()) / mask.size])  # 熵
        
        image_qualities.append({
            'std': image_std,
            'mean': image_mean,
            'size': image.shape
        })
        
        mask_qualities.append({
            'unique_classes': unique_classes,
            'entropy': mask_entropy,
            'size': mask.shape
        })
    
    return {
        'image_quality': {
            'avg_std': np.mean([q['std'] for q in image_qualities]),
            'avg_mean': np.mean([q['mean'] for q in image_qualities]),
            'size_variety': len(set([str(q['size']) for q in image_qualities]))
        },
        'mask_quality': {
            'avg_unique_classes': np.mean([q['unique_classes'] for q in mask_qualities]),
            'avg_entropy': np.mean([q['entropy'] for q in mask_qualities]),
            'size_variety': len(set([str(q['size']) for q in mask_qualities]))
        }
    }


def main():
    """主测试函数"""
    print("🚀 多尺度训练方法性能对比测试")
    print("=" * 60)
    
    # 创建测试实例
    data_dir = Path("/tmp/dummy_data")
    dataset_method = DatasetLevelMultiScale(data_dir)
    augmentation_method = AugmentationLevelMultiScale(data_dir)
    
    methods = [
        ("数据集级多尺度", dataset_method),
        ("数据增强级多尺度", augmentation_method)
    ]
    
    results = {}
    
    for method_name, method in methods:
        print(f"\n{'='*20} {method_name} {'='*20}")
        
        # 性能测试
        speed_result = benchmark_loading_speed(method, num_samples=1000)
        memory_result = benchmark_memory_usage(method, num_samples=100)
        scale_result = analyze_scale_distribution(method, num_samples=1000)
        quality_result = compare_data_quality(method, num_samples=100)
        
        results[method_name] = {
            'speed': speed_result,
            'memory': memory_result,
            'scale_distribution': scale_result,
            'data_quality': quality_result
        }
    
    # 生成对比报告
    print("\n" + "="*60)
    print("📊 性能对比报告")
    print("="*60)
    
    print("\n🚀 加载速度对比:")
    for method_name in results:
        speed = results[method_name]['speed']
        print(f"{method_name:20}: {speed['samples_per_second']:.1f} samples/sec")
    
    print("\n💾 内存使用对比:")
    for method_name in results:
        memory = results[method_name]['memory']
        print(f"{method_name:20}: {memory['avg_sample_size_mb']:.2f} MB/sample")
    
    print("\n📊 尺度分布对比:")
    for method_name in results:
        distribution = results[method_name]['scale_distribution']['scale_distribution']
        print(f"{method_name}:")
        for scale, ratio in distribution.items():
            print(f"  {scale}: {ratio:.1%}")
    
    print("\n🔍 数据质量对比:")
    for method_name in results:
        quality = results[method_name]['data_quality']
        print(f"{method_name}:")
        print(f"  图像细节丰富度: {quality['image_quality']['avg_std']:.1f}")
        print(f"  尺寸多样性: {quality['image_quality']['size_variety']} 种")
        print(f"  标签熵: {quality['mask_quality']['avg_entropy']:.2f}")
    
    return results


if __name__ == "__main__":
    results = main()
