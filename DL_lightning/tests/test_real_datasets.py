#!/usr/bin/env python3
"""
使用真实数据集进行测试
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from omegaconf import OmegaConf
import torch

def test_suide_real_data():
    """测试SuiDe真实数据"""
    
    print("🔍 测试SuiDe真实数据集...")
    
    try:
        from src.data.suide_datamodule import SuiDeDataModule
        
        # 使用真实的SuiDe数据路径，参考configs/data/suide_v2.1.yaml
        config = OmegaConf.create({
            'dataset_config': {
                'data_dir': "data/Data_SRC/Dataset_v2.2",
                'class_info_path': "data/Data_SRC/Dataset_v2.2/metadata/class_info.json",
                'training_level': 2,
                'num_classes': 14,  # 明确指定类别数量，与配置文件一致
                'ignore_index': 255,
                'scale_weights': {
                    "1_1": 0.7,
                    "1_2": 0.2,
                    "1_0.5": 0.1
                }
            },
            'transform_config': None,  # 不使用transform，直接测试原始数据
            'dataloader_config': {'batch_size': 2, 'num_workers': 0}
        })
        
        # 创建DataModule
        dm = SuiDeDataModule(
            dataset_config=config.dataset_config,
            transform_config=config.transform_config,
            dataloader_config=config.dataloader_config
        )
        
        print("✅ SuiDe DataModule创建成功")
        
        # Setup
        dm.setup('fit')
        
        print("✅ SuiDe DataModule setup成功")
        print(f"  训练集样本数: {len(dm.train_dataset)}")
        print(f"  验证集样本数: {len(dm.val_dataset)}")
        print(f"  类别数量: {dm.num_classes}")
        
        # 测试训练数据加载
        train_loader = dm.train_dataloader()
        train_batch = next(iter(train_loader))
        
        print("✅ SuiDe训练数据加载成功")
        print(f"  Batch keys: {list(train_batch.keys())}")
        print(f"  Image shape: {train_batch['image'].shape}")
        print(f"  Mask shape: {train_batch['mask'].shape}")
        print(f"  Image dtype: {train_batch['image'].dtype}")
        print(f"  Mask dtype: {train_batch['mask'].dtype}")
        print(f"  Image range: [{train_batch['image'].min():.3f}, {train_batch['image'].max():.3f}]")
        print(f"  Mask unique values: {torch.unique(train_batch['mask']).tolist()}")
        
        # 测试验证数据加载
        val_loader = dm.val_dataloader()
        val_batch = next(iter(val_loader))
        
        print("✅ SuiDe验证数据加载成功")
        print(f"  Val Image shape: {val_batch['image'].shape}")
        print(f"  Val Mask shape: {val_batch['mask'].shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ SuiDe测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_loveda_real_data():
    """测试LoveDA真实数据"""
    
    print("\n🔍 测试LoveDA真实数据集...")
    
    try:
        from src.data.loveda_datamodule import LoveDADataModule
        
        # 使用真实的LoveDA数据路径
        config = OmegaConf.create({
            'dataset_config': {
                'data_dir': "data/LoveDA",
                'num_classes': 7
            },
            'transform_config': None,  # 不使用transform，直接测试原始数据
            'dataloader_config': {'batch_size': 2, 'num_workers': 0}
        })
        
        # 创建DataModule
        dm = LoveDADataModule(
            dataset_config=config.dataset_config,
            transform_config=config.transform_config,
            dataloader_config=config.dataloader_config
        )
        
        print("✅ LoveDA DataModule创建成功")
        
        # Setup
        dm.setup('fit')
        
        print("✅ LoveDA DataModule setup成功")
        print(f"  训练集样本数: {len(dm.train_dataset)}")
        print(f"  验证集样本数: {len(dm.val_dataset)}")
        print(f"  类别数量: {dm.num_classes}")
        
        # 测试训练数据加载
        train_loader = dm.train_dataloader()
        train_batch = next(iter(train_loader))
        
        print("✅ LoveDA训练数据加载成功")
        print(f"  Batch keys: {list(train_batch.keys())}")
        print(f"  Image shape: {train_batch['image'].shape}")
        print(f"  Mask shape: {train_batch['mask'].shape}")
        print(f"  Image dtype: {train_batch['image'].dtype}")
        print(f"  Mask dtype: {train_batch['mask'].dtype}")
        print(f"  Image range: [{train_batch['image'].min():.3f}, {train_batch['image'].max():.3f}]")
        print(f"  Mask unique values: {torch.unique(train_batch['mask']).tolist()}")
        
        # 测试验证数据加载
        val_loader = dm.val_dataloader()
        val_batch = next(iter(val_loader))
        
        print("✅ LoveDA验证数据加载成功")
        print(f"  Val Image shape: {val_batch['image'].shape}")
        print(f"  Val Mask shape: {val_batch['mask'].shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ LoveDA测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_suide_auto_detect():
    """测试SuiDe自动检测类别数量"""

    print("\n🔍 测试SuiDe自动检测模式...")

    try:
        from src.data.suide_datamodule import SuiDeDataModule

        # 使用自动检测模式
        config = OmegaConf.create({
            'dataset_config': {
                'data_dir': "data/Data_SRC/Dataset_v2.2",
                'class_info_path': "data/Data_SRC/Dataset_v2.2/metadata/class_info.json",
                'training_level': 2,
                'auto_detect_classes': True,  # 启用自动检测
                'ignore_index': 255,
                'scale_weights': {
                    "1_1": 0.5,
                    "1_2": 0.3,
                    "1_0.5": 0.2
                }
            },
            'transform_config': None,
            'dataloader_config': {'batch_size': 1, 'num_workers': 0}
        })

        # 创建DataModule
        dm = SuiDeDataModule(
            dataset_config=config.dataset_config,
            transform_config=config.transform_config,
            dataloader_config=config.dataloader_config
        )

        print("✅ SuiDe自动检测DataModule创建成功")

        # Setup
        dm.setup('fit')

        print("✅ SuiDe自动检测setup成功")
        print(f"  自动检测的类别数量: {dm.num_classes}")

        return True

    except Exception as e:
        print(f"❌ SuiDe自动检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_datasets():
    """对比两个数据集的特征"""
    
    print("\n🔍 对比SuiDe和LoveDA数据集特征...")
    
    try:
        # 简单的数据集信息对比
        print("📊 数据集对比:")
        print("  SuiDe:")
        print("    - 多尺度遥感图像分割")
        print("    - 14个类别（二级分类）")
        print("    - 512×512像素")
        print("    - 支持多尺度权重采样")
        print("  LoveDA:")
        print("    - 标准遥感图像分割")
        print("    - 7个类别（土地覆盖）")
        print("    - 1024×1024像素")
        print("    - Urban/Rural域分离")
        
        return True
        
    except Exception as e:
        print(f"❌ 对比失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始真实数据集测试...")
    
    success1 = test_suide_real_data()
    success2 = test_loveda_real_data()
    success3 = test_suide_auto_detect()
    success4 = compare_datasets()

    if success1 and success2 and success3 and success4:
        print("\n🎉 真实数据集测试全部成功！")
        print("📋 测试总结:")
        print("  ✅ SuiDe数据集正常工作")
        print("  ✅ LoveDA数据集正常工作")
        print("  ✅ 两个数据集格式标准化")
        print("  ✅ 数据加载管道完整")
    else:
        print("\n💥 部分测试失败！")
        if not success1:
            print("  ❌ SuiDe数据集测试失败")
        if not success2:
            print("  ❌ LoveDA数据集测试失败")
        if not success3:
            print("  ❌ 数据集对比失败")
