#!/usr/bin/env python3
"""
准确的多尺度数据分析

基于代码逻辑分析SuiDe数据集多尺度的真实情况
"""

import rasterio
import numpy as np
from pathlib import Path
import json


def analyze_multiscale_reality():
    """分析多尺度数据的真实情况"""
    
    print("🔍 SuiDe数据集多尺度真实情况分析")
    print("=" * 60)
    
    # 基于代码的理论分析
    print("\n📋 基于代码逻辑的理论分析:")
    print("-" * 40)
    
    # 比例尺映射（来自代码）
    SCALE_MAPPING = {
        "scale_1_1": {"factor": 1.0, "description": "原始分辨率"},
        "scale_1_2": {"factor": 0.5, "description": "下采样"},
        "scale_1_0.5": {"factor": 2.0, "description": "上采样"}
    }
    
    tile_size = 512  # 输出切片大小
    base_resolution = 2.0  # 原始分辨率 2m/pixel
    
    print(f"原始影像分辨率: {base_resolution}m/pixel")
    print(f"输出切片尺寸: {tile_size}×{tile_size} 像素")
    print()
    
    for scale_name, scale_info in SCALE_MAPPING.items():
        factor = scale_info["factor"]
        
        # 计算原始窗口大小（代码第141行逻辑）
        window_size = int(tile_size / factor)
        
        # 计算地面覆盖范围
        ground_coverage = window_size * base_resolution
        
        # 计算有效分辨率（地面覆盖/输出像素）
        effective_resolution = ground_coverage / tile_size
        
        print(f"{scale_name}:")
        print(f"  scale_factor: {factor}")
        print(f"  原始窗口: {window_size}×{window_size} 像素")
        print(f"  地面覆盖: {ground_coverage}m × {ground_coverage}m")
        print(f"  有效分辨率: {effective_resolution:.1f}m/pixel")
        print(f"  覆盖面积: {(ground_coverage/1000)**2:.3f} km²")
        print()


def analyze_actual_files():
    """分析实际文件的情况"""
    
    print("📁 实际文件分析:")
    print("-" * 40)
    
    base_path = Path("/home/<USER>/DeepLearing/SuiDe_Project/data/Data_SRC/Dataset_v2.1")
    
    # 查找每个尺度的示例文件
    scales = ["scale_1_1", "scale_1_2", "scale_1_0.5"]
    
    for scale in scales:
        scale_dir = base_path / "images" / scale / "train"
        
        if scale_dir.exists():
            # 获取第一个文件
            files = list(scale_dir.glob("*.tif"))
            if files:
                file_path = files[0]
                
                try:
                    with rasterio.open(file_path) as src:
                        # 计算实际地面覆盖
                        bounds = src.bounds
                        width_m = bounds.right - bounds.left
                        height_m = bounds.top - bounds.bottom
                        
                        # 计算实际分辨率
                        pixel_size_x = width_m / src.width
                        pixel_size_y = height_m / src.height
                        
                        print(f"{scale}:")
                        print(f"  文件: {file_path.name}")
                        print(f"  图像尺寸: {src.width}×{src.height} 像素")
                        print(f"  地面覆盖: {width_m:.1f}m × {height_m:.1f}m")
                        print(f"  实际分辨率: {pixel_size_x:.2f}m/pixel × {pixel_size_y:.2f}m/pixel")
                        print(f"  地理变换: {[f'{x:.2f}' for x in src.transform.to_gdal()]}")
                        print()
                        
                except Exception as e:
                    print(f"{scale}: 无法读取文件 - {e}")
                    print()
            else:
                print(f"{scale}: 目录中无文件")
                print()
        else:
            print(f"{scale}: 目录不存在")
            print()


def analyze_annotation_data():
    """分析annotation文件中的信息"""
    
    print("📄 Annotation文件分析:")
    print("-" * 40)
    
    annotation_path = Path("/home/<USER>/DeepLearing/SuiDe_Project/data/Data_SRC/Dataset_v2.1/annotations/train.json")
    
    if annotation_path.exists():
        with open(annotation_path, 'r') as f:
            data = json.load(f)
        
        # 按尺度分组分析
        scale_samples = {}
        for tile in data['tiles'][:10]:  # 只分析前10个样本
            scale = tile.get('scale', 'unknown')
            if scale not in scale_samples:
                scale_samples[scale] = []
            scale_samples[scale].append(tile)
        
        for scale, samples in scale_samples.items():
            if samples:
                sample = samples[0]
                gt = sample.get('geo_transform', [])
                
                if len(gt) >= 6:
                    # 从地理变换计算像素分辨率
                    pixel_size_x = abs(gt[1])
                    pixel_size_y = abs(gt[5])
                    
                    # 计算地面覆盖（基于图像尺寸和像素分辨率）
                    width = sample.get('width', 512)
                    height = sample.get('height', 512)
                    ground_width = width * pixel_size_x
                    ground_height = height * pixel_size_y
                    
                    print(f"{scale}:")
                    print(f"  样本ID: {sample['id']}")
                    print(f"  图像尺寸: {width}×{height} 像素")
                    print(f"  地理变换像素分辨率: {pixel_size_x:.2f}m × {pixel_size_y:.2f}m")
                    print(f"  计算地面覆盖: {ground_width:.1f}m × {ground_height:.1f}m")
                    print()
    else:
        print("annotation文件不存在")
        print()


def compare_theory_vs_reality():
    """对比理论与实际"""
    
    print("⚖️  理论 vs 实际对比:")
    print("-" * 40)
    
    print("理论预期（基于代码逻辑）:")
    print("- scale_1_1: 1024m×1024m, 2.0m/pixel")
    print("- scale_1_2: 2048m×2048m, 4.0m/pixel") 
    print("- scale_1_0.5: 512m×512m, 1.0m/pixel")
    print()
    
    print("可能的实际情况:")
    print("1. 如果地理变换参数正确保存:")
    print("   - 应该看到不同的像素分辨率")
    print("   - 应该看到不同的地面覆盖范围")
    print()
    print("2. 如果地理变换参数保存有误:")
    print("   - 所有尺度显示相同的分辨率")
    print("   - 但实际像素内容代表不同的地面范围")
    print()


def main():
    """主函数"""
    
    # 理论分析
    analyze_multiscale_reality()
    
    # 实际文件分析
    analyze_actual_files()
    
    # Annotation分析
    analyze_annotation_data()
    
    # 对比分析
    compare_theory_vs_reality()
    
    print("🎯 结论:")
    print("-" * 40)
    print("基于代码分析，SuiDe的多尺度实现应该提供:")
    print("1. 不同的地面覆盖范围")
    print("2. 不同的有效分辨率") 
    print("3. 统一的512×512输出尺寸")
    print()
    print("如果实际测试显示所有尺度参数相同，")
    print("说明地理变换参数保存时可能有问题，")
    print("但像素内容本身应该是正确的多尺度数据。")


if __name__ == "__main__":
    main()
