#!/usr/bin/env python3
"""
测试修改后的LoveDA DataModule使用列表文件的实现
"""

import sys
from pathlib import Path

# 确保可以导入src模块
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))


def test_loveda_list_file_loading():
    """测试LoveDA使用列表文件加载"""
    print("🧪 测试LoveDA列表文件加载")
    print("-" * 50)
    
    try:
        import hydra
        from omegaconf import OmegaConf
        
        # 加载LoveDA配置
        config_path = project_root / 'configs/data/loveda.yaml'
        config = OmegaConf.load(config_path)
        
        # 实例化DataModule
        datamodule = hydra.utils.instantiate(config)
        datamodule.setup('fit')
        
        # 获取数据加载器
        train_loader = datamodule.train_dataloader()
        val_loader = datamodule.val_dataloader()
        
        print(f"✅ LoveDA DataModule实例化成功")
        print(f"   训练集大小: {len(datamodule.train_dataset)}")
        print(f"   验证集大小: {len(datamodule.val_dataset)}")
        
        # 测试数据加载
        train_batch = next(iter(train_loader))
        val_batch = next(iter(val_loader))
        
        print(f"✅ 数据加载成功")
        print(f"   训练批次形状: image {train_batch['image'].shape}, mask {train_batch['mask'].shape}")
        print(f"   验证批次形状: image {val_batch['image'].shape}, mask {val_batch['mask'].shape}")
        
        # 检查是否使用了列表文件
        train_dataset = datamodule.train_dataset
        if hasattr(train_dataset, 'samples'):
            sample = train_dataset.samples[0]
            print(f"✅ 样本路径示例:")
            print(f"   图像: {sample['image']}")
            print(f"   标签: {sample['mask']}")
            print(f"   域: {sample['domain']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_list_file_vs_directory_scan():
    """对比列表文件和目录扫描的结果"""
    print("\n🧪 对比列表文件和目录扫描结果")
    print("-" * 50)
    
    try:
        from src.data.loveda_datamodule import LoveDADataModule
        from pathlib import Path
        
        data_dir = Path("/home/<USER>/DeepLearing/SuiDe_Project/data/LoveDA")
        
        # 创建使用列表文件的数据集
        from omegaconf import DictConfig

        datamodule = LoveDADataModule(
            dataset_config=DictConfig({
                'data_dir': str(data_dir),
                'num_classes': 7,
                'ignore_index': 255,
                'include_background': True,
                'ignore_nodata': True
            }),
            dataloader_config=DictConfig({
                'batch_size': 8,
                'num_workers': 0,
                'pin_memory': True,
                'persistent_workers': False
            }),
            transform_config=DictConfig({
                'image_size': 1024,
                'mean': [0.485, 0.456, 0.406],
                'std': [0.229, 0.224, 0.225]
            })
        )
        datamodule.setup('fit')
        dataset_list = datamodule.train_dataset
        
        print(f"使用列表文件加载的样本数: {len(dataset_list)}")
        
        # 验证前几个样本
        for i in range(min(5, len(dataset_list))):
            sample = dataset_list.samples[i]
            print(f"样本 {i}: {sample['image'].name} -> {sample['mask'].name} ({sample['domain']})")
        
        return True
        
    except Exception as e:
        print(f"❌ 对比测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n🧪 测试错误处理")
    print("-" * 50)
    
    try:
        from src.data.loveda_datamodule import LoveDADataModule
        from pathlib import Path
        
        # 测试不存在的数据目录
        fake_data_dir = Path("/tmp/fake_loveda")
        
        try:
            from omegaconf import DictConfig

            datamodule = LoveDADataModule(
                dataset_config=DictConfig({
                    'data_dir': str(fake_data_dir),
                    'num_classes': 7,
                    'ignore_index': 255,
                    'include_background': True,
                    'ignore_nodata': True
                }),
                dataloader_config=DictConfig({
                    'batch_size': 8,
                    'num_workers': 0,
                    'pin_memory': True,
                    'persistent_workers': False
                }),
                transform_config=DictConfig({
                    'image_size': 1024,
                    'mean': [0.485, 0.456, 0.406],
                    'std': [0.229, 0.224, 0.225]
                })
            )
            datamodule.setup('fit')
            print("❌ 应该抛出异常但没有")
            return False
        except Exception as e:
            print(f"✅ 正确处理了不存在的数据目录: {type(e).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 测试修改后的LoveDA DataModule")
    print("=" * 60)
    
    tests = [
        ("列表文件加载", test_loveda_list_file_loading),
        ("列表文件vs目录扫描", test_list_file_vs_directory_scan),
        ("错误处理", test_error_handling)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！")
        print("\n✨ 修改成果:")
        print("  ✅ LoveDA现在优先使用官方列表文件")
        print("  ✅ 提供了目录扫描作为备用方案")
        print("  ✅ 增加了文件存在性验证")
        print("  ✅ 提供了详细的错误处理和日志")
        print("  ✅ 保持了与旧版本的兼容性")
        
        return True
    else:
        print(f"\n❌ 有 {total - passed} 个测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
