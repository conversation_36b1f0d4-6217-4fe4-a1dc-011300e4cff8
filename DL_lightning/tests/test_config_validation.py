#!/usr/bin/env python3
"""
配置验证测试

验证修复后的配置文件和DataModule能够正常加载和实例化
"""

import sys
import traceback
from pathlib import Path

# 确保可以导入src模块
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))


def test_config_loading():
    """测试配置文件加载"""
    print("🧪 测试配置文件加载")
    print("-" * 50)
    
    try:
        from omegaconf import OmegaConf
        
        # 测试SuiDe配置加载
        suide_config_path = project_root / 'configs/data/suide_v2.1_new.yaml'
        suide_config = OmegaConf.load(suide_config_path)
        print(f"✅ SuiDe配置加载成功")
        print(f"   - 目标类: {suide_config._target_}")
        print(f"   - 数据目录: {suide_config.dataset_config.data_dir}")
        print(f"   - 类别数量: {suide_config.dataset_config.num_classes}")
        print(f"   - 训练级别: {suide_config.dataset_config.training_level}")
        print(f"   - 多尺度: {suide_config.dataset_config.use_multiscale}")
        
        # 测试LoveDA配置加载
        loveda_config_path = project_root / 'configs/data/loveda.yaml'
        loveda_config = OmegaConf.load(loveda_config_path)
        print(f"✅ LoveDA配置加载成功")
        print(f"   - 目标类: {loveda_config._target_}")
        print(f"   - 数据目录: {loveda_config.dataset_config.data_dir}")
        print(f"   - 类别数量: {loveda_config.dataset_config.num_classes}")
        print(f"   - ignore_index: {loveda_config.dataset_config.ignore_index}")
        print(f"   - 包含背景类: {loveda_config.dataset_config.include_background}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        traceback.print_exc()
        return False


def test_datamodule_import():
    """测试DataModule导入"""
    print("\n🧪 测试DataModule导入")
    print("-" * 50)
    
    try:
        # 测试SuiDe DataModule导入
        from src.data.suide_datamodule import SuiDeDataModule
        print("✅ SuiDeDataModule导入成功")
        
        # 测试LoveDA DataModule导入
        from src.data.loveda_datamodule import LoveDADataModule
        print("✅ LoveDADataModule导入成功")
        
        # 测试统一导入接口
        from src.data import SuiDeDataModule as SuiDe_Unified
        from src.data import LoveDADataModule as LoveDA_Unified
        print("✅ 统一导入接口正常")
        
        return True
        
    except Exception as e:
        print(f"❌ DataModule导入失败: {e}")
        traceback.print_exc()
        return False


def test_hydra_instantiation():
    """测试Hydra实例化（使用虚拟路径）"""
    print("\n🧪 测试Hydra实例化")
    print("-" * 50)
    
    try:
        import hydra
        from omegaconf import OmegaConf
        
        # 测试SuiDe实例化（使用虚拟路径）
        suide_config_path = project_root / 'configs/data/suide_v2.1_new.yaml'
        suide_config = OmegaConf.load(suide_config_path)
        
        # 修改为虚拟路径避免文件不存在错误
        suide_config.dataset_config.data_dir = "/tmp/dummy_suide"
        suide_config.dataset_config.class_info_path = "/tmp/dummy_suide/class_info.json"
        
        suide_dm = hydra.utils.instantiate(suide_config)
        print(f"✅ SuiDe DataModule实例化成功: {type(suide_dm).__name__}")
        
        # 测试LoveDA实例化（使用虚拟路径）
        loveda_config_path = project_root / 'configs/data/loveda.yaml'
        loveda_config = OmegaConf.load(loveda_config_path)
        
        # 修改为虚拟路径
        loveda_config.dataset_config.data_dir = "/tmp/dummy_loveda"
        
        loveda_dm = hydra.utils.instantiate(loveda_config)
        print(f"✅ LoveDA DataModule实例化成功: {type(loveda_dm).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ Hydra实例化失败: {e}")
        traceback.print_exc()
        return False


def test_transforms_config():
    """测试transforms配置"""
    print("\n🧪 测试transforms配置")
    print("-" * 50)
    
    try:
        from src.data.components.transforms import create_transforms_from_hydra_config
        from omegaconf import DictConfig
        import numpy as np
        
        # 创建测试配置
        transform_config = DictConfig({
            'image_size': 512,
            'mean': [0.485, 0.456, 0.406],
            'std': [0.229, 0.224, 0.225],
            'augmentation': {
                'flip': True,
                'random_rotate90': True,
                'transpose': True,
                'random_brightness_contrast': True,
                'brightness_limit': 0.2,
                'contrast_limit': 0.2,
                'cutout': True,
                'cutout_max_size': 0.1
            }
        })
        
        # 测试训练变换
        train_transform = create_transforms_from_hydra_config(transform_config, 'train')
        print("✅ 训练变换创建成功")
        
        # 测试验证变换
        val_transform = create_transforms_from_hydra_config(transform_config, 'val')
        print("✅ 验证变换创建成功")
        
        # 测试变换应用
        dummy_image = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
        dummy_mask = np.random.randint(0, 10, (512, 512), dtype=np.uint8)
        
        train_result = train_transform(image=dummy_image, mask=dummy_mask)
        print(f"✅ 训练变换应用成功: image {train_result['image'].shape}, mask {train_result['mask'].shape}")
        
        val_result = val_transform(image=dummy_image, mask=dummy_mask)
        print(f"✅ 验证变换应用成功: image {val_result['image'].shape}, mask {val_result['mask'].shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ transforms配置测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始配置验证测试")
    print("=" * 60)
    
    tests = [
        ("配置文件加载", test_config_loading),
        ("DataModule导入", test_datamodule_import),
        ("Hydra实例化", test_hydra_instantiation),
        ("transforms配置", test_transforms_config)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {e}")
    
    print(f"\n📊 配置验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有配置验证测试通过！")
        print("\n✨ 修复成果:")
        print("  ✅ 类别数量配置已添加")
        print("  ✅ 硬编码路径已修复为相对路径")
        print("  ✅ LoveDA ignore_index配置已修复")
        print("  ✅ transforms配置使用正常")
        print("  ✅ 导入问题已解决")
        print("  ✅ Hydra实例化正常工作")
        
        return True
    else:
        print(f"\n❌ 有 {total - passed} 个测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
