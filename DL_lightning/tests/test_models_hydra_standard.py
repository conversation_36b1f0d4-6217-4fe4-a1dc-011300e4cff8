#!/usr/bin/env python3
"""
模型模块Hydra标准测试
严格遵循Hydra框架标准，不使用OmegaConf.create()

测试范围：
1. 使用DictConfig而非OmegaConf.create()
2. 使用hydra.utils.instantiate()标准方式
3. 验证模型在Hydra环境中的正确工作
4. 测试模型与损失函数、优化器、调度器的集成
"""

import sys
from pathlib import Path
import pytest
import warnings

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import torch
import hydra
from hydra.core.global_hydra import GlobalHydra
from omegaconf import DictConfig

# 导入模块
from src.modules.segmentation_module import SegmentationModule


class TestModelsHydraStandard:
    """模型模块Hydra标准测试"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        GlobalHydra.instance().clear()
    
    def teardown_method(self):
        """每个测试方法后的清理"""
        GlobalHydra.instance().clear()
    
    def test_hydra_model_instantiation(self):
        """使用Hydra标准方式实例化模型"""
        
        # ✅ 正确方式：使用DictConfig
        model_config = DictConfig({
            '_target_': 'src.modules.segmentation_module.SegmentationModule',
            'model_name': 'deeplabv3plus',
            'model_params': {
                'backbone': 'resnet50',
                'in_channels': 3,
                'pretrained': False
            },
            'optimizer_cfg': {
                '_target_': 'torch.optim.AdamW',
                'lr': 0.001,
                'weight_decay': 0.01
            },
            'scheduler_cfg': {
                '_target_': 'torch.optim.lr_scheduler.CosineAnnealingLR',
                'T_max': 10,
                'eta_min': 1e-6
            },
            'loss_cfg': {
                '_target_': 'src.losses.cross_entropy_loss.CrossEntropyLoss',
                'ignore_index': 255,
                'num_classes': 7
            },
            'num_classes': 7,
            'ignore_index': 255
        })
        
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", message=".*self.trainer.*")
            
            # 使用Hydra标准方式实例化
            model = hydra.utils.instantiate(model_config, _recursive_=False)
            
            assert model is not None, "Hydra模型实例化失败"
            assert isinstance(model, SegmentationModule), "不是SegmentationModule实例"
            
            # 验证模型组件
            assert hasattr(model, 'architecture'), "模型缺少网络架构"
            assert hasattr(model, 'loss_fn'), "模型缺少损失函数"
            
            print("✅ Hydra标准模型实例化测试通过")
    
    def test_all_model_architectures_with_hydra(self):
        """使用Hydra标准方式测试所有模型架构"""
        
        # 定义所有模型架构的Hydra配置
        model_configs = {
            'deeplabv3plus': DictConfig({
                '_target_': 'src.modules.segmentation_module.SegmentationModule',
                'model_name': 'deeplabv3plus',
                'model_params': {
                    'backbone': 'resnet50',
                    'in_channels': 3,
                    'pretrained': False
                },
                'optimizer_cfg': {
                    '_target_': 'torch.optim.AdamW',
                    'lr': 0.001
                },
                'scheduler_cfg': {
                    '_target_': 'torch.optim.lr_scheduler.StepLR',
                    'step_size': 10
                },
                'loss_cfg': {
                    '_target_': 'src.losses.cross_entropy_loss.CrossEntropyLoss',
                    'ignore_index': 255,
                    'num_classes': 7
                },
                'num_classes': 7,
                'ignore_index': 255
            }),
            'unet': DictConfig({
                '_target_': 'src.modules.segmentation_module.SegmentationModule',
                'model_name': 'unet',
                'model_params': {
                    'in_channels': 3,
                    'num_classes': 7,
                    'pretrained': False
                },
                'optimizer_cfg': {
                    '_target_': 'torch.optim.Adam',
                    'lr': 0.001
                },
                'scheduler_cfg': {
                    '_target_': 'torch.optim.lr_scheduler.StepLR',
                    'step_size': 10
                },
                'loss_cfg': {
                    '_target_': 'src.losses.dice_loss.DiceLoss',
                    'num_classes': 7
                },
                'num_classes': 7
            }),
            'unetpp': DictConfig({
                '_target_': 'src.modules.segmentation_module.SegmentationModule',
                'model_name': 'unetpp',
                'model_params': {
                    'in_channels': 3,
                    'num_classes': 7,
                    'pretrained': False
                },
                'optimizer_cfg': {
                    '_target_': 'torch.optim.AdamW',
                    'lr': 0.001
                },
                'scheduler_cfg': {
                    '_target_': 'torch.optim.lr_scheduler.CosineAnnealingLR',
                    'T_max': 10
                },
                'loss_cfg': {
                    '_target_': 'src.losses.focal_loss.FocalLoss',
                    'num_classes': 7,
                    'alpha': 1.0,
                    'gamma': 2.0
                },
                'num_classes': 7
            }),
            'swin_unet': DictConfig({
                '_target_': 'src.modules.segmentation_module.SegmentationModule',
                'model_name': 'swin_unet',
                'model_params': {
                    'in_channels': 3,
                    'num_classes': 7,
                    'pretrained': False
                },
                'optimizer_cfg': {
                    '_target_': 'torch.optim.AdamW',
                    'lr': 0.001
                },
                'scheduler_cfg': {
                    '_target_': 'torch.optim.lr_scheduler.StepLR',
                    'step_size': 10
                },
                'loss_cfg': {
                    '_target_': 'src.losses.combined_loss.CombinedLoss',
                    'loss_weights': {'ce': 0.5, 'dice': 0.5},
                    'num_classes': 7
                },
                'num_classes': 7
            })
        }
        
        results = {}
        
        for model_name, model_cfg in model_configs.items():
            try:
                with warnings.catch_warnings():
                    warnings.filterwarnings("ignore", message=".*self.trainer.*")
                    
                    # 使用Hydra标准方式实例化
                    model = hydra.utils.instantiate(model_cfg, _recursive_=False)
                    
                    assert model is not None, f"模型实例化失败: {model_name}"
                    assert isinstance(model, SegmentationModule), f"不是SegmentationModule: {model_name}"
                    
                    # 测试前向传播
                    test_batch = {
                        'image': torch.randn(2, 3, 128, 128),
                        'mask': torch.randint(0, 7, (2, 128, 128))
                    }
                    
                    model.train()
                    train_loss = model.training_step(test_batch, 0)
                    
                    assert isinstance(train_loss, torch.Tensor), f"训练步骤失败: {model_name}"
                    assert not torch.isnan(train_loss), f"训练损失为NaN: {model_name}"
                    
                    # 测试验证步骤
                    model.eval()
                    with torch.no_grad():
                        val_result = model.validation_step(test_batch, 0)
                    
                    results[model_name] = train_loss.item()
                    print(f"✅ {model_name}: 训练损失 = {train_loss.item():.4f}")
                    
            except Exception as e:
                pytest.fail(f"模型 {model_name} 测试失败: {e}")
        
        # 验证所有模型都成功测试
        assert len(results) == len(model_configs), "部分模型测试失败"
        
        print(f"✅ 所有 {len(results)} 个模型架构Hydra标准测试通过")
    
    def test_hydra_optimizer_scheduler_integration(self):
        """测试Hydra标准方式的优化器和调度器集成"""
        
        # 测试不同优化器和调度器组合
        optimizer_scheduler_configs = [
            {
                'name': 'AdamW + CosineAnnealing',
                'optimizer': DictConfig({
                    '_target_': 'torch.optim.AdamW',
                    'lr': 0.001,
                    'weight_decay': 0.01
                }),
                'scheduler': DictConfig({
                    '_target_': 'torch.optim.lr_scheduler.CosineAnnealingLR',
                    'T_max': 10,
                    'eta_min': 1e-6
                })
            },
            {
                'name': 'Adam + StepLR',
                'optimizer': DictConfig({
                    '_target_': 'torch.optim.Adam',
                    'lr': 0.001
                }),
                'scheduler': DictConfig({
                    '_target_': 'torch.optim.lr_scheduler.StepLR',
                    'step_size': 10,
                    'gamma': 0.1
                })
            },
            {
                'name': 'SGD + ExponentialLR',
                'optimizer': DictConfig({
                    '_target_': 'torch.optim.SGD',
                    'lr': 0.01,
                    'momentum': 0.9
                }),
                'scheduler': DictConfig({
                    '_target_': 'torch.optim.lr_scheduler.ExponentialLR',
                    'gamma': 0.95
                })
            }
        ]
        
        for config in optimizer_scheduler_configs:
            try:
                model_config = DictConfig({
                    '_target_': 'src.modules.segmentation_module.SegmentationModule',
                    'model_name': 'deeplabv3plus',
                    'model_params': {
                        'backbone': 'resnet50',
                        'in_channels': 3,
                        'pretrained': False
                    },
                    'optimizer_cfg': config['optimizer'],
                    'scheduler_cfg': config['scheduler'],
                    'loss_cfg': {
                        '_target_': 'src.losses.cross_entropy_loss.CrossEntropyLoss',
                        'ignore_index': 255,
                        'num_classes': 7
                    },
                    'num_classes': 7,
                    'ignore_index': 255
                })
                
                with warnings.catch_warnings():
                    warnings.filterwarnings("ignore", message=".*self.trainer.*")
                    
                    model = hydra.utils.instantiate(model_config, _recursive_=False)
                    
                    # 验证优化器和调度器配置
                    optimizer_cfg = model.configure_optimizers()
                    
                    assert optimizer_cfg is not None, f"优化器配置失败: {config['name']}"
                    
                    print(f"✅ {config['name']}: 优化器调度器集成测试通过")
                    
            except Exception as e:
                pytest.fail(f"优化器调度器配置 {config['name']} 测试失败: {e}")
    
    def test_hydra_loss_function_integration(self):
        """测试Hydra标准方式的损失函数集成"""
        
        # 测试不同损失函数配置
        loss_configs = [
            {
                'name': 'CrossEntropy',
                'config': DictConfig({
                    '_target_': 'src.losses.cross_entropy_loss.CrossEntropyLoss',
                    'ignore_index': 255,
                    'num_classes': 7
                })
            },
            {
                'name': 'Dice',
                'config': DictConfig({
                    '_target_': 'src.losses.dice_loss.DiceLoss',
                    'num_classes': 7,
                    'smooth': 1e-5
                })
            },
            {
                'name': 'Focal',
                'config': DictConfig({
                    '_target_': 'src.losses.focal_loss.FocalLoss',
                    'alpha': 1.0,
                    'gamma': 2.0,
                    'num_classes': 7
                })
            },
            {
                'name': 'Combined',
                'config': DictConfig({
                    '_target_': 'src.losses.combined_loss.CombinedLoss',
                    'loss_weights': {'ce': 0.6, 'dice': 0.4},
                    'ignore_index': 255,
                    'num_classes': 7
                })
            }
        ]
        
        for loss_config in loss_configs:
            try:
                model_config = DictConfig({
                    '_target_': 'src.modules.segmentation_module.SegmentationModule',
                    'model_name': 'deeplabv3plus',
                    'model_params': {
                        'backbone': 'resnet50',
                        'in_channels': 3,
                        'pretrained': False
                    },
                    'optimizer_cfg': {
                        '_target_': 'torch.optim.AdamW',
                        'lr': 0.001
                    },
                    'scheduler_cfg': {
                        '_target_': 'torch.optim.lr_scheduler.StepLR',
                        'step_size': 10
                    },
                    'loss_cfg': loss_config['config'],
                    'num_classes': 7,
                    'ignore_index': 255
                })
                
                with warnings.catch_warnings():
                    warnings.filterwarnings("ignore", message=".*self.trainer.*")
                    
                    model = hydra.utils.instantiate(model_config, _recursive_=False)
                    
                    # 测试损失函数工作
                    test_batch = {
                        'image': torch.randn(2, 3, 64, 64),
                        'mask': torch.randint(0, 7, (2, 64, 64))
                    }
                    
                    model.train()
                    train_loss = model.training_step(test_batch, 0)
                    
                    assert isinstance(train_loss, torch.Tensor), f"损失计算失败: {loss_config['name']}"
                    assert not torch.isnan(train_loss), f"损失为NaN: {loss_config['name']}"
                    
                    print(f"✅ {loss_config['name']}: 损失函数集成测试通过，损失值 = {train_loss.item():.4f}")
                    
            except Exception as e:
                pytest.fail(f"损失函数 {loss_config['name']} 集成测试失败: {e}")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "--tb=short", "-s"])
