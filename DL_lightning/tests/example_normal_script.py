#!/usr/bin/env python3
"""
普通脚本写法示例 - 手动测试模型
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import torch
from omegaconf import OmegaConf
from src.modules.segmentation_module import SegmentationModule
from src.models.segmentation import AVAILABLE_ARCHITECTURES

def test_model_inheritance():
    """测试模型继承关系"""
    print("🧪 测试模型继承关系...")
    
    try:
        import lightning.pytorch as pl
        
        # 检查继承关系
        if issubclass(SegmentationModule, pl.LightningModule):
            print("  ✅ SegmentationModule正确继承LightningModule")
            return True
        else:
            print("  ❌ SegmentationModule未正确继承LightningModule")
            return False
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def test_available_architectures():
    """测试可用架构"""
    print("🧪 测试可用架构...")
    
    try:
        if not AVAILABLE_ARCHITECTURES:
            print("  ❌ AVAILABLE_ARCHITECTURES为空")
            return False
        
        print(f"  ✅ 发现 {len(AVAILABLE_ARCHITECTURES)} 个可用架构:")
        for arch_name in AVAILABLE_ARCHITECTURES.keys():
            print(f"    - {arch_name}")
        return True
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def test_model_creation():
    """测试模型创建"""
    print("🧪 测试模型创建...")
    
    try:
        model = SegmentationModule(
            model_name='deeplabv3plus',
            model_params=OmegaConf.create({
                'backbone': 'resnet50',
                'in_channels': 3,
                'pretrained': False
            }),
            optimizer_cfg=OmegaConf.create({
                '_target_': 'torch.optim.AdamW',
                'lr': 0.001
            }),
            scheduler_cfg=OmegaConf.create({
                '_target_': 'torch.optim.lr_scheduler.StepLR',
                'step_size': 10
            }),
            loss_cfg=OmegaConf.create({
                '_target_': 'torch.nn.CrossEntropyLoss'
            }),
            num_classes=7
        )
        
        print("  ✅ 模型创建成功")
        return True
    except Exception as e:
        print(f"  ❌ 模型创建失败: {e}")
        return False

def test_forward_pass():
    """测试前向传播"""
    print("🧪 测试前向传播...")
    
    try:
        # 创建模型
        model = SegmentationModule(
            model_name='deeplabv3plus',
            model_params=OmegaConf.create({
                'backbone': 'resnet50',
                'in_channels': 3,
                'pretrained': False
            }),
            optimizer_cfg=OmegaConf.create({
                '_target_': 'torch.optim.AdamW',
                'lr': 0.001
            }),
            scheduler_cfg=OmegaConf.create({
                '_target_': 'torch.optim.lr_scheduler.StepLR',
                'step_size': 10
            }),
            loss_cfg=OmegaConf.create({
                '_target_': 'torch.nn.CrossEntropyLoss'
            }),
            num_classes=7
        )
        
        # 创建测试数据
        test_input = torch.randn(2, 3, 256, 256)
        
        # 前向传播
        model.eval()
        with torch.no_grad():
            output = model.architecture(test_input)
        
        expected_shape = (2, 7, 256, 256)
        if output.shape == expected_shape:
            print(f"  ✅ 前向传播成功: {test_input.shape} -> {output.shape}")
            return True
        else:
            print(f"  ❌ 输出形状错误: 期望{expected_shape}, 实际{output.shape}")
            return False
            
    except Exception as e:
        print(f"  ❌ 前向传播失败: {e}")
        return False

def main():
    """主函数 - 运行所有测试"""
    print("🎯 开始模型测试")
    print("=" * 50)
    
    # 定义测试列表
    tests = [
        ("模型继承关系测试", test_model_inheritance),
        ("可用架构测试", test_available_architectures),
        ("模型创建测试", test_model_creation),
        ("前向传播测试", test_forward_pass),
    ]
    
    # 运行测试
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name}: 通过")
        else:
            print(f"❌ {test_name}: 失败")
    
    # 输出结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    print(f"通过: {passed}/{total} ({passed/total:.1%})")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("⚠️ 部分测试失败")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
