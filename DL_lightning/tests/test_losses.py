#!/usr/bin/env python3
"""
损失函数模块全面测试
验证 src/losses/ 模块的功能完整性和正确性

测试范围：
1. 损失函数基类测试
2. 各个损失函数实例化测试
3. 损失函数前向传播测试
4. 组合损失函数测试
5. Hydra配置集成测试
6. 边界情况和错误处理测试
"""

import sys
from pathlib import Path
from typing import Dict, Any
import pytest
import warnings

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import torch
import torch.nn.functional as F
from omegaconf import DictConfig
import hydra

# 导入损失函数模块
from src.losses import (
    BaseLoss,
    DiceLoss,
    CEDiceLoss,
    FocalLoss,
    CrossEntropyLoss,
    LovaszLoss,
    CombinedLoss,
    AVAILABLE_LOSSES
)


class TestBaseLoss:
    """损失函数基类测试"""
    
    def test_base_loss_inheritance(self):
        """测试BaseLoss正确继承torch.nn.Module"""
        assert issubclass(BaseLoss, torch.nn.Module), "BaseLoss未正确继承torch.nn.Module"
    
    def test_base_loss_initialization(self):
        """测试BaseLoss初始化"""
        base_loss = BaseLoss()
        
        assert hasattr(base_loss, 'supports_class_weights'), "缺少supports_class_weights属性"
        assert hasattr(base_loss, 'logger'), "缺少logger属性"
        assert hasattr(base_loss, 'last_update_epoch'), "缺少last_update_epoch属性"
        
        assert isinstance(base_loss.supports_class_weights, bool), "supports_class_weights应该是bool类型"
        assert base_loss.last_update_epoch is None, "last_update_epoch初始值应该是None"
    
    def test_base_loss_abstract_methods(self):
        """测试BaseLoss抽象方法"""
        base_loss = BaseLoss()
        
        # forward方法应该抛出NotImplementedError
        with pytest.raises(NotImplementedError):
            dummy_input = torch.randn(2, 3, 4, 4)
            dummy_target = torch.randint(0, 3, (2, 4, 4))
            base_loss(dummy_input, dummy_target)


class TestAvailableLosses:
    """可用损失函数注册表测试"""
    
    def test_available_losses_registry(self):
        """测试损失函数注册表"""
        assert AVAILABLE_LOSSES, "AVAILABLE_LOSSES为空"
        assert len(AVAILABLE_LOSSES) > 0, "没有注册任何损失函数"
        
        # 验证预期的损失函数存在
        expected_losses = ['dice', 'ce_dice', 'focal', 'cross_entropy', 'ce', 'lovasz', 'combined']
        for loss_name in expected_losses:
            assert loss_name in AVAILABLE_LOSSES, f"缺少预期损失函数: {loss_name}"
    
    def test_loss_classes_callable(self):
        """测试所有注册的损失函数类都是可调用的"""
        for loss_name, loss_class in AVAILABLE_LOSSES.items():
            assert callable(loss_class), f"损失函数类不可调用: {loss_name}"
            assert issubclass(loss_class, torch.nn.Module), f"损失函数类未继承nn.Module: {loss_name}"


class TestIndividualLosses:
    """各个损失函数测试"""
    
    @pytest.mark.parametrize("loss_name,loss_class", [
        ('dice', DiceLoss),
        ('focal', FocalLoss),
        ('cross_entropy', CrossEntropyLoss),
        ('lovasz', LovaszLoss),
    ])
    def test_loss_instantiation(self, loss_name, loss_class):
        """测试各个损失函数的实例化"""
        try:
            if loss_name == 'dice':
                loss_fn = loss_class(num_classes=7)
            elif loss_name == 'focal':
                loss_fn = loss_class(alpha=1.0, gamma=2.0, num_classes=7)
            elif loss_name == 'cross_entropy':
                loss_fn = loss_class(ignore_index=255)
            elif loss_name == 'lovasz':
                loss_fn = loss_class(ignore_index=255)
            else:
                loss_fn = loss_class()
            
            assert loss_fn is not None, f"损失函数 {loss_name} 实例化失败"
            assert isinstance(loss_fn, torch.nn.Module), f"损失函数 {loss_name} 不是有效的PyTorch模块"
            
        except Exception as e:
            pytest.fail(f"损失函数 {loss_name} 实例化失败: {e}")
    
    @pytest.mark.parametrize("batch_size,num_classes,height,width", [
        (2, 7, 256, 256),
        (4, 14, 512, 512),
        (1, 5, 128, 128),
    ])
    def test_loss_forward_propagation(self, batch_size, num_classes, height, width):
        """测试损失函数前向传播"""
        # 创建测试数据
        logits = torch.randn(batch_size, num_classes, height, width)
        targets = torch.randint(0, num_classes, (batch_size, height, width))
        
        # 测试CrossEntropyLoss
        ce_loss = CrossEntropyLoss(ignore_index=255)
        ce_result = ce_loss(logits, targets)
        
        assert isinstance(ce_result, torch.Tensor), "CrossEntropyLoss未返回张量"
        assert ce_result.dim() == 0, "损失张量维度错误"
        assert not torch.isnan(ce_result), "损失为NaN"
        assert not torch.isinf(ce_result), "损失为无穷大"
        assert ce_result.item() >= 0, "损失值应该非负"
    
    def test_dice_loss_specific(self):
        """测试Dice损失函数特定功能"""
        dice_loss = DiceLoss(num_classes=3, smooth=1e-5)
        
        # 创建测试数据
        logits = torch.randn(2, 3, 4, 4)
        targets = torch.randint(0, 3, (2, 4, 4))
        
        loss = dice_loss(logits, targets)
        
        assert isinstance(loss, torch.Tensor), "DiceLoss未返回张量"
        assert 0 <= loss.item() <= 1, "Dice损失应该在[0,1]范围内"
    
    def test_focal_loss_specific(self):
        """测试Focal损失函数特定功能"""
        focal_loss = FocalLoss(alpha=1.0, gamma=2.0, num_classes=5)
        
        # 创建测试数据
        logits = torch.randn(2, 5, 8, 8)
        targets = torch.randint(0, 5, (2, 8, 8))
        
        loss = focal_loss(logits, targets)
        
        assert isinstance(loss, torch.Tensor), "FocalLoss未返回张量"
        assert loss.item() >= 0, "Focal损失应该非负"


class TestCombinedLoss:
    """组合损失函数测试"""
    
    def test_combined_loss_instantiation(self):
        """测试组合损失函数实例化"""
        loss_weights = {'ce': 0.5, 'dice': 0.5}
        combined_loss = CombinedLoss(loss_weights=loss_weights, ignore_index=255)
        
        assert combined_loss is not None, "CombinedLoss实例化失败"
        assert hasattr(combined_loss, 'loss_weights'), "CombinedLoss缺少loss_weights属性"
    
    def test_combined_loss_forward(self):
        """测试组合损失函数前向传播"""
        loss_weights = {'ce': 0.6, 'dice': 0.4}
        combined_loss = CombinedLoss(loss_weights=loss_weights, ignore_index=255, num_classes=7)
        
        # 创建测试数据
        logits = torch.randn(2, 7, 64, 64)
        targets = torch.randint(0, 7, (2, 64, 64))
        
        loss = combined_loss(logits, targets)
        
        assert isinstance(loss, torch.Tensor), "CombinedLoss未返回张量"
        assert loss.dim() == 0, "损失张量维度错误"
        assert not torch.isnan(loss), "损失为NaN"
        assert not torch.isinf(loss), "损失为无穷大"
        assert loss.item() >= 0, "损失值应该非负"


class TestCEDiceLoss:
    """CE+Dice组合损失函数测试"""
    
    def test_ce_dice_loss_instantiation(self):
        """测试CE+Dice损失函数实例化"""
        ce_dice_loss = CEDiceLoss(ce_weight=0.5, dice_weight=0.5)

        assert ce_dice_loss is not None, "CEDiceLoss实例化失败"
        assert hasattr(ce_dice_loss, 'ce_weight'), "CEDiceLoss缺少ce_weight属性"
        assert hasattr(ce_dice_loss, 'dice_weight'), "CEDiceLoss缺少dice_weight属性"
    
    def test_ce_dice_loss_forward(self):
        """测试CE+Dice损失函数前向传播"""
        ce_dice_loss = CEDiceLoss(ce_weight=0.7, dice_weight=0.3)

        # 创建测试数据 - 使用DiceLoss默认的14个类别
        logits = torch.randn(2, 14, 32, 32)
        targets = torch.randint(0, 14, (2, 32, 32))

        loss = ce_dice_loss(logits, targets)

        assert isinstance(loss, torch.Tensor), "CEDiceLoss未返回张量"
        assert loss.dim() == 0, "损失张量维度错误"
        assert not torch.isnan(loss), "损失为NaN"
        assert loss.item() >= 0, "损失值应该非负"


class TestHydraIntegration:
    """Hydra配置集成测试"""
    
    def test_hydra_loss_instantiation(self):
        """测试通过Hydra配置实例化损失函数"""
        # 创建测试配置
        test_configs = [
            {
                '_target_': 'src.losses.cross_entropy_loss.CrossEntropyLoss',
                'ignore_index': 255
            },
            {
                '_target_': 'src.losses.dice_loss.DiceLoss',
                'num_classes': 7,
                'smooth': 1e-5
            },
            {
                '_target_': 'src.losses.focal_loss.FocalLoss',
                'alpha': 1.0,
                'gamma': 2.0,
                'num_classes': 7
            }
        ]
        
        for config in test_configs:
            config_obj = DictConfig(config)
            
            try:
                loss_fn = hydra.utils.instantiate(config_obj)
                assert loss_fn is not None, f"Hydra实例化失败: {config['_target_']}"
                assert isinstance(loss_fn, torch.nn.Module), f"实例化对象不是nn.Module: {config['_target_']}"
            except Exception as e:
                pytest.fail(f"Hydra实例化失败 {config['_target_']}: {e}")


class TestErrorHandling:
    """错误处理和边界情况测试"""
    
    def test_invalid_input_shapes(self):
        """测试无效输入形状的错误处理"""
        ce_loss = CrossEntropyLoss()
        
        # 测试形状不匹配的输入
        logits = torch.randn(2, 5, 8, 8)  # [B, C, H, W]
        targets = torch.randint(0, 5, (2, 4, 4))  # [B, H', W'] - 不匹配的尺寸
        
        with pytest.raises((RuntimeError, ValueError)):
            ce_loss(logits, targets)
    
    def test_out_of_range_targets(self):
        """测试超出范围的目标值"""
        ce_loss = CrossEntropyLoss()
        
        logits = torch.randn(2, 5, 4, 4)  # 5个类别
        targets = torch.randint(0, 10, (2, 4, 4))  # 目标值超出类别范围
        
        # 这应该不会崩溃，但可能产生意外结果
        try:
            loss = ce_loss(logits, targets)
            # 如果没有抛出异常，检查损失是否合理
            assert isinstance(loss, torch.Tensor), "应该返回张量"
        except Exception:
            # 如果抛出异常也是可以接受的
            pass
    
    def test_empty_loss_weights(self):
        """测试空的损失权重配置"""
        with pytest.raises((ValueError, TypeError)):
            CombinedLoss(loss_weights={})  # 空的权重字典应该抛出错误


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "--tb=short"])
