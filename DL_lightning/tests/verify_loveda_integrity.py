#!/usr/bin/env python3
"""
验证LoveDA数据集的完整性

检查image和mask文件的一一对应关系，发现自动扫描方式的潜在问题
"""

import sys
from pathlib import Path
from collections import defaultdict


def check_file_correspondence(data_dir: Path, split: str):
    """检查指定split中image和mask文件的对应关系"""
    print(f"\n🔍 检查{split}集的文件对应关系:")
    print("-" * 50)
    
    split_name = split.capitalize()
    split_dir = data_dir / split_name
    
    if not split_dir.exists():
        print(f"❌ Split目录不存在: {split_dir}")
        return False
    
    issues_found = False
    total_images = 0
    total_masks = 0
    matched_pairs = 0
    
    for domain in ['Rural', 'Urban']:
        print(f"\n📁 检查{domain}域:")
        
        domain_dir = split_dir / domain
        if not domain_dir.exists():
            print(f"⚠️ 域目录不存在: {domain_dir}")
            continue
            
        image_dir = domain_dir / 'images_png'
        mask_dir = domain_dir / 'masks_png'
        
        if not image_dir.exists():
            print(f"❌ 图像目录不存在: {image_dir}")
            issues_found = True
            continue
            
        if not mask_dir.exists():
            print(f"❌ 标签目录不存在: {mask_dir}")
            issues_found = True
            continue
        
        # 收集所有图像和标签文件
        image_files = set(f.name for f in image_dir.glob('*.png'))
        mask_files = set(f.name for f in mask_dir.glob('*.png'))
        
        total_images += len(image_files)
        total_masks += len(mask_files)
        
        print(f"   图像文件数量: {len(image_files)}")
        print(f"   标签文件数量: {len(mask_files)}")
        
        # 检查对应关系
        matched = image_files & mask_files
        only_images = image_files - mask_files
        only_masks = mask_files - image_files
        
        matched_pairs += len(matched)
        
        print(f"   匹配的文件对: {len(matched)}")
        
        if only_images:
            print(f"   ❌ 只有图像没有标签的文件: {len(only_images)}")
            if len(only_images) <= 10:  # 只显示前10个
                for f in sorted(only_images):
                    print(f"      {f}")
            else:
                for f in sorted(list(only_images)[:10]):
                    print(f"      {f}")
                print(f"      ... 还有{len(only_images)-10}个文件")
            issues_found = True
            
        if only_masks:
            print(f"   ❌ 只有标签没有图像的文件: {len(only_masks)}")
            if len(only_masks) <= 10:  # 只显示前10个
                for f in sorted(only_masks):
                    print(f"      {f}")
            else:
                for f in sorted(list(only_masks)[:10]):
                    print(f"      {f}")
                print(f"      ... 还有{len(only_masks)-10}个文件")
            issues_found = True
        
        if not only_images and not only_masks:
            print(f"   ✅ 所有文件都有对应的配对")
    
    print(f"\n📊 {split}集总结:")
    print(f"   总图像文件: {total_images}")
    print(f"   总标签文件: {total_masks}")
    print(f"   匹配的文件对: {matched_pairs}")
    print(f"   匹配率: {matched_pairs/max(total_images, total_masks)*100:.1f}%")
    
    return not issues_found


def compare_with_list_files(data_dir: Path, split: str):
    """对比列表文件中的样本与目录扫描的结果"""
    print(f"\n🔍 对比{split}集的列表文件与目录扫描:")
    print("-" * 50)
    
    # 从列表文件读取
    list_file = data_dir / f"{split}_list.txt"
    if not list_file.exists():
        print(f"❌ 列表文件不存在: {list_file}")
        return False
    
    list_samples = set()
    with open(list_file, 'r') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            parts = line.split()
            if len(parts) >= 2:
                img_path, mask_path = parts[:2]
                # 提取文件名
                img_name = Path(img_path).name
                mask_name = Path(mask_path).name
                list_samples.add((img_name, mask_name))
    
    # 从目录扫描获取
    scan_samples = set()
    split_name = split.capitalize()
    split_dir = data_dir / split_name
    
    for domain in ['Rural', 'Urban']:
        domain_dir = split_dir / domain
        if not domain_dir.exists():
            continue
            
        image_dir = domain_dir / 'images_png'
        mask_dir = domain_dir / 'masks_png'
        
        if not image_dir.exists() or not mask_dir.exists():
            continue
        
        for image_path in image_dir.glob('*.png'):
            mask_path = mask_dir / image_path.name
            if mask_path.exists():
                scan_samples.add((image_path.name, image_path.name))
    
    print(f"列表文件样本数: {len(list_samples)}")
    print(f"目录扫描样本数: {len(scan_samples)}")
    
    # 检查差异
    only_in_list = list_samples - scan_samples
    only_in_scan = scan_samples - list_samples
    
    if only_in_list:
        print(f"❌ 只在列表文件中的样本: {len(only_in_list)}")
        for img, mask in sorted(list(only_in_list)[:5]):
            print(f"   {img} -> {mask}")
        if len(only_in_list) > 5:
            print(f"   ... 还有{len(only_in_list)-5}个")
    
    if only_in_scan:
        print(f"❌ 只在目录扫描中的样本: {len(only_in_scan)}")
        for img, mask in sorted(list(only_in_scan)[:5]):
            print(f"   {img} -> {mask}")
        if len(only_in_scan) > 5:
            print(f"   ... 还有{len(only_in_scan)-5}个")
    
    if not only_in_list and not only_in_scan:
        print("✅ 列表文件与目录扫描结果完全一致")
        return True
    else:
        print("❌ 列表文件与目录扫描结果存在差异")
        return False


def check_file_sizes_and_integrity(data_dir: Path, split: str):
    """检查文件大小和基本完整性"""
    print(f"\n🔍 检查{split}集的文件完整性:")
    print("-" * 50)
    
    split_name = split.capitalize()
    split_dir = data_dir / split_name
    
    issues_found = False
    size_mismatches = []
    zero_size_files = []
    
    for domain in ['Rural', 'Urban']:
        domain_dir = split_dir / domain
        if not domain_dir.exists():
            continue
            
        image_dir = domain_dir / 'images_png'
        mask_dir = domain_dir / 'masks_png'
        
        if not image_dir.exists() or not mask_dir.exists():
            continue
        
        for image_path in image_dir.glob('*.png'):
            mask_path = mask_dir / image_path.name
            if not mask_path.exists():
                continue
            
            # 检查文件大小
            img_size = image_path.stat().st_size
            mask_size = mask_path.stat().st_size
            
            if img_size == 0:
                zero_size_files.append(f"图像: {image_path}")
                issues_found = True
            
            if mask_size == 0:
                zero_size_files.append(f"标签: {mask_path}")
                issues_found = True
            
            # 检查大小是否合理（图像通常比标签大）
            if img_size > 0 and mask_size > 0 and mask_size > img_size:
                size_mismatches.append(f"{image_path.name}: 图像{img_size}字节, 标签{mask_size}字节")
    
    if zero_size_files:
        print(f"❌ 发现{len(zero_size_files)}个零字节文件:")
        for f in zero_size_files[:10]:
            print(f"   {f}")
        if len(zero_size_files) > 10:
            print(f"   ... 还有{len(zero_size_files)-10}个")
        issues_found = True
    
    if size_mismatches:
        print(f"⚠️ 发现{len(size_mismatches)}个大小异常的文件对:")
        for f in size_mismatches[:5]:
            print(f"   {f}")
        if len(size_mismatches) > 5:
            print(f"   ... 还有{len(size_mismatches)-5}个")
    
    if not issues_found:
        print("✅ 所有文件大小正常")
    
    return not issues_found


def main():
    """主函数"""
    print("🔍 LoveDA数据集完整性验证")
    print("=" * 60)
    print("检查自动扫描方式的潜在问题...")
    
    data_dir = Path("/home/<USER>/DeepLearing/SuiDe_Project/data/LoveDA")
    
    if not data_dir.exists():
        print(f"❌ 数据目录不存在: {data_dir}")
        return False
    
    all_good = True
    
    # 检查训练集和验证集
    for split in ['train', 'val']:
        # 检查文件对应关系
        correspondence_ok = check_file_correspondence(data_dir, split)
        all_good = all_good and correspondence_ok
        
        # 对比列表文件
        list_comparison_ok = compare_with_list_files(data_dir, split)
        all_good = all_good and list_comparison_ok
        
        # 检查文件完整性
        integrity_ok = check_file_sizes_and_integrity(data_dir, split)
        all_good = all_good and integrity_ok
    
    print(f"\n📊 总体评估:")
    if all_good:
        print("✅ LoveDA数据集完整性良好，自动扫描方式安全可用")
        print("✅ 文件一一对应关系正确")
        print("✅ 与列表文件结果一致")
    else:
        print("❌ 发现数据完整性问题！")
        print("⚠️ 建议使用列表文件方式以确保数据正确性")
        print("⚠️ 自动扫描方式存在风险")
    
    return all_good


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
