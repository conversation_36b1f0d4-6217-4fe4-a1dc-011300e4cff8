# 测试套件

本目录包含项目的完整测试套件，验证所有核心功能和集成。

## 🔧 重要测试文件

### ⭐ 核心集成测试
- **`test_wandb_raytune_integration.py`** - WandB + Ray Tune 完整集成测试
- `test_hydra_config_standard.py` - Hydra配置系统测试
- `test_config_validation.py` - 配置验证测试

### 🎯 核心组件测试
- `test_models.py` / `test_models_hydra_standard.py` - 模型测试
- `test_losses.py` / `test_losses_hydra_standard.py` - 损失函数测试
- `test_optimizers_schedulers_extended.py` - 优化器调度器测试

### 📊 数据相关测试
- `test_data_datasets.py` - 数据集测试
- `test_real_datasets.py` - 真实数据测试
- `verify_loveda_integrity.py` - LoveDA数据完整性验证

## 🚀 运行测试

```bash
# 🔥 首先运行集成测试 (推荐)
python tests/test_wandb_raytune_integration.py

# 运行所有测试
python -m pytest tests/

# 运行特定测试
python -m pytest tests/test_models.py

# 运行所有测试脚本
python tests/run_all_tests.py
```

## 📋 测试优先级

1. **🔥 高优先级**: `test_wandb_raytune_integration.py` - 验证完整工作流
2. **⚙️ 中优先级**: 核心组件测试 (models, losses, data)
3. **🔍 低优先级**: 专项和验证测试