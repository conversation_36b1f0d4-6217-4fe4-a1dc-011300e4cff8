#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
WandB + Ray Tune 集成测试
验证Hydra + OmegaConf.to_container + WandB + Ray Tune的完整工作流
"""

import os
import tempfile
import pytest
from pathlib import Path
from omegaconf import DictConfig, OmegaConf
import hydra
from hydra import compose, initialize_config_dir
from hydra.core.global_hydra import GlobalHydra


def create_test_config():
    """创建测试配置"""
    config_content = """
experiment:
  name: "integration_test"
  version: "v1.0"
  tags: ["test", "integration"]

model:
  _target_: "torch.nn.Linear"
  in_features: 10
  out_features: 1
  lr: 1e-3
  hidden_dim: ${model.in_features}

optimizer:
  _target_: "torch.optim.AdamW"
  lr: ${model.lr}
  weight_decay: 1e-2

data:
  batch_size: 32
  num_workers: 4
  input_dim: ${model.in_features}

trainer:
  max_epochs: 5
  log_every_n_steps: ${data.batch_size}

wandb:
  project: "test_integration"
  entity: "test_user"
  mode: "disabled"  # 测试模式，不实际上传

ray_tune:
  num_samples: 2
  max_concurrent_trials: 1
"""
    return config_content


def test_config_loading():
    """测试配置加载"""
    print("🔍 测试1: 配置加载")
    print("-" * 40)
    
    config_content = create_test_config()
    config = OmegaConf.create(config_content)
    
    # 验证配置结构
    assert "experiment" in config
    assert "model" in config
    assert "optimizer" in config
    assert config.experiment.name == "integration_test"
    
    print(f"✅ 配置加载成功")
    print(f"   实验名称: {config.experiment.name}")
    print(f"   模型目标: {config.model._target_}")
    print(f"   插值示例: model.hidden_dim = {config.model.hidden_dim}")
    
    return config


def test_omegaconf_conversion():
    """测试OmegaConf转换"""
    print("\n🔍 测试2: OmegaConf转换")
    print("-" * 40)
    
    config = test_config_loading()
    
    # 转换为原生字典
    dict_config = OmegaConf.to_container(config, resolve=True)
    
    # 验证转换结果
    assert isinstance(dict_config, dict)
    assert dict_config["model"]["hidden_dim"] == 10  # 插值已解析
    assert dict_config["optimizer"]["lr"] == 1e-3    # 插值已解析
    assert dict_config["trainer"]["log_every_n_steps"] == 32  # 插值已解析
    
    print(f"✅ 转换成功")
    print(f"   原始类型: {type(config)}")
    print(f"   转换后类型: {type(dict_config)}")
    print(f"   插值解析: model.hidden_dim = {dict_config['model']['hidden_dim']}")
    print(f"   插值解析: optimizer.lr = {dict_config['optimizer']['lr']}")
    
    return config, dict_config


def test_wandb_integration():
    """测试WandB集成"""
    print("\n🔍 测试3: WandB集成")
    print("-" * 40)
    
    try:
        import wandb
        wandb_available = True
    except ImportError:
        print("⚠️ WandB未安装，跳过WandB测试")
        return True
    
    config, dict_config = test_omegaconf_conversion()
    
    try:
        # 初始化WandB (测试模式)
        run = wandb.init(
            project=config.wandb.project,
            entity=config.wandb.entity,
            config=dict_config,  # 使用转换后的配置
            mode=config.wandb.mode,  # disabled模式
            reinit=True
        )
        
        # 验证配置记录
        assert run.config is not None
        assert run.config["model"]["lr"] == 1e-3
        assert run.config["model"]["hidden_dim"] == 10
        
        # 测试日志记录
        wandb.log({"test_metric": 0.95, "epoch": 1})
        
        # 关闭run
        wandb.finish()
        
        print(f"✅ WandB集成成功")
        print(f"   项目: {config.wandb.project}")
        print(f"   配置记录: ✅")
        print(f"   日志记录: ✅")
        
        return True
        
    except Exception as e:
        print(f"❌ WandB集成失败: {e}")
        return False


def test_ray_tune_integration():
    """测试Ray Tune集成"""
    print("\n🔍 测试4: Ray Tune集成")
    print("-" * 40)
    
    try:
        import ray
        from ray import tune
        from ray.air import RunConfig
        ray_available = True
    except ImportError:
        print("⚠️ Ray Tune未安装，跳过Ray Tune测试")
        return True
    
    config, dict_config = test_omegaconf_conversion()
    
    def simple_train_function(trial_config):
        """简单的训练函数"""
        # 验证配置类型
        assert isinstance(trial_config, dict)
        
        # 模拟训练过程
        lr = trial_config.get("lr", 1e-3)
        for epoch in range(3):
            # 模拟损失下降
            loss = 1.0 - (epoch * 0.1) - (lr * 100)
            
            # 报告结果
            tune.report({"loss": loss, "epoch": epoch})
        
        return {"final_loss": loss}
    
    try:
        # 初始化Ray (如果未初始化)
        if not ray.is_initialized():
            ray.init(local_mode=True, ignore_reinit_error=True)
        
        # 创建搜索空间
        search_space = {
            "lr": tune.choice([1e-4, 1e-3, 1e-2]),
            "batch_size": tune.choice([16, 32, 64])
        }
        
        # 运行调优
        tuner = tune.Tuner(
            simple_train_function,
            param_space=search_space,
            run_config=RunConfig(
                name="test_tune",
                stop={"epoch": 2},
                verbose=0
            )
        )
        
        results = tuner.fit()
        
        # 验证结果
        assert len(results) > 0
        best_result = results.get_best_result("loss", "min")
        assert best_result is not None
        
        print(f"✅ Ray Tune集成成功")
        print(f"   试验数量: {len(results)}")
        print(f"   最佳损失: {best_result.metrics['loss']:.4f}")
        print(f"   最佳参数: lr={best_result.config['lr']}")
        
        # 清理Ray
        ray.shutdown()
        
        return True
        
    except Exception as e:
        print(f"❌ Ray Tune集成失败: {e}")
        if ray.is_initialized():
            ray.shutdown()
        return False


def test_hydra_integration():
    """测试Hydra集成"""
    print("\n🔍 测试5: Hydra集成")
    print("-" * 40)
    
    # 创建临时配置文件
    with tempfile.TemporaryDirectory() as temp_dir:
        config_file = Path(temp_dir) / "config.yaml"
        config_content = create_test_config()
        config_file.write_text(config_content)
        
        try:
            # 清理之前的Hydra实例
            GlobalHydra.instance().clear()
            
            # 初始化Hydra
            with initialize_config_dir(config_dir=str(temp_dir), version_base=None):
                cfg = compose(config_name="config")
                
                # 验证Hydra配置
                assert isinstance(cfg, DictConfig)
                assert cfg.experiment.name == "integration_test"
                
                # 测试转换
                dict_config = OmegaConf.to_container(cfg, resolve=True)
                assert isinstance(dict_config, dict)
                
                # 测试Hydra功能
                assert cfg.model._target_ == "torch.nn.Linear"
                
                print(f"✅ Hydra集成成功")
                print(f"   配置类型: {type(cfg)}")
                print(f"   转换成功: {type(dict_config)}")
                print(f"   插值解析: ✅")
                
                return True
                
        except Exception as e:
            print(f"❌ Hydra集成失败: {e}")
            return False
        finally:
            GlobalHydra.instance().clear()


def test_complete_workflow():
    """测试完整工作流"""
    print("\n🔍 测试6: 完整工作流")
    print("-" * 40)
    
    try:
        # 模拟完整的Hydra主函数
        config = test_config_loading()
        
        # 阶段1: Hydra配置管理
        print(f"   阶段1: Hydra配置管理 ✅")
        
        # 阶段2: 转换配置
        dict_config = OmegaConf.to_container(config, resolve=True)
        print(f"   阶段2: 配置转换 ✅")
        
        # 阶段3: 外部集成准备
        # 验证JSON序列化
        import json
        json_str = json.dumps(dict_config)
        assert len(json_str) > 0
        
        # 验证pickle序列化
        import pickle
        pickle_data = pickle.dumps(dict_config)
        assert len(pickle_data) > 0
        
        print(f"   阶段3: 序列化验证 ✅")
        
        # 阶段4: 模拟外部库使用
        # 模拟WandB配置
        wandb_config = dict_config.copy()
        assert isinstance(wandb_config, dict)
        
        # 模拟Ray Tune配置
        tune_config = dict_config.copy()
        assert isinstance(tune_config, dict)
        
        print(f"   阶段4: 外部库兼容性 ✅")
        
        print(f"✅ 完整工作流测试成功")
        print(f"   所有阶段都正常工作")
        print(f"   Hydra + OmegaConf + WandB + Ray Tune 集成完美!")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整工作流测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("WandB + Ray Tune 集成测试")
    print("=" * 60)
    print("测试 Hydra + OmegaConf.to_container + WandB + Ray Tune 完整集成")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        test_config_loading,
        test_omegaconf_conversion,
        test_wandb_integration,
        test_ray_tune_integration,
        test_hydra_integration,
        test_complete_workflow
    ]
    
    results = []
    for test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 异常: {e}")
            results.append(False)
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📋 测试结果总结")
    print("=" * 60)
    
    passed = sum(1 for r in results if r)
    total = len(results)
    
    print(f"\n🎯 测试统计:")
    print(f"   总测试数: {total}")
    print(f"   通过数: {passed}")
    print(f"   失败数: {total - passed}")
    print(f"   成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print(f"\n🎉 所有测试通过!")
        print(f"   ✅ Hydra + OmegaConf.to_container + WandB + Ray Tune")
        print(f"   ✅ 集成完全正常，可以开始实际项目开发!")
    else:
        print(f"\n⚠️ 部分测试失败，请检查环境配置")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
