#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调度器Hydra实例化单元测试
测试SegmentationModule中调度器创建的正确性（注册表已移除）
"""

import sys
import unittest
import torch
import torch.nn as nn
from pathlib import Path
from omegaconf import DictConfig, OmegaConf

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.segmentation_module import SegmentationModule
from src.models.segmentation.unet import UNet

class TestSchedulerHydraInstantiation(unittest.TestCase):
    """调度器Hydra实例化测试类"""

    def setUp(self):
        """测试前准备"""
        # 创建一个简单的模型和优化器
        self.model = UNet(in_channels=3, num_classes=5)
        self.optimizer = torch.optim.AdamW(list(self.model.parameters()), lr=1e-3)

        # 创建SegmentationModule实例的基本配置
        self.model_params = OmegaConf.create({
            "in_channels": 3,
            "num_classes": 5
        })

        self.loss_cfg = {"_target_": "torch.nn.CrossEntropyLoss"}
    
    def test_available_schedulers(self):
        """测试可用调度器注册"""
        # 检查是否有足够的调度器
        self.assertGreaterEqual(len(AVAILABLE_SCHEDULERS), 6, "可用调度器数量不足")
        
        # 检查必要的调度器是否存在
        required_schedulers = ['cosine', 'step', 'poly']
        for sch_name in required_schedulers:
            self.assertIn(sch_name, AVAILABLE_SCHEDULERS, f"缺少必要的调度器: {sch_name}")
        
        # 检查自定义调度器是否存在
        custom_schedulers = ['multiscale', 'adaptive_cosine']
        for sch_name in custom_schedulers:
            self.assertIn(sch_name, AVAILABLE_SCHEDULERS, f"缺少自定义调度器: {sch_name}")
    
    def test_create_scheduler_by_hydra_config(self):
        """测试通过Hydra配置创建调度器"""
        # 测试余弦退火调度器
        scheduler_cfg = OmegaConf.create({
            '_target_': 'src.schedulers.standard.CosineAnnealingLR',
            'T_max': 100,
            'eta_min': 1e-6
        })

        module = SegmentationModule(
            model_name="unet",
            model_params=self.model_params,
            num_classes=5,
            optimizer_cfg=OmegaConf.create({'_target_': 'src.optimizers.standard.AdamW', 'lr': 1e-3}),
            scheduler_cfg=scheduler_cfg,
            loss_cfg=self.loss_cfg
        )
        module.architecture = self.model

        scheduler = module._create_scheduler(self.optimizer)
        self.assertEqual(scheduler.__class__.__name__, 'CosineAnnealingLR', "创建余弦退火调度器失败")

        # 测试阶梯调度器
        scheduler_cfg = OmegaConf.create({
            '_target_': 'src.schedulers.standard.StepLR',
            'step_size': 30,
            'gamma': 0.1
        })
        module.scheduler_cfg = scheduler_cfg
        scheduler = module._create_scheduler(self.optimizer)
        self.assertEqual(scheduler.__class__.__name__, 'StepLR', "创建阶梯调度器失败")

        # 测试自定义调度器
        scheduler_cfg = OmegaConf.create({
            '_target_': 'src.schedulers.examples.MultiScaleScheduler',
            'base_epochs': 100
        })
        module.scheduler_cfg = scheduler_cfg
        scheduler = module._create_scheduler(self.optimizer)
        self.assertEqual(scheduler.__class__.__name__, 'MultiScaleScheduler', "创建自定义调度器失败")
    
    def test_create_scheduler_by_config(self):
        """测试通过Hydra配置创建调度器"""
        # 测试Hydra配置
        scheduler_cfg = OmegaConf.create({
            '_target_': 'torch.optim.lr_scheduler.ExponentialLR',
            'gamma': 0.95
        })

        module = SegmentationModule(
            model_name="unet",
            model_params=self.model_params,
            num_classes=5,
            optimizer_cfg="adamw",
            scheduler_cfg=scheduler_cfg,
            loss_cfg=self.loss_cfg
        )
        module.architecture = self.model

        scheduler = module._create_scheduler(self.optimizer)
        self.assertIsInstance(scheduler, torch.optim.lr_scheduler.ExponentialLR, "通过配置创建指数衰减调度器失败")

        # 验证调度器参数
        initial_lr = self.optimizer.param_groups[0]['lr']
        scheduler.step()
        new_lr = self.optimizer.param_groups[0]['lr']
        expected_lr = initial_lr * 0.95
        self.assertAlmostEqual(new_lr, expected_lr, places=6, msg="调度器参数设置错误")
    
    def test_create_scheduler_with_warmup(self):
        """测试预热调度器创建"""
        # 测试无预热（应该返回标准调度器）
        scheduler_cfg = OmegaConf.create({
            '_target_': 'torch.optim.lr_scheduler.CosineAnnealingLR',
            'T_max': 100,
            'eta_min': 1e-6
        })

        module = SegmentationModule(
            model_name="unet",
            model_params=self.model_params,
            num_classes=5,
            optimizer_cfg="adamw",
            scheduler_cfg=scheduler_cfg,
            loss_cfg=self.loss_cfg
        )
        module.architecture = self.model

        scheduler = module._create_scheduler(self.optimizer)
        self.assertIsInstance(scheduler, torch.optim.lr_scheduler.CosineAnnealingLR, "无预热调度器创建失败")

        # 测试有预热（应该返回组合调度器）
        # 重置优化器学习率
        for param_group in self.optimizer.param_groups:
            param_group['lr'] = 1e-3

        scheduler_cfg_with_warmup = OmegaConf.create({
            '_target_': 'torch.optim.lr_scheduler.CosineAnnealingLR',
            'T_max': 100,
            'eta_min': 1e-6,
            'warmup_epochs': 5,
            'warmup_start_lr': 1e-6
        })

        module.scheduler_cfg = scheduler_cfg_with_warmup
        scheduler = module._create_scheduler(self.optimizer)
        self.assertIsInstance(scheduler, torch.optim.lr_scheduler.SequentialLR, "预热调度器创建失败")

        # 测试预热调度器的学习率变化
        initial_lr = self.optimizer.param_groups[0]['lr']

        # 第一步应该是预热起始学习率
        scheduler.step()
        first_step_lr = self.optimizer.param_groups[0]['lr']

        # 预热调度器应该从较小的学习率开始，然后逐渐增加
        self.assertIsNotNone(first_step_lr, "预热调度器学习率更新失败")
    
    def test_available_schedulers_registry(self):
        """测试可用调度器注册表"""
        # 检查是否有足够的调度器
        self.assertGreaterEqual(len(AVAILABLE_SCHEDULERS), 6, "可用调度器数量不足")

        # 检查必要的调度器是否存在
        required_schedulers = ['cosine', 'step', 'poly']
        for sch_name in required_schedulers:
            self.assertIn(sch_name, AVAILABLE_SCHEDULERS, f"缺少必要的调度器: {sch_name}")

        # 检查自定义调度器是否存在
        custom_schedulers = ['multiscale', 'adaptive_cosine']
        for sch_name in custom_schedulers:
            self.assertIn(sch_name, AVAILABLE_SCHEDULERS, f"缺少自定义调度器: {sch_name}")
    
    def test_scheduler_step_functionality(self):
        """测试调度器步进功能"""
        # 创建余弦退火调度器
        module = SegmentationModule(
            model_name="unet",
            model_params=self.model_params,
            num_classes=5,
            optimizer_cfg="adamw",
            scheduler_cfg="cosine",
            loss_cfg=self.loss_cfg
        )
        module.architecture = self.model

        scheduler = module._create_scheduler(self.optimizer)

        initial_lr = self.optimizer.param_groups[0]['lr']
        lr_history = [initial_lr]

        # 执行10步
        for _ in range(10):
            scheduler.step()
            lr_history.append(self.optimizer.param_groups[0]['lr'])

        # 检查学习率是否在衰减
        self.assertLess(lr_history[-1], lr_history[0], "学习率未衰减")

        # 检查最小学习率
        self.assertGreaterEqual(lr_history[-1], 1e-6, "学习率低于最小值")

    def test_error_handling(self):
        """测试错误处理"""
        # 测试未知调度器 - 应该回退到默认调度器
        module = SegmentationModule(
            model_name="unet",
            model_params=self.model_params,
            num_classes=5,
            optimizer_cfg="adamw",
            scheduler_cfg="unknown_scheduler",
            loss_cfg=self.loss_cfg
        )
        module.architecture = self.model

        scheduler = module._create_scheduler(self.optimizer)
        self.assertIsInstance(scheduler, torch.optim.lr_scheduler.CosineAnnealingLR, "未知调度器应该回退到默认调度器")

        # 测试无效配置 - 应该回退到默认调度器
        invalid_cfg = OmegaConf.create({'_target_': 'invalid.path'})
        module.scheduler_cfg = invalid_cfg

        scheduler = module._create_scheduler(self.optimizer)
        self.assertIsInstance(scheduler, torch.optim.lr_scheduler.CosineAnnealingLR, "无效配置应该回退到默认调度器")

if __name__ == '__main__':
    unittest.main()
