#!/usr/bin/env python3
"""
Hydra配置标准化验证测试
验证项目中所有配置都使用标准的Hydra方式

测试范围：
1. 验证所有配置文件都可以通过Hydra加载
2. 验证配置组合和覆盖功能
3. 验证实例化功能正常工作
4. 验证测试环境配置支持
"""

import sys
from pathlib import Path
import pytest
import warnings

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import torch
import hydra
from hydra.core.global_hydra import GlobalHydra
from omegaconf import DictConfig

# 导入Hydra配置工具
from src.utils.hydra_config_utils import (
    HydraConfigManager,
    load_config_with_hydra,
    create_test_config,
    load_data_config_for_test,
    load_model_config_for_test
)


class TestHydraConfigStandard:
    """Hydra配置标准化测试"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        GlobalHydra.instance().clear()
    
    def teardown_method(self):
        """每个测试方法后的清理"""
        GlobalHydra.instance().clear()
    
    def test_hydra_config_manager(self):
        """测试Hydra配置管理器"""
        
        with HydraConfigManager() as manager:
            # 测试基本配置加载
            cfg = manager.load_config("config", overrides=["data=suide"])
            
            assert cfg is not None, "配置加载失败"
            assert isinstance(cfg, DictConfig), "配置不是DictConfig类型"
            assert hasattr(cfg, 'data'), "配置缺少data部分"
            
            print("✅ Hydra配置管理器测试通过")
    
    def test_data_config_loading(self):
        """测试数据配置加载"""
        
        # 测试所有数据配置
        datasets = ['suide', 'loveda']
        
        for dataset in datasets:
            try:
                cfg = load_data_config_for_test(dataset)
                
                assert cfg is not None, f"数据配置加载失败: {dataset}"
                assert hasattr(cfg, 'data'), f"配置缺少data部分: {dataset}"
                
                # 测试实例化
                datamodule = hydra.utils.instantiate(cfg.data)
                assert datamodule is not None, f"数据模块实例化失败: {dataset}"
                
                print(f"✅ {dataset} 数据配置测试通过")
                
            except Exception as e:
                pytest.fail(f"数据配置 {dataset} 测试失败: {e}")
    
    def test_model_config_loading(self):
        """测试模型配置加载"""
        
        # 测试所有模型配置
        models = ['deeplabv3plus', 'unet', 'unetpp', 'swin_unet']
        
        for model in models:
            try:
                cfg = load_model_config_for_test(
                    model, 
                    **{"data": "suide", "trainer.fast_dev_run": True}
                )
                
                assert cfg is not None, f"模型配置加载失败: {model}"
                assert hasattr(cfg, 'model'), f"配置缺少model部分: {model}"
                
                # 测试实例化
                with warnings.catch_warnings():
                    warnings.filterwarnings("ignore", message=".*self.trainer.*")
                    
                    model_instance = hydra.utils.instantiate(
                        cfg.model,
                        optimizer_cfg=cfg.optimizer,
                        scheduler_cfg=cfg.scheduler,
                        loss_cfg=cfg.loss,
                        _recursive_=False
                    )
                    assert model_instance is not None, f"模型实例化失败: {model}"
                
                print(f"✅ {model} 模型配置测试通过")
                
            except Exception as e:
                pytest.fail(f"模型配置 {model} 测试失败: {e}")
    
    def test_loss_config_loading(self):
        """测试损失函数配置加载"""
        
        # 测试所有损失函数配置
        losses = ['cross_entropy', 'dice', 'focal', 'combined']
        
        for loss in losses:
            try:
                with HydraConfigManager() as manager:
                    cfg = manager.load_loss_config(loss, **{"data": "suide"})
                    
                    assert cfg is not None, f"损失函数配置加载失败: {loss}"
                    assert hasattr(cfg, 'loss'), f"配置缺少loss部分: {loss}"
                    
                    # 测试实例化
                    loss_fn = hydra.utils.instantiate(cfg.loss)
                    assert loss_fn is not None, f"损失函数实例化失败: {loss}"
                    assert isinstance(loss_fn, torch.nn.Module), f"不是PyTorch模块: {loss}"
                
                print(f"✅ {loss} 损失函数配置测试通过")
                
            except Exception as e:
                pytest.fail(f"损失函数配置 {loss} 测试失败: {e}")
    
    def test_config_override_functionality(self):
        """测试配置覆盖功能"""
        
        with HydraConfigManager() as manager:
            # 测试基本覆盖
            cfg = manager.load_config(
                "config",
                overrides=[
                    "model=deeplabv3plus",
                    "loss=cross_entropy", 
                    "data=suide",
                    "optimizer.lr=0.0001",
                    "trainer.max_epochs=1"
                ]
            )
            
            assert cfg.optimizer.lr == 0.0001, "学习率覆盖失败"
            assert cfg.trainer.max_epochs == 1, "训练轮数覆盖失败"
            
            print("✅ 配置覆盖功能测试通过")
    
    def test_create_test_config_function(self):
        """测试测试配置创建函数"""
        
        # 测试配置创建
        test_config_dict = {
            '_target_': 'src.losses.cross_entropy_loss.CrossEntropyLoss',
            'ignore_index': 255,
            'num_classes': 7
        }
        
        cfg = create_test_config(test_config_dict)
        
        assert isinstance(cfg, DictConfig), "配置不是DictConfig类型"
        assert cfg._target_ == 'src.losses.cross_entropy_loss.CrossEntropyLoss', "_target_不正确"
        assert cfg.ignore_index == 255, "ignore_index不正确"
        
        # 测试实例化
        loss_fn = hydra.utils.instantiate(cfg)
        assert loss_fn is not None, "实例化失败"
        assert isinstance(loss_fn, torch.nn.Module), "不是PyTorch模块"
        
        print("✅ 测试配置创建函数测试通过")
    
    def test_config_composition(self):
        """测试配置组合功能"""
        
        with HydraConfigManager() as manager:
            # 测试复杂的配置组合
            cfg = manager.load_config(
                "config",
                overrides=[
                    "model=deeplabv3plus",
                    "loss=combined",
                    "loss.loss_weights.ce=0.7",
                    "loss.loss_weights.dice=0.3",
                    "optimizer=adamw",
                    "optimizer.lr=0.0001",
                    "scheduler=cosine",
                    "data=suide",
                    "trainer.max_epochs=1",
                    "trainer.fast_dev_run=true"
                ]
            )
            
            # 验证配置组合正确
            assert cfg.loss._target_ == "src.losses.combined_loss.CombinedLoss"
            assert cfg.loss.loss_weights.ce == 0.7
            assert cfg.loss.loss_weights.dice == 0.3
            assert cfg.optimizer.lr == 0.0001
            assert cfg.trainer.fast_dev_run == True
            
            print("✅ 配置组合功能测试通过")
    
    def test_all_config_files_loadable(self):
        """测试所有配置文件都可以通过Hydra加载"""
        
        config_files = [
            # 数据配置
            ('data', ['suide', 'loveda']),
            # 模型配置
            ('model', ['deeplabv3plus', 'unet', 'unetpp', 'swin_unet']),
            # 损失函数配置
            ('loss', ['cross_entropy', 'dice', 'focal', 'combined', 'ce_dice']),
            # 优化器配置
            ('optimizer', ['adam', 'adamw', 'sgd']),
            # 调度器配置
            ('scheduler', ['step', 'cosine', 'exponential'])
        ]
        
        with HydraConfigManager() as manager:
            for config_type, config_names in config_files:
                for config_name in config_names:
                    try:
                        # 构建覆盖参数
                        overrides = [f"{config_type}={config_name}"]
                        if config_type != 'data':
                            overrides.append("data=suide")  # 提供默认数据配置
                        
                        cfg = manager.load_config("config", overrides=overrides)
                        
                        assert cfg is not None, f"配置加载失败: {config_type}/{config_name}"
                        assert hasattr(cfg, config_type), f"配置缺少{config_type}部分: {config_name}"
                        
                        print(f"✅ {config_type}/{config_name} 配置文件加载成功")
                        
                    except Exception as e:
                        pytest.fail(f"配置文件 {config_type}/{config_name} 加载失败: {e}")
        
        print("✅ 所有配置文件Hydra加载测试通过")


class TestBackwardCompatibility:
    """向后兼容性测试"""
    
    def setup_method(self):
        GlobalHydra.instance().clear()
    
    def teardown_method(self):
        GlobalHydra.instance().clear()
    
    def test_legacy_config_loading_warning(self):
        """测试遗留配置加载函数的警告"""
        
        from src.utils.hydra_config_utils import load_config_legacy
        
        # 测试警告是否正确发出
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            
            config_path = project_root / "configs/data/suide.yaml"
            cfg = load_config_legacy(config_path)
            
            assert len(w) == 1, "未发出弃用警告"
            assert issubclass(w[0].category, DeprecationWarning), "警告类型不正确"
            assert "load_config_legacy已弃用" in str(w[0].message), "警告消息不正确"
            
            assert cfg is not None, "遗留配置加载失败"
            
        print("✅ 向后兼容性测试通过")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "--tb=short", "-s"])
