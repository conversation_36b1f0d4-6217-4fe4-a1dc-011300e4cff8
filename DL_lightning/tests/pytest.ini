[tool:pytest]
# pytest配置文件

# 测试发现
testpaths = .
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# 输出配置
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes

# 标记定义
markers =
    basic: 基础功能测试
    loading: 数据加载测试
    integration: 集成测试
    slow: 慢速测试
    requires_data: 需要真实数据的测试

# 最小版本要求
minversion = 6.0

# 测试路径
norecursedirs = 
    .git
    .tox
    dist
    build
    *.egg
    __pycache__
