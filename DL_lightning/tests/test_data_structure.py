#!/usr/bin/env python3
"""
测试重构后的数据模块结构
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from omegaconf import OmegaConf

def test_data_structure():
    """测试重构后的数据模块结构"""
    
    print("🔍 测试重构后的数据模块结构...")
    
    try:
        # 测试导入
        from src.data.datasets.suide_dataset import SuiDeDataset, SuiDeClassMapper
        from src.data.suide_datamodule import SuiDeDataModule
        
        print("✅ 所有模块导入成功")
        
        # 测试配置
        config = OmegaConf.create({
            'dataset_config': {
                'data_dir': "data/Data_SRC/Dataset_v2.2",
                'class_info_path': "data/Data_SRC/Dataset_v2.2/metadata/class_info.json",
                'training_level': 2,
                'ignore_index': 255,
                'scale_weights': {
                    "1_1": 0.7,
                    "1_2": 0.2,
                    "1_0.5": 0.1
                }
            },
            'transform_config': None,
            'dataloader_config': {'batch_size': 4, 'num_workers': 0}
        })
        
        # 测试DataModule创建
        dm = SuiDeDataModule(
            dataset_config=config.dataset_config,
            transform_config=config.transform_config,
            dataloader_config=config.dataloader_config
        )
        
        print("✅ DataModule创建成功")
        
        # 测试setup
        dm.setup('fit')
        
        print("✅ DataModule setup成功")
        print(f"  训练集样本数: {len(dm.train_dataset)}")
        print(f"  验证集样本数: {len(dm.val_dataset)}")
        print(f"  类别数量: {dm.num_classes}")
        
        # 测试数据加载
        train_loader = dm.train_dataloader()
        batch = next(iter(train_loader))
        
        print("✅ 数据加载成功")
        print(f"  Batch keys: {list(batch.keys())}")
        print(f"  Image shape: {batch['image'].shape}")
        print(f"  Mask shape: {batch['mask'].shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_data_structure()
    if success:
        print("\n🎉 数据模块结构重构成功！")
    else:
        print("\n💥 数据模块结构重构失败！")
