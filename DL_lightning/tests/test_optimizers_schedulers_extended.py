#!/usr/bin/env python3
"""
扩展的优化器和调度器测试
验证新增的优化器和调度器配置是否正常工作

测试范围：
1. 新增的优化器配置 (RMSprop, Adamax等)
2. 新增的调度器配置 (Polynomial, ReduceOnPlateau等)
3. Warmup调度器功能
4. 语义分割最佳实践组合
"""

import sys
from pathlib import Path
import pytest
import warnings

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import torch
import hydra
from hydra import compose, initialize
from hydra.core.global_hydra import GlobalHydra
from omegaconf import DictConfig

# 导入新增的调度器
from src.schedulers.warmup_scheduler import (
    WarmupCosineAnnealingLR,
    WarmupPolynomialLR,
    WarmupStepLR
)


class TestExtendedOptimizers:
    """测试扩展的优化器配置"""
    
    def setup_method(self):
        GlobalHydra.instance().clear()
    
    def teardown_method(self):
        GlobalHydra.instance().clear()
    
    def test_new_optimizer_configs(self):
        """测试新增的优化器配置"""
        
        # 测试所有优化器配置
        optimizer_configs = [
            'adam', 'adamw', 'sgd', 'rmsprop', 'adamax'
        ]
        
        with initialize(config_path="../configs", version_base=None):
            for opt_name in optimizer_configs:
                try:
                    cfg = compose(
                        config_name="config",
                        overrides=[
                            f"optimizer={opt_name}",
                            "data=suide",
                            "model=deeplabv3",
                            "trainer.fast_dev_run=true"
                        ]
                    )
                    
                    # 验证配置加载
                    assert cfg.optimizer is not None, f"优化器配置加载失败: {opt_name}"
                    assert hasattr(cfg.optimizer, '_target_'), f"缺少_target_: {opt_name}"
                    assert hasattr(cfg.optimizer, 'lr'), f"缺少学习率: {opt_name}"
                    
                    # 创建简单模型测试实例化
                    model = torch.nn.Linear(10, 1)
                    optimizer = hydra.utils.instantiate(cfg.optimizer, params=model.parameters())
                    
                    assert optimizer is not None, f"优化器实例化失败: {opt_name}"
                    assert isinstance(optimizer, torch.optim.Optimizer), f"不是有效的优化器: {opt_name}"
                    
                    print(f"✅ {opt_name} 优化器配置测试通过")
                    
                except Exception as e:
                    pytest.fail(f"优化器 {opt_name} 测试失败: {e}")
    
    def test_optimizer_parameters(self):
        """测试优化器参数配置的完整性"""
        
        with initialize(config_path="../configs", version_base=None):
            # 测试AdamW的完整参数
            cfg = compose(
                config_name="config",
                overrides=["optimizer=adamw", "data=suide"]
            )
            
            adamw_cfg = cfg.optimizer
            assert adamw_cfg.lr == 1e-4, "AdamW学习率不正确"
            assert adamw_cfg.weight_decay == 1e-2, "AdamW权重衰减不正确"
            assert adamw_cfg.betas == [0.9, 0.999], "AdamW beta参数不正确"
            
            # 测试SGD的完整参数
            cfg = compose(
                config_name="config", 
                overrides=["optimizer=sgd", "data=suide"]
            )
            
            sgd_cfg = cfg.optimizer
            assert sgd_cfg.lr == 1e-2, "SGD学习率不正确"
            assert sgd_cfg.momentum == 0.9, "SGD动量不正确"
            assert sgd_cfg.nesterov == True, "SGD Nesterov设置不正确"
            
            print("✅ 优化器参数配置测试通过")


class TestExtendedSchedulers:
    """测试扩展的调度器配置"""
    
    def setup_method(self):
        GlobalHydra.instance().clear()
    
    def teardown_method(self):
        GlobalHydra.instance().clear()
    
    def test_new_scheduler_configs(self):
        """测试新增的调度器配置"""
        
        # 测试所有调度器配置
        scheduler_configs = [
            'step', 'cosine', 'exponential', 
            'polynomial', 'reduce_on_plateau'
        ]
        
        with initialize(config_path="../configs", version_base=None):
            for sched_name in scheduler_configs:
                try:
                    cfg = compose(
                        config_name="config",
                        overrides=[
                            f"scheduler={sched_name}",
                            "optimizer=adamw",
                            "data=suide",
                            "model=deeplabv3",
                            "trainer.fast_dev_run=true"
                        ]
                    )
                    
                    # 验证配置加载
                    assert cfg.scheduler is not None, f"调度器配置加载失败: {sched_name}"
                    assert hasattr(cfg.scheduler, '_target_'), f"缺少_target_: {sched_name}"
                    
                    # 创建简单模型和优化器测试实例化
                    model = torch.nn.Linear(10, 1)
                    optimizer = torch.optim.AdamW(model.parameters(), lr=0.001)
                    
                    # 特殊处理ReduceLROnPlateau
                    if sched_name == 'reduce_on_plateau':
                        scheduler = hydra.utils.instantiate(cfg.scheduler, optimizer=optimizer)
                    else:
                        scheduler = hydra.utils.instantiate(cfg.scheduler, optimizer=optimizer)
                    
                    assert scheduler is not None, f"调度器实例化失败: {sched_name}"
                    
                    print(f"✅ {sched_name} 调度器配置测试通过")
                    
                except Exception as e:
                    pytest.fail(f"调度器 {sched_name} 测试失败: {e}")
    
    def test_warmup_schedulers(self):
        """测试Warmup调度器功能"""
        
        # 创建测试模型和优化器
        model = torch.nn.Linear(10, 1)
        optimizer = torch.optim.AdamW(model.parameters(), lr=0.001)
        
        # 测试WarmupCosineAnnealingLR
        warmup_cosine = WarmupCosineAnnealingLR(
            optimizer=optimizer,
            warmup_epochs=5,
            max_epochs=20,
            warmup_start_lr=1e-6,
            eta_min=1e-6
        )
        
        # 测试warmup阶段
        initial_lr = warmup_cosine.get_lr()[0]
        assert initial_lr == 1e-6, "Warmup初始学习率不正确"
        
        # 模拟几个epoch
        for epoch in range(10):
            warmup_cosine.step()
            current_lr = warmup_cosine.get_lr()[0]
            assert current_lr > 0, f"第{epoch}个epoch学习率异常"
        
        # 测试WarmupPolynomialLR
        warmup_poly = WarmupPolynomialLR(
            optimizer=optimizer,
            warmup_epochs=3,
            max_epochs=15,
            power=0.9
        )
        
        for epoch in range(8):
            warmup_poly.step()
            current_lr = warmup_poly.get_lr()[0]
            assert current_lr > 0, f"Polynomial调度器第{epoch}个epoch学习率异常"
        
        print("✅ Warmup调度器功能测试通过")


class TestOptimizerSchedulerCombinations:
    """测试优化器和调度器组合"""
    
    def setup_method(self):
        GlobalHydra.instance().clear()
    
    def teardown_method(self):
        GlobalHydra.instance().clear()
    
    def test_segmentation_best_practices(self):
        """测试语义分割最佳实践组合"""
        
        # 测试经典组合
        combinations = [
            # CNN + SGD + Polynomial
            {
                'optimizer': 'sgd',
                'scheduler': 'polynomial',
                'description': 'CNN经典组合'
            },
            # Transformer + AdamW + Cosine
            {
                'optimizer': 'adamw', 
                'scheduler': 'cosine',
                'description': 'Transformer现代组合'
            },
            # 通用 + Adam + Step
            {
                'optimizer': 'adam',
                'scheduler': 'step', 
                'description': '通用稳定组合'
            }
        ]
        
        with initialize(config_path="../configs", version_base=None):
            for combo in combinations:
                try:
                    cfg = compose(
                        config_name="config",
                        overrides=[
                            f"optimizer={combo['optimizer']}",
                            f"scheduler={combo['scheduler']}",
                            "data=suide",
                            "model=deeplabv3",
                            "trainer.fast_dev_run=true"
                        ]
                    )
                    
                    # 验证配置组合
                    assert cfg.optimizer is not None, f"优化器配置失败: {combo['description']}"
                    assert cfg.scheduler is not None, f"调度器配置失败: {combo['description']}"
                    
                    # 测试实例化
                    model = torch.nn.Linear(10, 1)
                    optimizer = hydra.utils.instantiate(cfg.optimizer, params=model.parameters())
                    scheduler = hydra.utils.instantiate(cfg.scheduler, optimizer=optimizer)
                    
                    assert optimizer is not None, f"优化器实例化失败: {combo['description']}"
                    assert scheduler is not None, f"调度器实例化失败: {combo['description']}"
                    
                    print(f"✅ {combo['description']} 组合测试通过")
                    
                except Exception as e:
                    pytest.fail(f"组合 {combo['description']} 测试失败: {e}")
    
    def test_with_segmentation_module(self):
        """测试与SegmentationModule的集成"""
        
        with initialize(config_path="../configs", version_base=None):
            with warnings.catch_warnings():
                warnings.filterwarnings("ignore", message=".*self.trainer.*")
                
                cfg = compose(
                    config_name="config",
                    overrides=[
                        "model=deeplabv3",
                        "optimizer=adamw",
                        "scheduler=cosine",
                        "loss=cross_entropy",
                        "data=suide",
                        "trainer.fast_dev_run=true"
                    ]
                )
                
                # 实例化SegmentationModule
                model = hydra.utils.instantiate(
                    cfg.model,
                    optimizer_cfg=cfg.optimizer,
                    scheduler_cfg=cfg.scheduler,
                    loss_cfg=cfg.loss,
                    _recursive_=False
                )
                
                assert model is not None, "SegmentationModule实例化失败"
                
                # 测试优化器配置
                optimizer_config = model.configure_optimizers()
                assert optimizer_config is not None, "优化器配置失败"
                
                print("✅ SegmentationModule集成测试通过")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "--tb=short", "-s"])
