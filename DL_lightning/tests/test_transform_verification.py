#!/usr/bin/env python3
"""
验证Transform配置是否真正生效
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

import yaml
import numpy as np
from omegaconf import DictConfig, OmegaConf
import torch

def test_transform_creation():
    """测试Transform创建是否成功"""
    
    print("🔧 验证Transform配置是否真正生效")
    print("=" * 80)
    
    try:
        from src.data.components.transforms import create_transforms_from_hydra_config
        
        # 加载配置文件
        suide_path = Path(__file__).parent.parent / "configs" / "data" / "suide.yaml"
        loveda_path = Path(__file__).parent.parent / "configs" / "data" / "loveda.yaml"
        
        with open(suide_path, 'r') as f:
            suide_config = yaml.safe_load(f)
        
        with open(loveda_path, 'r') as f:
            loveda_config = yaml.safe_load(f)
        
        print("\n📋 **1. SuiDe Transform测试**")
        print("-" * 50)
        
        # 正确提取transform_config
        suide_transform_config = OmegaConf.create(suide_config['transform_config'])
        
        print(f"  配置内容: {suide_transform_config}")
        print(f"  图像尺寸: {suide_transform_config.image_size}")
        print(f"  增强配置: {suide_transform_config.augmentation}")
        
        # 创建训练transform
        try:
            suide_train_transform = create_transforms_from_hydra_config(
                suide_transform_config, split='train'
            )
            print("  ✅ SuiDe训练transform创建成功")
            print(f"  Transform类型: {type(suide_train_transform)}")
            
            # 创建验证transform
            suide_val_transform = create_transforms_from_hydra_config(
                suide_transform_config, split='val'
            )
            print("  ✅ SuiDe验证transform创建成功")
            
        except Exception as e:
            print(f"  ❌ SuiDe transform创建失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print("\n📋 **2. LoveDA Transform测试**")
        print("-" * 50)
        
        # 正确提取transform_config
        loveda_transform_config = OmegaConf.create(loveda_config['transform_config'])
        
        print(f"  配置内容: {loveda_transform_config}")
        print(f"  图像尺寸: {loveda_transform_config.image_size}")
        print(f"  增强配置: {loveda_transform_config.augmentation}")
        
        # 创建训练transform
        try:
            loveda_train_transform = create_transforms_from_hydra_config(
                loveda_transform_config, split='train'
            )
            print("  ✅ LoveDA训练transform创建成功")
            print(f"  Transform类型: {type(loveda_train_transform)}")
            
            # 创建验证transform
            loveda_val_transform = create_transforms_from_hydra_config(
                loveda_transform_config, split='val'
            )
            print("  ✅ LoveDA验证transform创建成功")
            
        except Exception as e:
            print(f"  ❌ LoveDA transform创建失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 测试transform实际应用
        print("\n📋 **3. Transform实际应用测试**")
        print("-" * 50)
        
        # 创建测试图像和掩码
        test_image = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
        test_mask = np.random.randint(0, 14, (512, 512), dtype=np.uint8)
        
        print(f"  测试图像shape: {test_image.shape}")
        print(f"  测试掩码shape: {test_mask.shape}")
        
        # 测试SuiDe transform
        try:
            suide_result = suide_train_transform(test_image, test_mask)
            print(f"  ✅ SuiDe transform应用成功")
            print(f"    输出图像shape: {suide_result['image'].shape}")
            print(f"    输出掩码shape: {suide_result['mask'].shape}")
            print(f"    图像类型: {type(suide_result['image'])}")
            print(f"    掩码类型: {type(suide_result['mask'])}")
        except Exception as e:
            print(f"  ❌ SuiDe transform应用失败: {e}")
        
        # 测试LoveDA transform (需要调整图像尺寸)
        test_image_1024 = np.random.randint(0, 255, (1024, 1024, 3), dtype=np.uint8)
        test_mask_1024 = np.random.randint(0, 7, (1024, 1024), dtype=np.uint8)
        
        try:
            loveda_result = loveda_train_transform(test_image_1024, test_mask_1024)
            print(f"  ✅ LoveDA transform应用成功")
            print(f"    输出图像shape: {loveda_result['image'].shape}")
            print(f"    输出掩码shape: {loveda_result['mask'].shape}")
            print(f"    图像类型: {type(loveda_result['image'])}")
            print(f"    掩码类型: {type(loveda_result['mask'])}")
        except Exception as e:
            print(f"  ❌ LoveDA transform应用失败: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_augmentation_parameters():
    """验证增强参数是否正确应用"""
    
    print("\n📋 **4. 增强参数验证**")
    print("-" * 50)
    
    try:
        from src.data.components.transforms import TransformComposer
        
        # 测试SuiDe增强参数
        suide_config = OmegaConf.create({
            'image_size': 512,
            'mean': [0.485, 0.456, 0.406],
            'std': [0.229, 0.224, 0.225],
            'augmentation': {
                'random_crop': True,
                'random_rotate90': True,
                'flip': True,
                'transpose': False,  # SuiDe特点
                'random_brightness_contrast': True,
                'brightness_limit': 0.2,  # SuiDe更强
                'contrast_limit': 0.2,    # SuiDe更强
                'cutout': False,           # SuiDe未启用
                'cutout_max_size': 0.1
            }
        })
        
        # 测试LoveDA增强参数
        loveda_config = OmegaConf.create({
            'image_size': 1024,
            'mean': [0.485, 0.456, 0.406],
            'std': [0.229, 0.224, 0.225],
            'augmentation': {
                'random_crop': True,
                'random_rotate90': True,
                'flip': True,
                'transpose': True,         # LoveDA启用
                'random_brightness_contrast': True,
                'brightness_limit': 0.15,  # LoveDA更保守
                'contrast_limit': 0.15,    # LoveDA更保守
                'cutout': True,            # LoveDA启用
                'cutout_max_size': 0.05    # LoveDA更小
            }
        })
        
        print("  🎯 SuiDe增强参数验证:")
        suide_transform = TransformComposer.create_transforms_from_config(suide_config, 'train')
        print(f"    ✅ 创建成功，包含增强: transpose={suide_config.augmentation.transpose}")
        print(f"    ✅ 亮度限制: {suide_config.augmentation.brightness_limit}")
        print(f"    ✅ Cutout启用: {suide_config.augmentation.cutout}")
        
        print("  🎯 LoveDA增强参数验证:")
        loveda_transform = TransformComposer.create_transforms_from_config(loveda_config, 'train')
        print(f"    ✅ 创建成功，包含增强: transpose={loveda_config.augmentation.transpose}")
        print(f"    ✅ 亮度限制: {loveda_config.augmentation.brightness_limit}")
        print(f"    ✅ Cutout启用: {loveda_config.augmentation.cutout}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 增强参数验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_datamodule_transform_usage():
    """验证DataModule是否正确使用transform配置"""
    
    print("\n📋 **5. DataModule Transform使用验证**")
    print("-" * 50)
    
    try:
        from src.data.suide_datamodule import SuiDeDataModule
        from src.data.loveda_datamodule import LoveDADataModule
        
        # 加载配置文件
        suide_path = Path(__file__).parent.parent / "configs" / "data" / "suide.yaml"
        with open(suide_path, 'r') as f:
            suide_config = yaml.safe_load(f)
        
        # 测试SuiDe DataModule是否使用transform配置
        print("  🎯 SuiDe DataModule Transform测试:")
        
        dataset_config = OmegaConf.create(suide_config['dataset_config'])
        dataset_config.data_dir = "data/Data_SRC/Dataset_v2.2"
        dataset_config.class_info_path = "data/Data_SRC/Dataset_v2.2/metadata/class_info.json"
        
        transform_config = OmegaConf.create(suide_config['transform_config'])
        dataloader_config = OmegaConf.create({'batch_size': 1, 'num_workers': 0})
        
        dm = SuiDeDataModule(
            dataset_config=dataset_config,
            transform_config=transform_config,  # 传递transform配置
            dataloader_config=dataloader_config
        )
        
        dm.setup('fit')
        print("    ✅ SuiDe DataModule setup成功，transform配置已传递")
        
        # 测试数据加载
        train_loader = dm.train_dataloader()
        batch = next(iter(train_loader))
        print(f"    ✅ 数据加载成功，图像shape: {batch['image'].shape}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ DataModule transform测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始Transform配置验证...")
    
    success1 = test_transform_creation()
    success2 = test_augmentation_parameters()
    success3 = test_datamodule_transform_usage()
    
    print("\n" + "=" * 80)
    print("📊 **验证总结**")
    print("=" * 80)
    
    if success1 and success2 and success3:
        print("🎉 Transform配置验证全部成功！")
        print("✅ Transform创建正常")
        print("✅ 增强参数正确应用")
        print("✅ DataModule正确使用transform配置")
        print("✅ 配置文件中的增强策略真正生效")
    else:
        print("💥 Transform配置验证失败！")
        if not success1:
            print("❌ Transform创建失败")
        if not success2:
            print("❌ 增强参数验证失败")
        if not success3:
            print("❌ DataModule transform使用失败")
