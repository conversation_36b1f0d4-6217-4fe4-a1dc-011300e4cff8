# DL_Framework 技术概览

> 本文面向开源社区，概述 **DL_Framework** 的核心组件与运行流程。
> 所有流程图均使用 [Mermaid](https://mermaid.js.org/) 语法，可在 GitHub / Gitee 等平台直接渲染。

---

## 1. 顶层架构

```mermaid
graph TD
    A[configs/config.yaml] -->|加载| B(Config)
    B --> C[ExperimentManager]
    B --> D[create_model]
    B --> E[create_data_loaders]
    B --> F[OptimizerFactory]
    B --> G[SchedulerFactory]
    D --> H[torch.nn.Module]
    E --> I[DataLoader(s)]
    F --> J[torch.optim]
    G --> K[LR Scheduler]
    H --> L(Trainer)
    I --> L
    J --> L
    K --> L
    C --> L
    L --> M[CallbackList]
    M --> N[Training / Validation Loop]
```

**说明**：
1. `scripts/train.py` 解析命令行后，首先读取顶层 YAML，`Config` 递归加载所有子配置。
2. 实验目录 & 日志由 `ExperimentManager` 统一管理。
3. 核心组件（模型、数据、优化器、调度器）均由 *Factory* 根据配置生成。
4. `Trainer` 负责事件驱动的训练循环，其行为由一组 `Callback` 扩展。

---

## 2. 数据管道

```mermaid
flowchart LR
    subgraph DatasetFactory
        C1[type & split] --> C2[TransformComposer]
        C1 --> C3[SuiDeDatasetV2]
    end
    C3 -->|torch.utils.data| C4(DataLoader)
    C2 --> C3
    C4 -->|batch| C5(Trainer)
```

**要点**：
- `TransformComposer` 依据配置动态组合 `albumentations / torchvision` 等增强。
- 分布式训练由 `create_distributed_sampler` 注入 `Sampler`，保证多卡数据划分与随机种子一致。

---

## 3. 训练流程与回调

```mermaid
sequenceDiagram
    participant T as Trainer
    participant L as CallbackList
    participant C as ConcreteCallback
    T->>L: on_train_begin
    loop epoch
        T->>L: on_epoch_begin
        loop batch
            T->>L: on_batch_begin
            T->>C: forward & backward
            T->>L: on_batch_end
        end
        T->>L: on_validation_end
        T->>L: on_epoch_end
    end
    T->>L: on_train_end
```

**内置回调**：`RichProgress`、`Metrics`、`Checkpoint`、`EarlyStopping`、`TensorBoard`、`LRScheduler`、`TuneReporter` 等。

---

## 4. 配置系统

```mermaid
graph LR
    A[主配置] --> B{{含 name 字段?}}
    B -- 是 --> C[加载子配置 configs/<group>/<name>.yaml]
    C --> D[深度合并 (_deep_update)]
    D --> E[变量解析 ${...}]
    E --> F[路径解析 <相对→绝对>]
    B -- 否 --> E
```

**特性**：
- 支持多级继承与覆盖，变量引用可跨文件。
- 自动将相对路径解析为绝对路径，避免因工作目录不同导致的路径错误。

---

## 5. 实验管理

```mermaid
graph TD
    S[ExperimentManager] --> P1[创建 experiments/<timestamp>_<model>_<dataset>]
    S --> P2[保存配置快照]
    S --> P3[setup_logging → log.txt]
    S --> P4[get_tensorboard_dir]
    S --> P5[管理 checkpoints]
```

**功能**：
- 统一实验版本号，方便对比。
- 自动保留 N 个最近检查点，过期的自动删除。

---

## 6. 模型与损失工厂

```mermaid
flowchart TD
    subgraph ModelFactory
        M1[name] --> M2[registry[name]]
        M2 --> M3[实例化 nn.Module]
    end
    subgraph LossFactory
        L1[name] --> L2[registry[name]]
        L2 --> L3[支持组合权重]
    end
```

**扩展方法**：
1. 在 `src/models/` 新建模型实现，并在其 `__init__.py` 注册到字典。  
2. 写配置 `configs/model/your_model.yaml` 并在主 YAML 中 `model.name: your_model`。  
3. 损失同理，可在组合损失中调整 `components: {ce: 0.5, dice: 0.5}` 权重。

---

### 参考
- 项目主页：<https://github.com/your-org/DL_Framework>
- Issues 与讨论区欢迎反馈与 PR！ 