# DL_Framework 用户手册

本文档将指导您如何使用我们重构后的现代化深度学习框架。

## 1. 核心设计理念

新框架的核心设计理念是**"关注点分离" (Separation of Concerns)**，通过**基于回调 (Callback-based)** 的架构实现。

- **Trainer (`src/training/trainer.py`)**: 框架的"底盘"。它只负责最核心、最稳定的训练与验证循环，不关心任何具体的功能实现。
- **Callbacks (`src/training/callbacks.py`)**: 框架的"可插拔配件"。如日志记录、检查点保存、早停、指标计算等所有附加功能，都被实现为独立的 `Callback` 类。
- **Factories (`src/models/factory.py`, etc.)**: 框架的"总装厂"。它们根据配置文件，自动创建和组装模型、优化器、损失函数等核心组件。

这种设计带来了极高的**灵活性**、**可扩展性**和**可维护性**。您可以像搭乐高一样，通过配置文件自由组合所需的功能，而无需改动框架核心代码。

## 2. 配置文件 (`config.yaml`)

配置文件是驱动整个框架的"蓝图"。除了模型、数据、损失函数等基本配置外，新的核心在于 `callbacks` 部分。

### 2.1 配置回调

您可以在您的 `.yaml` 配置文件中添加一个 `callbacks` 字段来精细化控制训练行为。

```yaml
# ... (其他配置如 model, dataset, train)

# 回调函数配置
callbacks:
  # 检查点保存配置
  checkpoint:
    enabled: true
    monitor: 'miou'      # 监控的指标, e.g., 'miou', 'val_loss'
    mode: 'max'          # 'max' 表示指标越大越好, 'min' 表示越小越好
    save_freq: 1         # 每隔多少个 epoch 保存一次检查点
    max_kept: 5          # 最多保留多少个检查点文件

  # 早停配置
  early_stopping:
    enabled: true        # 是否启用早停
    monitor: 'miou'      # 监控的指标
    mode: 'max'          # 'max' 或 'min'
    patience: 10         # 指标在 10 个 epoch 内不改善则停止训练
```

**说明:**
- 如果您不提供 `callbacks` 配置，框架会默认启用 `CheckpointCallback`。
- 将 `enabled: false` 可以禁用对应的回调。

## 3. 如何运行

### 3.1 运行训练

使用 `DL_Framework/scripts/train.py` 脚本来启动训练。您必须通过 `--config` 参数指定配置文件的路径。

**命令格式:**
```bash
python DL_Framework/scripts/train.py --config [你的配置文件路径]
```

**示例:**
```bash
python DL_Framework/scripts/train.py --config configs/deeplabv3plus_suide_v2.1.yaml
```

训练过程中的所有日志、检查点和输出文件，都将保存在配置文件中 `project.output_dir` 和 `project.name` 指定的实验目录下。

### 3.2 运行超参数搜索

我们已经集成了强大的 `Ray Tune` 工具来进行超参数搜索。

**准备工作:**
1.  确保您已经安装了相关依赖：
    ```bash
    pip install "ray[tune]" hyperopt
    ```
2.  打开 `DL_Framework/scripts/tune.py` 脚本。
3.  修改 `search_space` 字典，定义您想要搜索的超参数及其范围。
4.  根据需要调整 `num_samples` (总试验次数) 和其他 Ray Tune 配置。

**启动命令:**
```bash
python DL_Framework/scripts/tune.py
```

脚本会自动运行多个试验，并最终在控制台报告找到的最佳超参数组合。所有试验的详细结果将保存在 `experiments/tune_results` 目录下。

## 4. 框架结构速查

- **`Doc/`**: 存放框架的文档。
- **`configs/`**: 存放所有 YAML 配置文件。
- **`DL_Framework/`**: 项目主代码。
  - **`scripts/`**: 可执行脚本。
    - `train.py`: 主训练脚本。
    - `tune.py`: 超参数搜索脚本。
  - **`src/`**: 框架核心源码。
    - **`data/`**: 数据加载和处理。
    - **`losses/`**: 损失函数。
    - **`models/`**: 模型定义。
    - **`training/`**: 训练核心逻辑。
      - `trainer.py`: 核心训练器。
      - `callbacks.py`: 所有回调实现。
      - `optimizer.py`, `scheduler.py`: 优化器和调度器工厂。
    - **`utils/`**: 工具函数。
      - `config.py`: 配置加载。
      - `checkpoint.py`: 检查点管理。
      - `distributed.py`: 分布式训练支持。
- **`experiments/`**: 保存所有实验结果的默认目录。 