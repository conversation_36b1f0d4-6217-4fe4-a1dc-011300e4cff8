"""
TensorBoard 日志处理器模块

封装了所有使用 TensorBoard 进行可视化记录的功能。
- 混淆矩阵绘图
"""

import numpy as np
import matplotlib.pyplot as plt
import torch
import torchvision
from torch.utils.tensorboard import SummaryWriter
from typing import Optional

def _generate_color_map(num_classes: int) -> torch.Tensor:
    """为不同类别生成一个固定的颜色映射表"""
    # 使用固定的随机种子确保每次颜色都一样
    np.random.seed(42)
    color_map = np.random.randint(0, 255, size=(num_classes, 3), dtype=np.uint8)
    # 将背景(类别0)设置为黑色，以提高对比度
    color_map[0] = [0, 0, 0]
    return torch.from_numpy(color_map)

def _map_masks_to_color(masks: torch.Tensor, color_map: torch.Tensor, ignore_index: Optional[int]) -> torch.Tensor:
    """将单通道的类别掩码映射为彩色的RGB图像"""
    
    # 复制掩码以进行可视化，避免修改原始数据
    vis_masks = masks.clone()

    # 如果存在忽略索引，则将其值替换为背景类（0），以避免索引越界
    if ignore_index is not None:
        vis_masks[vis_masks == ignore_index] = 0

    color_masks = color_map[vis_masks.long()].cpu()
    # (B, H, W, 3) -> (B, 3, H, W)
    return color_masks.permute(0, 3, 1, 2).float() / 255.0

def log_segmentation_predictions(
    writer: SummaryWriter,
    epoch: int,
    images: torch.Tensor,
    true_masks: torch.Tensor,
    pred_masks: torch.Tensor,
    num_classes: int,
    ignore_index: Optional[int],
    max_images: int = 8
):
    """
    记录分割模型的预测结果到TensorBoard。
    将 (原图, 真实标签, 预测结果) 并排展示。
    """
    # 只取一部分图片进行可视化，避免图像过大
    images = images[:max_images]
    true_masks = true_masks[:max_images]
    pred_masks = pred_masks[:max_images]

    # 反归一化图像，以便于观察。这里假设是一个标准的ImageNet归一化。
    # 如果您的归一化方式不同，需要调整这里的均值和标准差。
    mean = torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1)
    std = torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1)
    images_unnorm = images.cpu() * std + mean
    images_unnorm = torch.clamp(images_unnorm, 0, 1)

    # 生成颜色映射表并将掩码转换为彩色图
    color_map = _generate_color_map(num_classes)
    true_masks_color = _map_masks_to_color(true_masks.cpu(), color_map, ignore_index)
    pred_masks_color = _map_masks_to_color(pred_masks.cpu(), color_map, ignore_index)

    # 将三组图片拼接在一起 (B, 3, H, W*3)
    combined_images = torch.cat([images_unnorm, true_masks_color, pred_masks_color], dim=3)

    # 创建网格
    grid = torchvision.utils.make_grid(combined_images, nrow=1) # 每行显示一个样本

    writer.add_image('Images/Segmentation_Predictions', grid, epoch)

def log_confusion_matrix_figure(
    writer: SummaryWriter, 
    epoch: int, 
    cm_matrix: np.ndarray
):
    """
    根据给定的混淆矩阵(numpy array)绘制图像，并将其写入TensorBoard。

    Args:
        writer: TensorBoard SummaryWriter 实例。
        epoch: 当前的周期数。
        cm_matrix: 混淆矩阵 (numpy array)。
    """
    if cm_matrix.sum() == 0:
        return  # 避免在空矩阵上绘图

    # 归一化以获得更好的颜色可视化
    cm_sum = cm_matrix.sum(axis=1, keepdims=True)
    # 加上一个很小的数避免除以零
    cm_norm = cm_matrix / (cm_sum + 1e-10)
    
    fig, ax = plt.subplots(figsize=(12, 10))
    im = ax.imshow(cm_norm, cmap='viridis')

    ax.set_xticks(np.arange(cm_matrix.shape[1]))
    ax.set_yticks(np.arange(cm_matrix.shape[0]))
    
    plt.setp(ax.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

    # 在热力图上标注原始计数值
    for i in range(cm_matrix.shape[0]):
        for j in range(cm_matrix.shape[1]):
            ax.text(j, i, f"{cm_matrix[i, j]:,}",
                    ha="center", va="center", 
                    color="w" if cm_norm[i, j] < 0.6 else "k", # 动态调整字体颜色
                    fontsize=8)
    
    ax.set_title(f"Confusion Matrix at Epoch {epoch}")
    ax.set_xlabel("Predicted label")
    ax.set_ylabel("True label")
    fig.tight_layout()

    writer.add_figure('Images/Confusion_Matrix', fig, epoch)
    plt.close(fig) # 释放内存
