"""
UNet++语义分割模型实现
带有嵌套跳跃连接的UNet++架构，用于遥感影像语义分割
"""

from typing import Dict, List, Optional, Tuple, Union

import torch
import torch.nn as nn
import torch.nn.functional as F


class DoubleConv(nn.Module):
    """UNet++中的双卷积块"""
    def __init__(self, in_channels: int, out_channels: int, mid_channels: Optional[int] = None):
        super(DoubleConv, self).__init__()
        if not mid_channels:
            mid_channels = out_channels
        
        self.double_conv = nn.Sequential(
            nn.Conv2d(in_channels, mid_channels, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(mid_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(mid_channels, out_channels, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x):
        return self.double_conv(x)


class Down(nn.Module):
    """下采样模块"""
    def __init__(self, in_channels: int, out_channels: int):
        super(Down, self).__init__()
        self.maxpool_conv = nn.Sequential(
            nn.MaxPool2d(2),
            DoubleConv(in_channels, out_channels)
        )
    
    def forward(self, x):
        return self.maxpool_conv(x)


class Up(nn.Module):
    """上采样模块"""
    def __init__(self, in_channels: int, out_channels: int, bilinear: bool = True):
        super(Up, self).__init__()
        
        # 如果使用双线性插值上采样，conv减少一半通道
        if bilinear:
            self.up = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
        else:
            self.up = nn.ConvTranspose2d(in_channels // 2, in_channels // 2, kernel_size=2, stride=2)
    
    def forward(self, x1, x2):
        x1 = self.up(x1)
        
        # 计算需要的padding使两个特征图大小匹配
        diffY = x2.size()[2] - x1.size()[2]
        diffX = x2.size()[3] - x1.size()[3]
        
        x1 = F.pad(x1, [diffX // 2, diffX - diffX // 2, diffY // 2, diffY - diffY // 2])
        
        return x1


class NestedConv(nn.Module):
    """嵌套卷积块，用于处理连接后的特征"""
    def __init__(self, in_channels: int, out_channels: int):
        super(NestedConv, self).__init__()
        self.conv = DoubleConv(in_channels, out_channels)
    
    def forward(self, *inputs):
        # 连接所有输入特征
        if len(inputs) > 1:
            x = torch.cat(inputs, dim=1)
        else:
            x = inputs[0]
        return self.conv(x)


class OutConv(nn.Module):
    """输出卷积层"""
    def __init__(self, in_channels: int, out_channels: int):
        super(OutConv, self).__init__()
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size=1)
    
    def forward(self, x):
        return self.conv(x)


class UNetPlusPlus(nn.Module):
    """
    UNet++语义分割模型
    带有嵌套跳跃连接的U形架构，提高分割精度
    """
    def __init__(self, num_classes: int, in_channels: int = 3, features: List[int] = [64, 128, 256, 512], 
                 deep_supervision: bool = False, bilinear: bool = True, pretrained: bool = False):
        super(UNetPlusPlus, self).__init__()
        
        self.num_classes = num_classes
        self.in_channels = in_channels
        self.bilinear = bilinear
        self.deep_supervision = deep_supervision
        
        # 初始双卷积
        self.inc = DoubleConv(in_channels, features[0])
        
        # 下采样路径
        self.down1 = Down(features[0], features[1])
        self.down2 = Down(features[1], features[2])
        self.down3 = Down(features[2], features[3])
        factor = 2 if bilinear else 1
        self.down4 = Down(features[3], features[3] * 2 // factor)
        
        # 上采样路径
        # 第一层深度
        self.up1_0 = Up(features[1] * 2, features[0], bilinear)
        self.conv1_0 = NestedConv(features[0] * 2, features[0])
        
        # 第二层深度
        self.up2_0 = Up(features[2] * 2, features[1], bilinear)
        self.conv2_0 = NestedConv(features[1] * 2, features[1])
        self.up1_1 = Up(features[1] * 2, features[0], bilinear)
        self.conv1_1 = NestedConv(features[0] * 3, features[0])
        
        # 第三层深度
        self.up3_0 = Up(features[3] * 2, features[2], bilinear)
        self.conv3_0 = NestedConv(features[2] * 2, features[2])
        self.up2_1 = Up(features[2] * 2, features[1], bilinear)
        self.conv2_1 = NestedConv(features[1] * 3, features[1])
        self.up1_2 = Up(features[1] * 2, features[0], bilinear)
        self.conv1_2 = NestedConv(features[0] * 4, features[0])
        
        # 第四层深度
        self.up4_0 = Up(features[3] * 2, features[3], bilinear)
        self.conv4_0 = NestedConv(features[3] * 2, features[3])
        self.up3_1 = Up(features[3] * 2, features[2], bilinear)
        self.conv3_1 = NestedConv(features[2] * 3, features[2])
        self.up2_2 = Up(features[2] * 2, features[1], bilinear)
        self.conv2_2 = NestedConv(features[1] * 4, features[1])
        self.up1_3 = Up(features[1] * 2, features[0], bilinear)
        self.conv1_3 = NestedConv(features[0] * 5, features[0])
        
        # 输出卷积 - 支持深度监督
        self.outc1 = OutConv(features[0], num_classes)
        self.outc2 = OutConv(features[0], num_classes)
        self.outc3 = OutConv(features[0], num_classes)
        self.outc4 = OutConv(features[0], num_classes)
    
    def forward(self, x):
        # 保存原始输入尺寸
        input_size = x.size()[2:]
        
        # 下采样路径
        x0_0 = self.inc(x)
        x1_0 = self.down1(x0_0)
        x2_0 = self.down2(x1_0)
        x3_0 = self.down3(x2_0)
        x4_0 = self.down4(x3_0)
        
        # 第一层深度的节点
        x0_1 = self.conv1_0(x0_0, self.up1_0(x1_0, x0_0))
        
        # 第二层深度的节点
        x1_1 = self.conv2_0(x1_0, self.up2_0(x2_0, x1_0))
        x0_2 = self.conv1_1(x0_0, x0_1, self.up1_1(x1_1, x0_0))
        
        # 第三层深度的节点
        x2_1 = self.conv3_0(x2_0, self.up3_0(x3_0, x2_0))
        x1_2 = self.conv2_1(x1_0, x1_1, self.up2_1(x2_1, x1_0))
        x0_3 = self.conv1_2(x0_0, x0_1, x0_2, self.up1_2(x1_2, x0_0))
        
        # 第四层深度的节点
        x3_1 = self.conv4_0(x3_0, self.up4_0(x4_0, x3_0))
        x2_2 = self.conv3_1(x2_0, x2_1, self.up3_1(x3_1, x2_0))
        x1_3 = self.conv2_2(x1_0, x1_1, x1_2, self.up2_2(x2_2, x1_0))
        x0_4 = self.conv1_3(x0_0, x0_1, x0_2, x0_3, self.up1_3(x1_3, x0_0))
        
        # 输出处理
        if self.deep_supervision:
            output1 = self.outc1(x0_1)
            output2 = self.outc2(x0_2)
            output3 = self.outc3(x0_3)
            output4 = self.outc4(x0_4)
            
            # 确保输出尺寸与输入相同
            if output1.size()[2:] != input_size:
                output1 = F.interpolate(output1, size=input_size, mode='bilinear', align_corners=True)
                output2 = F.interpolate(output2, size=input_size, mode='bilinear', align_corners=True)
                output3 = F.interpolate(output3, size=input_size, mode='bilinear', align_corners=True)
                output4 = F.interpolate(output4, size=input_size, mode='bilinear', align_corners=True)
            
            # 平均所有输出或使用加权平均
            return (output1 + output2 + output3 + output4) / 4
        else:
            output = self.outc4(x0_4)
            if output.size()[2:] != input_size:
                output = F.interpolate(output, size=input_size, mode='bilinear', align_corners=True)
            return output 