"""
模型工厂模块
用于创建和管理不同类型的语义分割模型
支持DeepLabV3+、UNet、UNet++和Swin-UNet等模型架构
"""

import logging
from typing import Any, Dict, Optional, Tuple

import torch
from src.utils.config import Config

from .deeplabv3plus import DeepLabV3Plus
from .modular_adaptive_deeplabv3plus import ModularAdaptiveDeepLabV3Plus
from .swin_unet import SwinUnet
from .unet import UNet
from .unetpp import UNetPlusPlus

# 注册可用的模型类型
AVAILABLE_MODELS = {
    'deeplabv3plus': DeepLabV3Plus,
    'unet': UNet,
    'unetpp': UNetPlusPlus,
    'swin': SwinUnet,
    'modular_adaptive_deeplabv3plus': ModularAdaptiveDeepLabV3Plus,
}

def create_model(config: Config) -> torch.nn.Module:
    """
    根据配置创建模型
    
    Args:
        config: 模型配置对象
        
    Returns:
        创建的模型实例
        
    Raises:
        ValueError: 如果指定的模型类型不支持
    """
    model_name = config.get('model.name').lower()
    
    if model_name not in AVAILABLE_MODELS:
        raise ValueError(f"不支持的模型类型: {model_name}，可用的模型类型: {list(AVAILABLE_MODELS.keys())}")
    
    # 提取通用参数
    model_class = AVAILABLE_MODELS[model_name]
    model_kwargs = {
        'num_classes': config.get('model.num_classes'),
        'in_channels': config.get('model.in_channels'),
        'pretrained': config.get('model.pretrained')
    }
    
    # 提取特定模型参数
    # 合并特定模型参数
    model_details = ""
    if model_name == 'deeplabv3plus':
        backbone = config.get('model.backbone')
        model_kwargs['backbone'] = backbone
        model_details = f"骨干网络：{backbone}"
    elif model_name == 'modular_adaptive_deeplabv3plus':
        # 骨干网络参数
        backbone = config.get('model.backbone')
        model_kwargs['backbone'] = backbone
        model_details = f"骨干网络：{backbone}"

        # 聚合所有插件相关的参数
        plugin_configs = config.get('model.plugins', {})
        if plugin_configs: # 确保 'model.plugins' 存在
            plugin_args = {
                'enable_color_enhancement': plugin_configs.get('color_enhancement', {}).get('enabled', False),
                'enable_edge_enhancement': plugin_configs.get('edge_enhancement', {}).get('enabled', False),
                'enable_attention_enhancement': plugin_configs.get('attention_enhancement', {}).get('enabled', False),
                'enable_histogram_equalization': plugin_configs.get('histogram_equalization', {}).get('enabled', False),
                'enable_color_sensitive_attention': plugin_configs.get('color_sensitive_attention', {}).get('enabled', False),
                'enable_class_balance': plugin_configs.get('class_balance', {}).get('enabled', False),
                'enable_geomorphology_prior': plugin_configs.get('geomorphology_prior', {}).get('enabled', False),
                'use_multi_colorspace': plugin_configs.get('color_enhancement', {}).get('use_multi_colorspace', False)
            }
            model_kwargs.update(plugin_args)

            # 构建详细信息字符串
            enabled_plugins = [name for name, enabled in plugin_args.items() if enabled and name.startswith('enable_')]
            model_details += f"，启用插件：{', '.join(p.replace('enable_', '') for p in enabled_plugins)}"
            if plugin_args.get('use_multi_colorspace'):
                model_details += "，多颜色空间处理：是"
    elif model_name == 'unet' or model_name == 'unetpp':
        features = config.get('model.features')
        bilinear = config.get('model.bilinear')
        model_kwargs['features'] = features
        model_kwargs['bilinear'] = bilinear
        model_details = f"特征通道：{features}，双线性插值：{bilinear}"
        if model_name == 'unetpp':
            deep_supervision = config.get('model.deep_supervision')
            model_kwargs['deep_supervision'] = deep_supervision
            model_details += f"，深度监督：{deep_supervision}"
    elif model_name == 'swin':
        img_size = config.get('model.img_size')
        depths = config.get('model.depths')
        model_kwargs['img_size'] = img_size
        model_kwargs['depths'] = depths
        model_details = f"图像尺寸：{img_size}，层深度：{depths}"
    
    # 创建并返回模型实例
    logger = logging.getLogger(__name__)
    logger.info("="*80)
    logger.info(f"创建模型: {model_name}, 参数: {model_kwargs}")
    model = model_class(**model_kwargs)
    
    # 打印详细模型信息
    logger.info("="*50)
    logger.info(f"模型详细信息:")
    logger.info(f"类型: {model_name.upper()}")
    logger.info(f"类别数: {model_kwargs['num_classes']}")
    logger.info(f"输入通道: {model_kwargs['in_channels']}")
    logger.info(f"预训练: {model_kwargs['pretrained']}")
    logger.info(f"架构细节: {model_details}")
    logger.info("="*50)
    
    return model

def get_model_info(model: torch.nn.Module) -> Tuple[int, float]:
    """
    获取模型的基本信息，包括参数数量和计算量
    
    Args:
        model: 模型实例
        
    Returns:
        (参数数量, 模型大小MB)的元组
    """
    # 计算参数数量
    num_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    # 计算模型大小（MB）
    model_size = sum(p.numel() * p.element_size() for p in model.parameters()) / (1024 * 1024)
    
    return num_params, model_size
