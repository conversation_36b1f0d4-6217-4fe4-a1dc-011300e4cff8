"""
DeepLabV3+语义分割模型实现
基于PyTorch的DeepLabV3+模型，支持多种backbone网络
"""

from typing import Dict, List, Optional

import torch
import torch.nn as nn
import torch.nn.functional as F

# 将 torchvision 作为本模块的强制依赖在顶部导入
# 如果用户使用此模型，则必须安装 torchvision
import torchvision.models as models
from torchvision.models import ResNet50_Weights, ResNet101_Weights


class ASPP(nn.Module):
    """
    Atrous Spatial Pyramid Pooling模块
    用于提取多尺度特征的空洞卷积金字塔池化模块
    """
    def __init__(self, in_channels: int, out_channels: int, rates: List[int] = [6, 12, 18]):
        super(ASPP, self).__init__()
        
        # 1x1卷积
        self.aspp0 = nn.Sequential(
            nn.Conv2d(in_channels, out_channels, 1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
        
        # 带有不同空洞率的3x3卷积
        self.aspp1 = self._make_atrous_branch(in_channels, out_channels, rate=rates[0])
        self.aspp2 = self._make_atrous_branch(in_channels, out_channels, rate=rates[1])
        self.aspp3 = self._make_atrous_branch(in_channels, out_channels, rate=rates[2])
        
        # 全局平均池化分支
        self.global_avg_pool = nn.Sequential(
            nn.AdaptiveAvgPool2d((1, 1)),
            nn.Conv2d(in_channels, out_channels, 1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
        
        # 合并所有分支的输出
        self.output = nn.Sequential(
            nn.Conv2d(out_channels * 5, out_channels, 1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5)
        )
    
    def _make_atrous_branch(self, in_channels: int, out_channels: int, rate: int) -> nn.Sequential:
        """创建空洞卷积分支"""
        return nn.Sequential(
            nn.Conv2d(in_channels, out_channels, 3, padding=rate, dilation=rate, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x):
        size = x.size()
        
        x0 = self.aspp0(x)
        x1 = self.aspp1(x)
        x2 = self.aspp2(x)
        x3 = self.aspp3(x)
        
        # 全局特征
        x4 = self.global_avg_pool(x)
        x4 = F.interpolate(x4, size=size[2:], mode='bilinear', align_corners=True)
        
        # 合并所有特征
        x = torch.cat((x0, x1, x2, x3, x4), dim=1)
        x = self.output(x)
        
        return x


class Decoder(nn.Module):
    """DeepLabV3+解码器模块"""
    def __init__(self, low_level_channels: int, encoder_channels: int):
        super(Decoder, self).__init__()
        
        # 低层次特征处理
        self.low_level_conv = nn.Sequential(
            nn.Conv2d(low_level_channels, 48, 1, bias=False),
            nn.BatchNorm2d(48),
            nn.ReLU(inplace=True)
        )
        
        # 解码器卷积
        self.decoder_conv = nn.Sequential(
            nn.Conv2d(encoder_channels + 48, 256, 3, padding=1, bias=False),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Conv2d(256, 256, 3, padding=1, bias=False),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.1)
        )
    
    def forward(self, x, low_level_feat):
        # 处理低层次特征
        low_level_feat = self.low_level_conv(low_level_feat)
        
        # 上采样编码器输出
        x = F.interpolate(x, size=low_level_feat.size()[2:], mode='bilinear', align_corners=True)
        
        # 连接特征
        x = torch.cat((x, low_level_feat), dim=1)
        
        # 解码器卷积
        x = self.decoder_conv(x)
        
        return x


class ResNetBackbone(nn.Module):
    """ResNet骨干网络，支持ResNet50和ResNet101"""
    def __init__(self, backbone_name: str = 'resnet50', pretrained: bool = True):
        super(ResNetBackbone, self).__init__()
        
        # 选择骨干网络
        if backbone_name == 'resnet50':
            if pretrained:
                backbone = models.resnet50(weights=ResNet50_Weights.DEFAULT)
            else:
                backbone = models.resnet50(weights=None)
        elif backbone_name == 'resnet101':
            if pretrained:
                backbone = models.resnet101(weights=ResNet101_Weights.DEFAULT)
            else:
                backbone = models.resnet101(weights=None)
        else:
            raise ValueError(f"不支持的ResNet骨干网络: {backbone_name}")
        
        # 提取需要的层
        self.layer0 = nn.Sequential(
            backbone.conv1,
            backbone.bn1,
            backbone.relu,
            backbone.maxpool
        )
        self.layer1 = backbone.layer1
        self.layer2 = backbone.layer2
        self.layer3 = backbone.layer3
        self.layer4 = backbone.layer4
        
        # 输出通道数
        if backbone_name in ['resnet50', 'resnet101']:
            self.out_channels = 2048
            self.low_level_channels = 256
    
    def forward(self, x):
        # 提取多层次特征
        x0 = self.layer0(x)  # 1/4
        x1 = self.layer1(x0)  # 1/4
        x2 = self.layer2(x1)  # 1/8
        x3 = self.layer3(x2)  # 1/16
        x4 = self.layer4(x3)  # 1/32
        
        return x4, x1  # 返回主干特征和低层次特征


class DeepLabV3Plus(nn.Module):
    """
    DeepLabV3+语义分割模型
    整合了ASPP模块和编解码器结构
    """
    def __init__(self, num_classes: int, in_channels: int = 3, backbone: str = 'resnet50', 
                 pretrained: bool = True, output_stride: int = 16):
        super(DeepLabV3Plus, self).__init__()
        
        # 保存参数
        self.num_classes = num_classes
        self.backbone_name = backbone
        
        # 创建骨干网络
        self.backbone = ResNetBackbone(backbone, pretrained)
        
        # 创建ASPP模块
        self.aspp = ASPP(
            in_channels=self.backbone.out_channels, 
            out_channels=256,
            rates=[6, 12, 18]
        )
        
        # 创建解码器
        self.decoder = Decoder(
            low_level_channels=self.backbone.low_level_channels,
            encoder_channels=256
        )
        
        # 最终分类头
        self.classifier = nn.Conv2d(256, num_classes, 1)
    
    def forward(self, x):
        # 保存输入大小用于上采样
        input_size = x.size()[2:]
        
        # 特征提取
        features, low_level_features = self.backbone(x)
        
        # ASPP模块处理
        x = self.aspp(features)
        
        # 解码器处理
        x = self.decoder(x, low_level_features)
        
        # 分类头
        x = self.classifier(x)
        
        # 上采样到原始大小
        x = F.interpolate(x, size=input_size, mode='bilinear', align_corners=True)
        
        return x
