"""
模块化自适应DeepLabV3+语义分割模型
采用插件式架构，确保数值稳定性和梯度流通性
每个增强模块都是可选的，可以独立测试和开关
"""

import math
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple

import torch
import torch.nn as nn
import torch.nn.functional as F

# 对于此模型，torchvision 是一个强制依赖，因为主干网络来自 torchvision.models。
# 移除 try-except，让环境依赖问题在导入时直接、快速地失败。
import torchvision
import torchvision.models as models
from torchvision.models import ResNet50_Weights, ResNet101_Weights
from torchvision.models._utils import IntermediateLayerGetter

# ================================
# 基础接口定义
# ================================

class BasePlugin(nn.Module, ABC):
    """所有插件的基础接口"""
    
    def __init__(self):
        super().__init__()
    
    @abstractmethod
    def forward(self, x: torch.Tensor, **kwargs) -> torch.Tensor:
        """插件的前向传播"""
        pass
    
    @abstractmethod
    def get_config(self) -> Dict:
        """获取插件配置"""
        pass


class FeatureEnhancementPlugin(BasePlugin):
    """特征增强插件基类"""
    
    def __init__(self, enabled: bool = True, residual: bool = True):
        super().__init__()
        self.enabled = enabled
        self.residual = residual
    
    def forward(self, x: torch.Tensor, **kwargs) -> torch.Tensor:
        if not self.enabled:
            return x
        
        enhanced = self._enhance(x, **kwargs)
        
        if self.residual and enhanced.shape == x.shape:
            return x + enhanced * 0.1  # 小幅增强，保持稳定性
        else:
            return enhanced
    
    @abstractmethod
    def _enhance(self, x: torch.Tensor, **kwargs) -> torch.Tensor:
        """具体的增强实现"""
        pass


# ================================
# 核心基础模块
# ================================

class ASPPConv(nn.Sequential):
    def __init__(self, in_channels, out_channels, dilation):
        super(ASPPConv, self).__init__(
            nn.Conv2d(in_channels, out_channels, 3, padding=dilation, dilation=dilation, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )

class ASPPPooling(nn.Sequential):
    def __init__(self, in_channels, out_channels):
        super(ASPPPooling, self).__init__(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(in_channels, out_channels, 1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
    def forward(self, x):
        size = x.shape[-2:]
        x = super(ASPPPooling, self).forward(x)
        return F.interpolate(x, size=size, mode='bilinear', align_corners=False)

class ASPP(nn.Module):
    def __init__(self, in_channels, atrous_rates):
        super(ASPP, self).__init__()
        out_channels = 256
        modules = []
        modules.append(nn.Sequential(
            nn.Conv2d(in_channels, out_channels, 1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)))

        rates = tuple(atrous_rates)
        for rate in rates:
            modules.append(ASPPConv(in_channels, out_channels, rate))

        modules.append(ASPPPooling(in_channels, out_channels))

        self.convs = nn.ModuleList(modules)

        self.project = nn.Sequential(
            nn.Conv2d(len(self.convs) * out_channels, out_channels, 1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5))

    def forward(self, x):
        res = []
        for conv in self.convs:
            res.append(conv(x))
        res = torch.cat(res, dim=1)
        return self.project(res)

class BaseDeepLabV3Plus(nn.Module):
    """
    Standard DeepLabV3+ model using a real torchvision backbone.
    """
    def __init__(self, num_classes: int, in_channels: int = 3, backbone: str = 'resnet50', pretrained: bool = True):
        super().__init__()
        
        if in_channels != 3 and pretrained:
            print("警告: 输入通道数不为3，但预训练模型是在3通道RGB图像上训练的。将忽略`pretrained`参数。")
            pretrained = False

        # Validate backbone
        if 'resnet' not in backbone:
            raise ValueError("This implementation currently only supports ResNet backbones.")
            
        # Get pretrained backbone model
        if backbone == 'resnet50':
            weights = ResNet50_Weights.DEFAULT if pretrained else None
            backbone_model = models.resnet50(weights=weights, replace_stride_with_dilation=[False, True, True])
        elif backbone == 'resnet101':
            weights = ResNet101_Weights.DEFAULT if pretrained else None
            backbone_model = models.resnet101(weights=weights, replace_stride_with_dilation=[False, True, True])
        else:
            raise ValueError(f"Unsupported backbone: {backbone}")

        # Modify input layer if in_channels is not 3
        if in_channels != 3:
            orig_conv = backbone_model.conv1
            backbone_model.conv1 = nn.Conv2d(in_channels, orig_conv.out_channels, 
                                             kernel_size=orig_conv.kernel_size, stride=orig_conv.stride,
                                             padding=orig_conv.padding, bias=orig_conv.bias)

        # Use IntermediateLayerGetter to extract features from different layers
        self.backbone = IntermediateLayerGetter(backbone_model, return_layers={'layer4': 'out', 'layer1': 'low_level'})
        
        backbone_out_channels = 2048  # For ResNet50/101, layer4 has 2048 channels
        backbone_low_level_channels = 256 # For ResNet50/101, layer1 has 256 channels (after first block)
        
        # In ResNet, layer1's output is actually 64 channels for the initial block, 
        # but the first bottleneck block outputs 256. We need to match this.
        # The structure is conv1 -> bn1 -> relu -> maxpool -> layer1.
        # `layer1` in torchvision's IntermediateLayerGetter points to the output of the first stage.
        # For ResNets, this is 256 channels.

        self.aspp = ASPP(in_channels=backbone_out_channels, atrous_rates=[12, 24, 36])
        
        # Decoder
        # The input channels for the decoder are the concatenation of low-level features and upsampled ASPP features
        self.decoder_conv = nn.Sequential(
            nn.Conv2d(backbone_low_level_channels, 48, 1, bias=False),
            nn.BatchNorm2d(48),
            nn.ReLU(inplace=True)
        )
        
        self.decoder_final = nn.Sequential(
            nn.Conv2d(48 + 256, 256, 3, padding=1, bias=False), # 48 from low-level, 256 from aspp
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, padding=1, bias=False),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True)
        )
        
        self.classifier = nn.Conv2d(256, num_classes, 1)
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        input_shape = x.shape[-2:]
        
        features = self.backbone(x)
        x_high = features['out']
        x_low = features['low_level']
        
        aspp_features = self.aspp(x_high)
        
        low_level_processed = self.decoder_conv(x_low)
        
        aspp_upsampled = F.interpolate(aspp_features, size=low_level_processed.shape[-2:], mode='bilinear', align_corners=True)
        
        concatenated = torch.cat([low_level_processed, aspp_upsampled], dim=1)
        
        decoded_features = self.decoder_final(concatenated)
        
        logits = self.classifier(decoded_features)
        
        intermediate_features = {
            'raw_features': x_high,
            'low_level_features': x_low,
            'aspp_features': aspp_features,
            'decoded_features': decoded_features,
            'input': x
        }
        
        return logits, intermediate_features


# ================================
# 增强插件实现
# ================================

class AdaptiveHistogramEqualizationPlugin(FeatureEnhancementPlugin):
    """区域自适应直方图均衡化插件"""
    
    def __init__(self, in_channels: int = 3, num_bins: int = 256, enabled: bool = True, residual: bool = True):
        super().__init__(enabled, residual)
        self.num_bins = num_bins
        self.in_channels = in_channels
        
        # 学习区域分割参数
        self.region_segmenter = nn.Sequential(
            nn.Conv2d(in_channels, 8, 3, padding=1, bias=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(8, 1, 1, bias=True),
            nn.Sigmoid()
        )
        
        # 固定的均衡化强度，避免学习参数导致的不稳定
        self.register_buffer('equalization_strength', torch.tensor(0.1, dtype=torch.float32))
        
        # 初始化小权重
        self._init_small_weights()
    
    def _init_small_weights(self):
        """初始化为小权重，确保稳定性"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.normal_(m.weight, 0, 0.01)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def _enhance(self, x: torch.Tensor, **kwargs) -> torch.Tensor:
        # 生成区域掩码
        region_mask = self.region_segmenter(x)
        
        # 简化的对比度增强，避免复杂的直方图操作
        # 计算全局统计量
        mean_val = x.mean(dim=[2, 3], keepdim=True)
        std_val = x.std(dim=[2, 3], keepdim=True) + 1e-6
        
        # 标准化
        normalized = (x - mean_val) / std_val
        
        # 区域自适应调整
        enhanced = x + self.equalization_strength * normalized * region_mask
        
        return enhanced
    
    def get_config(self) -> Dict:
        return {
            'type': 'adaptive_histogram_equalization',
            'enabled': self.enabled,
            'residual': self.residual,
            'num_bins': self.num_bins
        }


class ColorEnhancementPlugin(FeatureEnhancementPlugin):
    """色彩增强插件 - 多颜色空间并行处理版本"""
    
    def __init__(self, in_channels: int = 3, enabled: bool = True, residual: bool = True, use_multi_colorspace: bool = True):
        super().__init__(enabled, residual)
        self.in_channels = in_channels
        self.use_multi_colorspace = use_multi_colorspace
        
        # 注意：多颜色空间处理仍然假设RGB输入，输出也是RGB
        output_channels = 3  # 始终输出RGB
        
        if use_multi_colorspace:
            # 多颜色空间并行处理
            # RGB分支
            self.rgb_branch = nn.Sequential(
                nn.Conv2d(in_channels, 8, 3, padding=1, bias=True),
                nn.ReLU(inplace=True),
                nn.Conv2d(8, output_channels, 1, bias=True)
            )
            
            # HSV分支 (处理转换后的HSV特征)
            self.hsv_branch = nn.Sequential(
                nn.Conv2d(in_channels, 8, 3, padding=1, bias=True),
                nn.ReLU(inplace=True),
                nn.Conv2d(8, output_channels, 1, bias=True)
            )
            
            # Lab分支 (处理转换后的Lab特征)
            self.lab_branch = nn.Sequential(
                nn.Conv2d(in_channels, 8, 3, padding=1, bias=True),
                nn.ReLU(inplace=True),
                nn.Conv2d(8, output_channels, 1, bias=True)
            )
            
            # 颜色空间融合网络
            fusion_input_channels = output_channels * 3  # 3个颜色空间 * output_channels
            self.colorspace_fusion = nn.Sequential(
                nn.Conv2d(fusion_input_channels, 16, 3, padding=1, bias=True),
                nn.ReLU(inplace=True),
                nn.Conv2d(16, output_channels, 1, bias=True),
                nn.Tanh()  # 输出范围[-1, 1]
            )
            
            # 自适应通道选择机制
            self.channel_selector = nn.Sequential(
                nn.AdaptiveAvgPool2d(1),
                nn.Conv2d(fusion_input_channels, 3, 1, bias=True),  # 为每个颜色空间生成权重
                nn.Softmax(dim=1)
            )
        else:
            # 简化的色彩增强网络（原版本）
            self.color_adjust = nn.Sequential(
                nn.Conv2d(in_channels, 16, 3, padding=1, bias=True),
                nn.ReLU(inplace=True),
                nn.Conv2d(16, output_channels, 3, padding=1, bias=True),
                nn.Tanh()  # 输出范围[-1, 1]
            )
        
        # 黄土区域检测（轻量级）
        self.yellow_mask = nn.Sequential(
            nn.Conv2d(in_channels, 8, 3, padding=1, bias=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(8, 1, 1, bias=True),
            nn.Sigmoid()
        )
        
        # 初始化为小权重
        self._init_small_weights()
    
    def _init_small_weights(self):
        """初始化为小权重，确保稳定性"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.normal_(m.weight, 0, 0.01)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def _rgb_to_hsv(self, rgb: torch.Tensor) -> torch.Tensor:
        """RGB到HSV颜色空间转换 (简化版本，保持数值稳定性)"""
        r, g, b = rgb[:, 0:1], rgb[:, 1:2], rgb[:, 2:3]
        
        max_val, _ = torch.max(rgb, dim=1, keepdim=True)
        min_val, _ = torch.min(rgb, dim=1, keepdim=True)
        diff = max_val - min_val
        
        # Value (明度)
        v = max_val
        
        # Saturation (饱和度)
        s = torch.where(max_val > 1e-6, diff / (max_val + 1e-6), torch.zeros_like(max_val))
        
        # Hue (色调) - 简化计算，避免复杂的条件判断
        sqrt_3 = torch.tensor(3.0, device=rgb.device, dtype=rgb.dtype)
        pi_2 = torch.tensor(2 * math.pi, device=rgb.device, dtype=rgb.dtype)
        h = torch.where(diff > 1e-6, 
                       torch.atan2(torch.sqrt(sqrt_3) * (g - b), 2 * r - g - b) / pi_2 + 0.5,
                       torch.zeros_like(max_val))
        
        return torch.cat([h, s, v], dim=1)
    
    def _rgb_to_lab(self, rgb: torch.Tensor) -> torch.Tensor:
        """RGB到Lab颜色空间转换 (简化版本)"""
        # 简化的RGB到Lab转换，保持数值稳定性
        # 使用线性变换近似
        if not hasattr(self, '_transform_matrix') or self._transform_matrix.device != rgb.device:
            self._transform_matrix = torch.tensor([
                [0.412453, 0.357580, 0.180423],
                [0.212671, 0.715160, 0.072169],
                [0.019334, 0.119193, 0.950227]
            ], device=rgb.device, dtype=rgb.dtype)
        
        # 重塑RGB为 (B*H*W, 3)
        b, c, h, w = rgb.shape
        rgb_flat = rgb.permute(0, 2, 3, 1).reshape(-1, 3)
        
        # 应用变换矩阵
        lab_flat = torch.matmul(rgb_flat, self._transform_matrix.t())
        
        # 重塑回原始形状
        lab = lab_flat.reshape(b, h, w, 3).permute(0, 3, 1, 2)
        
        # 标准化到[0,1]范围
        lab = torch.clamp(lab, 0, 1)
        
        return lab
    
    def _enhance(self, x: torch.Tensor, **kwargs) -> torch.Tensor:
        if self.use_multi_colorspace:
            # 多颜色空间并行处理
            # 转换到不同颜色空间
            rgb_features = x
            hsv_features = self._rgb_to_hsv(x)
            lab_features = self._rgb_to_lab(x)
            
            # 各颜色空间分支处理
            rgb_enhanced = self.rgb_branch(rgb_features)
            hsv_enhanced = self.hsv_branch(hsv_features)
            lab_enhanced = self.lab_branch(lab_features)
            
            # 拼接所有颜色空间特征
            all_features = torch.cat([rgb_enhanced, hsv_enhanced, lab_enhanced], dim=1)
            
            # 自适应通道选择
            channel_weights = self.channel_selector(all_features)
            
            # 加权融合不同颜色空间
            weighted_rgb = rgb_enhanced * channel_weights[:, 0:1]
            weighted_hsv = hsv_enhanced * channel_weights[:, 1:2]  
            weighted_lab = lab_enhanced * channel_weights[:, 2:3]
            weighted_features = torch.cat([weighted_rgb, weighted_hsv, weighted_lab], dim=1)
            
            # 最终融合
            color_delta = self.colorspace_fusion(weighted_features) * 0.05
        else:
            # 简化版本处理
            color_delta = self.color_adjust(x) * 0.05
        
        # 黄土区域掩码
        yellow_mask = self.yellow_mask(x)
        
        # 区域自适应增强
        enhanced = x + color_delta * yellow_mask
        
        # 确保在合理范围内
        enhanced = torch.clamp(enhanced, 0, 1)
        
        return enhanced
    
    def get_config(self) -> Dict:
        return {
            'type': 'color_enhancement',
            'enabled': self.enabled,
            'residual': self.residual,
            'use_multi_colorspace': self.use_multi_colorspace
        }


class EdgeEnhancementPlugin(FeatureEnhancementPlugin):
    """边缘增强插件"""
    
    def __init__(self, in_channels: int, enabled: bool = True, residual: bool = True):
        super().__init__(enabled, residual)
        self.in_channels = in_channels
        
        # 边缘检测卷积
        self.edge_conv = nn.Conv2d(in_channels, in_channels, 3, padding=1, groups=in_channels, bias=False)
        
        # 边缘特征处理
        self.edge_process = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // 4, 1, bias=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // 4, in_channels, 1, bias=True),
            nn.Sigmoid()
        )
        
        # 初始化边缘检测为Sobel算子
        self._init_sobel_weights()
    
    def _init_sobel_weights(self):
        """初始化为Sobel边缘检测算子"""
        # 延迟初始化，在第一次前向传播时设置
        self._sobel_initialized = False
    
    def _ensure_sobel_weights(self, device):
        """确保Sobel权重已正确初始化到指定设备"""
        if not self._sobel_initialized:
            sobel_kernel = torch.tensor([[[-1, -2, -1], [0, 0, 0], [1, 2, 1]]], 
                                       dtype=torch.float32, device=device)
            for i in range(self.in_channels):
                self.edge_conv.weight.data[i] = sobel_kernel
            self._sobel_initialized = True
    
    def _enhance(self, x: torch.Tensor, **kwargs) -> torch.Tensor:
        # 确保Sobel权重已初始化到正确设备
        self._ensure_sobel_weights(x.device)
        
        # 边缘检测
        edge_response = torch.abs(self.edge_conv(x))
        
        # 边缘权重
        edge_weights = self.edge_process(edge_response)
        
        # 边缘增强
        enhanced = x * (1 + edge_weights * 0.1)  # 小幅增强
        
        return enhanced
    
    def get_config(self) -> Dict:
        return {
            'type': 'edge_enhancement',
            'enabled': self.enabled,
            'residual': self.residual,
            'in_channels': self.in_channels
        }


class AttentionEnhancementPlugin(FeatureEnhancementPlugin):
    """注意力增强插件"""
    
    def __init__(self, in_channels: int, reduction: int = 16, enabled: bool = True, residual: bool = True):
        super().__init__(enabled, residual)
        
        # 简化的通道注意力
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(in_channels, in_channels // reduction, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // reduction, in_channels, 1),
            nn.Sigmoid()
        )
        
        # 空间注意力
        self.spatial_attention = nn.Sequential(
            nn.Conv2d(2, 1, 7, padding=3, bias=False),
            nn.Sigmoid()
        )
    
    def _enhance(self, x: torch.Tensor, **kwargs) -> torch.Tensor:
        # 通道注意力
        channel_weights = self.channel_attention(x)
        x_channel = x * channel_weights
        
        # 空间注意力
        avg_pool = torch.mean(x_channel, dim=1, keepdim=True)
        max_pool, _ = torch.max(x_channel, dim=1, keepdim=True)
        spatial_input = torch.cat([avg_pool, max_pool], dim=1)
        spatial_weights = self.spatial_attention(spatial_input)
        
        enhanced = x_channel * spatial_weights
        
        return enhanced
    
    def get_config(self) -> Dict:
        return {
            'type': 'attention_enhancement',
            'enabled': self.enabled,
            'residual': self.residual
        }


class ColorSensitiveChannelAttention(FeatureEnhancementPlugin):
    """色彩敏感的通道注意力模块"""
    
    def __init__(self, in_channels: int, reduction: int = 16, enabled: bool = True, residual: bool = True):
        super().__init__(enabled, residual)
        
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        
        # 两个并行的注意力分支
        self.fc = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // reduction, 1, bias=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // reduction, in_channels, 1, bias=True)
        )
        
        # 色彩敏感分支
        self.color_fc = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // reduction, 1, bias=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // reduction, in_channels, 1, bias=True)
        )
        
        self.sigmoid = nn.Sigmoid()
        
    def _enhance(self, x: torch.Tensor, **kwargs) -> torch.Tensor:
        # 标准SE注意力
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        std_attention = self.sigmoid(avg_out + max_out)
        
        # 色彩敏感注意力
        color_out = self.color_fc(self.avg_pool(x))
        color_attention = self.sigmoid(color_out)
        
        # 组合注意力
        combined_attention = (std_attention + color_attention) / 2
        
        return x * combined_attention
    
    def get_config(self) -> Dict:
        return {
            'type': 'color_sensitive_channel_attention',
            'enabled': self.enabled,
            'residual': self.residual
        }


class ClassBalancePlugin(FeatureEnhancementPlugin):
    """类别不平衡处理插件"""
    
    def __init__(self, num_classes: int, feature_channels: int, enabled: bool = True, residual: bool = True):
        super().__init__(enabled, residual)
        self.num_classes = num_classes
        self.feature_channels = feature_channels
        
        # 简化的类别权重预测
        self.class_weight_predictor = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(feature_channels, num_classes, 1),
            nn.Softmax(dim=1)
        )
        
        # 单一的特征增强层
        self.feature_enhance = nn.Sequential(
            nn.Conv2d(feature_channels, feature_channels, 3, padding=1, bias=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(feature_channels, feature_channels, 1, bias=True)
        )
        
    def _enhance(self, x: torch.Tensor, **kwargs) -> torch.Tensor:
        # 预测类别权重
        class_weights = self.class_weight_predictor(x)
        
        # 简单的特征增强
        enhanced_features = self.feature_enhance(x)
        
        # 全局权重调整
        global_weight = class_weights.mean(dim=1, keepdim=True)
        
        # 轻微的残差连接
        output = x + 0.1 * enhanced_features * global_weight
        
        return output
    
    def get_config(self) -> Dict:
        return {
            'type': 'class_balance',
            'enabled': self.enabled,
            'residual': self.residual,
            'num_classes': self.num_classes
        }


class GeomorphologyPriorPlugin(BasePlugin):
    """地貌先验知识插件 - 通过拉普拉斯算子鼓励平滑"""
    
    def __init__(self, num_classes: int, feature_channels: int, enabled: bool = True):
        super().__init__()
        self.enabled = enabled
        self.num_classes = num_classes
        
        # 创建固定的拉普拉斯核
        laplacian_kernel = torch.tensor([[1, 1, 1], [1, -8, 1], [1, 1, 1]], dtype=torch.float32)
        # 调整核的形状以匹配Conv2d的权重 (out_channels, in_channels/groups, H, W)
        self.register_buffer('laplacian_kernel', laplacian_kernel.view(1, 1, 3, 3).repeat(num_classes, 1, 1, 1))
        
        # 学习调整强度
        self.register_buffer('adjustment_strength', torch.tensor(0.01, dtype=torch.float32))

    def forward(self, features: torch.Tensor, class_logits: torch.Tensor, **kwargs) -> torch.Tensor:
        """
        接收logits，应用先验平滑，并返回调整后的logits
        """
        if not self.enabled:
            return class_logits

        # 使用softmax将logits转换为概率
        class_prob = F.softmax(class_logits, dim=1)
        
        # 应用拉普拉斯算子来找到概率图中的高梯度区域（边缘）
        # 使用分组卷积，每个类别的概率图都用自己的核进行卷积
        prior_adjustment = F.conv2d(class_prob, self.laplacian_kernel, padding=1, groups=self.num_classes)
        
        # 将调整应用于原始logits。负反馈：在边缘处抑制logits，鼓励平滑
        return class_logits - self.adjustment_strength * prior_adjustment

    def get_config(self) -> Dict:
        return {
            'type': 'geomorphology_prior',
            'enabled': self.enabled,
            'num_classes': self.num_classes
        }


# ================================
# 插件管理器
# ================================

class PluginManager(nn.Module):
    """插件管理器，负责插件的注册、启用/禁用和执行"""
    
    def __init__(self):
        super().__init__()
        self.plugins = nn.ModuleDict()  # 使用ModuleDict确保插件被正确注册
        self.execution_order = []
    
    def register_plugin(self, name: str, plugin: BasePlugin, position: str = 'append'):
        """注册插件"""
        self.plugins[name] = plugin
        
        if position == 'prepend':
            self.execution_order.insert(0, name)
        else:
            self.execution_order.append(name)
    
    def enable_plugin(self, name: str):
        """启用插件"""
        if name in self.plugins and hasattr(self.plugins[name], 'enabled'):
            self.plugins[name].enabled = True
    
    def disable_plugin(self, name: str):
        """禁用插件"""
        if name in self.plugins and hasattr(self.plugins[name], 'enabled'):
            self.plugins[name].enabled = False
    
    def execute_plugins(self, stage: str, x: torch.Tensor, stage_filter: str = None, **kwargs) -> torch.Tensor:
        """执行特定阶段的插件"""
        for plugin_name in self.execution_order:
            plugin = self.plugins[plugin_name]
            if hasattr(plugin, 'stage') and plugin.stage == stage:
                # 如果指定了stage_filter，只执行匹配的插件
                if stage_filter and plugin_name != stage_filter:
                    continue
                if hasattr(plugin, 'enabled') and plugin.enabled:
                    x = plugin.forward(x, **kwargs)
        return x
    
    def get_plugin_configs(self) -> Dict:
        """获取所有插件的配置"""
        configs = {}
        for name, plugin in self.plugins.items():
            configs[name] = plugin.get_config()
        return configs


# ================================
# 主模型
# ================================

class ModularAdaptiveDeepLabV3Plus(nn.Module):
    """
    模块化自适应DeepLabV3+模型
    采用插件式架构，确保稳定性和可扩展性
    """
    
    def __init__(self, num_classes: int, in_channels: int = 3, backbone: str = 'resnet50', pretrained: bool = True,
                 enable_color_enhancement: bool = True,
                 enable_edge_enhancement: bool = True, 
                 enable_attention_enhancement: bool = True,
                 enable_histogram_equalization: bool = True,
                 enable_color_sensitive_attention: bool = True,
                 enable_class_balance: bool = True,
                 enable_geomorphology_prior: bool = True,
                 use_multi_colorspace: bool = True):
        super(ModularAdaptiveDeepLabV3Plus, self).__init__()
        
        self.num_classes = num_classes
        self.in_channels = in_channels
        
        # 核心基础模型
        self.base_model = BaseDeepLabV3Plus(num_classes, in_channels, backbone, pretrained)
        
        # 插件管理器
        self.plugin_manager = PluginManager()
        
        # 注册预处理插件
        if enable_histogram_equalization:
            hist_eq_plugin = AdaptiveHistogramEqualizationPlugin(in_channels=in_channels, enabled=enable_histogram_equalization)
            hist_eq_plugin.stage = 'preprocessing'
            self.plugin_manager.register_plugin('histogram_equalization', hist_eq_plugin, position='prepend')
        
        if enable_color_enhancement:
            color_plugin = ColorEnhancementPlugin(in_channels=in_channels, enabled=enable_color_enhancement, use_multi_colorspace=use_multi_colorspace)
            color_plugin.stage = 'preprocessing'
            self.plugin_manager.register_plugin('color_enhancement', color_plugin)
        
        # 注册特征增强插件
        if enable_edge_enhancement:
            edge_plugin = EdgeEnhancementPlugin(in_channels=256, enabled=enable_edge_enhancement)
            edge_plugin.stage = 'feature_enhancement'
            self.plugin_manager.register_plugin('edge_enhancement', edge_plugin)
        
        if enable_attention_enhancement:
            attention_plugin = AttentionEnhancementPlugin(in_channels=256, enabled=enable_attention_enhancement)
            attention_plugin.stage = 'feature_enhancement'
            self.plugin_manager.register_plugin('attention_enhancement', attention_plugin)
            
        if enable_color_sensitive_attention:
            color_attention_plugin = ColorSensitiveChannelAttention(in_channels=256, enabled=enable_color_sensitive_attention)
            color_attention_plugin.stage = 'feature_enhancement'
            self.plugin_manager.register_plugin('color_sensitive_attention', color_attention_plugin)
        
        # 注册后处理插件
        if enable_class_balance:
            class_balance_plugin = ClassBalancePlugin(num_classes=num_classes, feature_channels=256, enabled=enable_class_balance)
            class_balance_plugin.stage = 'post_processing'
            self.plugin_manager.register_plugin('class_balance', class_balance_plugin)
        
        if enable_geomorphology_prior:
            geomorphology_plugin = GeomorphologyPriorPlugin(num_classes=num_classes, feature_channels=256, enabled=enable_geomorphology_prior)
            geomorphology_plugin.stage = 'post_processing'
            self.plugin_manager.register_plugin('geomorphology_prior', geomorphology_plugin)
        
        # 特征融合模块（轻量级）
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(256, 256, 3, padding=1, bias=True),
            nn.ReLU(inplace=True),
            nn.Dropout(0.1)
        )
        
        # 最终分类头
        self.final_classifier = nn.Conv2d(256, num_classes, 1)
        
        # 初始化新增模块
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        for m in [self.feature_fusion, self.final_classifier]:
            for module in m.modules():
                if isinstance(module, nn.Conv2d):
                    nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')
                    if module.bias is not None:
                        nn.init.constant_(module.bias, 0)
                elif isinstance(module, nn.BatchNorm2d):
                    nn.init.constant_(module.weight, 1)
                    nn.init.constant_(module.bias, 0)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # 保存输入大小
        input_size = x.size()[2:]
        
        # 预处理阶段：直方图均衡化和色彩增强
        enhanced_input = self.plugin_manager.execute_plugins('preprocessing', x)
        
        # 基础模型前向传播
        logits, intermediate_features = self.base_model(enhanced_input)
        
        # 特征增强阶段：边缘、注意力和色彩敏感注意力增强
        enhanced_features = self.plugin_manager.execute_plugins(
            'feature_enhancement', 
            intermediate_features['decoded_features'],
            **intermediate_features
        )
        
        # 特征融合（包含残差连接）
        if enhanced_features.shape == intermediate_features['decoded_features'].shape:
            fused_features = intermediate_features['decoded_features'] + enhanced_features * 0.1
        else:
            fused_features = enhanced_features
        
        fused_features = self.feature_fusion(fused_features)
        
        # 后处理阶段：类别平衡处理
        balanced_features = self.plugin_manager.execute_plugins(
            'post_processing',
            fused_features,
            stage_filter='class_balance'
        )
        
        # 最终分类
        enhanced_logits = self.final_classifier(balanced_features)
        enhanced_logits = F.interpolate(enhanced_logits, size=input_size, mode='bilinear', align_corners=True)
        
        # 地貌先验知识处理 - 确保特征和logits尺寸匹配
        if 'geomorphology_prior' in self.plugin_manager.plugins:
            geomorphology_plugin = self.plugin_manager.plugins['geomorphology_prior']
            if geomorphology_plugin.enabled:
                # 将balanced_features上采样到与enhanced_logits相同的尺寸
                features_upsampled = F.interpolate(balanced_features, size=input_size, mode='bilinear', align_corners=True)
                enhanced_logits = geomorphology_plugin.forward(features_upsampled, enhanced_logits)
        
        # 结合基础logits和增强logits（加权融合）
        final_logits = 0.7 * logits + 0.3 * enhanced_logits
        
        return final_logits
    
    def enable_plugin(self, plugin_name: str):
        """启用指定插件"""
        self.plugin_manager.enable_plugin(plugin_name)
    
    def disable_plugin(self, plugin_name: str):
        """禁用指定插件"""
        self.plugin_manager.disable_plugin(plugin_name)
    
    def get_plugin_status(self) -> Dict:
        """获取插件状态"""
        return self.plugin_manager.get_plugin_configs()
    
    def get_model_complexity(self) -> Dict:
        """获取模型复杂度信息"""
        total_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        base_params = sum(p.numel() for p in self.base_model.parameters() if p.requires_grad)
        
        return {
            'total_parameters': total_params,
            'base_parameters': base_params,
            'enhancement_parameters': total_params - base_params,
            'parameter_increase_ratio': (total_params - base_params) / base_params * 100
        } 