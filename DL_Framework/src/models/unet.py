"""
UNet语义分割模型实现
经典的UNet架构用于遥感影像语义分割
"""

from typing import Dict, List, Optional, Tuple

import torch
import torch.nn as nn
import torch.nn.functional as F


class DoubleConv(nn.Module):
    """UNet中的双卷积块"""
    def __init__(self, in_channels: int, out_channels: int, mid_channels: Optional[int] = None):
        super(DoubleConv, self).__init__()
        if not mid_channels:
            mid_channels = out_channels
        
        self.double_conv = nn.Sequential(
            nn.Conv2d(in_channels, mid_channels, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(mid_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(mid_channels, out_channels, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x):
        return self.double_conv(x)


class Down(nn.Module):
    """下采样模块"""
    def __init__(self, in_channels: int, out_channels: int):
        super(Down, self).__init__()
        self.maxpool_conv = nn.Sequential(
            nn.MaxPool2d(2),
            DoubleConv(in_channels, out_channels)
        )
    
    def forward(self, x):
        return self.maxpool_conv(x)


class Up(nn.Module):
    """上采样模块"""
    def __init__(self, in_channels: int, out_channels: int, bilinear: bool = True):
        super(Up, self).__init__()
        
        # 如果使用双线性插值上采样，conv减少一半通道
        if bilinear:
            self.up = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
            self.conv = DoubleConv(in_channels, out_channels, in_channels // 2)
        else:
            self.up = nn.ConvTranspose2d(in_channels, in_channels // 2, kernel_size=2, stride=2)
            self.conv = DoubleConv(in_channels, out_channels)
    
    def forward(self, x1, x2):
        x1 = self.up(x1)
        
        # 计算需要的padding使两个特征图大小匹配
        diffY = x2.size()[2] - x1.size()[2]
        diffX = x2.size()[3] - x1.size()[3]
        
        x1 = F.pad(x1, [diffX // 2, diffX - diffX // 2, diffY // 2, diffY - diffY // 2])
        
        # 连接特征
        x = torch.cat([x2, x1], dim=1)
        
        return self.conv(x)


class OutConv(nn.Module):
    """输出卷积层"""
    def __init__(self, in_channels: int, out_channels: int):
        super(OutConv, self).__init__()
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size=1)
    
    def forward(self, x):
        return self.conv(x)


class UNet(nn.Module):
    """
    UNet语义分割模型
    经典的U形架构，适合遥感影像分割
    """
    def __init__(self, num_classes: int, in_channels: int = 3, features: List[int] = [64, 128, 256, 512], 
                 bilinear: bool = True, pretrained: bool = False):
        super(UNet, self).__init__()
        
        self.num_classes = num_classes
        self.in_channels = in_channels
        self.bilinear = bilinear
        
        # 初始双卷积
        self.inc = DoubleConv(in_channels, features[0])
        
        # 下采样路径
        self.down1 = Down(features[0], features[1])
        self.down2 = Down(features[1], features[2])
        self.down3 = Down(features[2], features[3])
        self.down4 = Down(features[3], features[3] * 2 if not bilinear else features[3])
        
        # 上采样路径
        self.up1 = Up(features[3] * 2, features[2], bilinear)
        self.up2 = Up(features[2] * 2, features[1], bilinear)
        self.up3 = Up(features[1] * 2, features[0], bilinear)
        self.up4 = Up(features[0] * 2, features[0], bilinear)
        
        # 输出卷积
        self.outc = OutConv(features[0], num_classes)
    
    def forward(self, x):
        # 下采样路径
        x1 = self.inc(x)        # 特征[64, H, W]
        x2 = self.down1(x1)     # 特征[128, H/2, W/2]
        x3 = self.down2(x2)     # 特征[256, H/4, W/4]
        x4 = self.down3(x3)     # 特征[512, H/8, W/8]
        x5 = self.down4(x4)     # 特征[1024或512, H/16, W/16]
        
        # 上采样路径
        x = self.up1(x5, x4)    # 特征[256, H/8, W/8]
        x = self.up2(x, x3)     # 特征[128, H/4, W/4]
        x = self.up3(x, x2)     # 特征[64, H/2, W/2]
        x = self.up4(x, x1)     # 特征[64, H, W]
        
        # 输出层
        logits = self.outc(x)   # 特征[num_classes, H, W]
        
        return logits
