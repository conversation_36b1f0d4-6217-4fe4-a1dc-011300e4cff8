"""
Swin-UNet语义分割模型实现
基于Swin Transformer的U形架构，用于高精度遥感影像语义分割
"""

from typing import Dict, List, Optional, Tuple, Union

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision
from torchvision.models import ResNet50_Weights


class PatchEmbed(nn.Module):
    """图像分块嵌入模块"""
    def __init__(self, img_size: int = 224, patch_size: int = 4, in_channels: int = 3, embed_dim: int = 96):
        super().__init__()
        self.img_size = (img_size, img_size) if isinstance(img_size, int) else img_size
        self.patch_size = (patch_size, patch_size) if isinstance(patch_size, int) else patch_size
        self.patches_resolution = [self.img_size[0] // self.patch_size[0], self.img_size[1] // self.patch_size[1]]
        self.num_patches = self.patches_resolution[0] * self.patches_resolution[1]
        
        self.in_channels = in_channels
        self.embed_dim = embed_dim
        
        self.proj = nn.Conv2d(in_channels, embed_dim, kernel_size=patch_size, stride=patch_size)
        self.norm = nn.LayerNorm(embed_dim)
    
    def forward(self, x):
        B, C, H, W = x.shape
        # 将输入图像转换为token序列
        x = self.proj(x).flatten(2).transpose(1, 2)  # B, L, C
        x = self.norm(x)
        return x


class WindowAttention(nn.Module):
    """窗口自注意力模块"""
    def __init__(self, dim: int, window_size: int, num_heads: int):
        super().__init__()
        self.dim = dim
        self.window_size = window_size if isinstance(window_size, tuple) else (window_size, window_size)
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5
        
        # 相对位置编码
        self.relative_position_bias_table = nn.Parameter(
            torch.zeros((2 * self.window_size[0] - 1) * (2 * self.window_size[1] - 1), num_heads)
        )
        
        # 定义坐标索引
        coords_h = torch.arange(self.window_size[0])
        coords_w = torch.arange(self.window_size[1])
        coords = torch.stack(torch.meshgrid([coords_h, coords_w], indexing="ij"))  # 2, Wh, Ww
        coords_flatten = torch.flatten(coords, 1)  # 2, Wh*Ww
        
        # 计算相对坐标
        relative_coords = coords_flatten[:, :, None] - coords_flatten[:, None, :]  # 2, Wh*Ww, Wh*Ww
        relative_coords = relative_coords.permute(1, 2, 0).contiguous()  # Wh*Ww, Wh*Ww, 2
        relative_coords[:, :, 0] += self.window_size[0] - 1  # 偏移到从0开始的索引
        relative_coords[:, :, 1] += self.window_size[1] - 1
        relative_coords[:, :, 0] *= 2 * self.window_size[1] - 1
        relative_position_index = relative_coords.sum(-1)  # Wh*Ww, Wh*Ww
        self.register_buffer("relative_position_index", relative_position_index)
        
        self.qkv = nn.Linear(dim, dim * 3, bias=True)
        self.proj = nn.Linear(dim, dim)
        self.softmax = nn.Softmax(dim=-1)
    
    def forward(self, x, mask=None):
        """前向传播"""
        B_, N, C = x.shape
        qkv = self.qkv(x).reshape(B_, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]  # B_, nH, N, C/nH
        
        q = q * self.scale
        attn = (q @ k.transpose(-2, -1))  # B_, nH, N, N
        
        # 添加相对位置偏置
        relative_position_bias = self.relative_position_bias_table[self.relative_position_index.view(-1)].view(
            self.window_size[0] * self.window_size[1], self.window_size[0] * self.window_size[1], -1)  # Wh*Ww, Wh*Ww, nH
        relative_position_bias = relative_position_bias.permute(2, 0, 1).contiguous()  # nH, Wh*Ww, Wh*Ww
        attn = attn + relative_position_bias.unsqueeze(0)
        
        if mask is not None:
            nW = mask.shape[0]
            attn = attn.view(B_ // nW, nW, self.num_heads, N, N) + mask.unsqueeze(1).unsqueeze(0)
            attn = attn.view(-1, self.num_heads, N, N)
        
        attn = self.softmax(attn)
        x = (attn @ v).transpose(1, 2).reshape(B_, N, C)
        x = self.proj(x)
        
        return x


class UpsampleBlock(nn.Module):
    """上采样模块"""
    def __init__(self, in_channels: int, out_channels: int):
        super(UpsampleBlock, self).__init__()
        self.up = nn.ConvTranspose2d(in_channels, out_channels, kernel_size=2, stride=2)
        self.conv = nn.Sequential(
            nn.Conv2d(out_channels*2, out_channels, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x1, x2):
        x1 = self.up(x1)
        
        # 处理大小不匹配的情况
        diffY = x2.size()[2] - x1.size()[2]
        diffX = x2.size()[3] - x1.size()[3]
        x1 = F.pad(x1, [diffX // 2, diffX - diffX // 2,
                        diffY // 2, diffY - diffY // 2])
        
        x = torch.cat([x2, x1], dim=1)
        return self.conv(x)


class SwinUnet(nn.Module):
    """
    Swin-UNet语义分割模型
    基于Swin Transformer的U形架构，适合高精度遥感影像分割
    """
    def __init__(self, num_classes: int = 14, img_size: int = 512, in_channels: int = 3, 
                 pretrained: bool = True, depths: List[int] = [2, 2, 6, 2]):
        super(SwinUnet, self).__init__()
        
        self.num_classes = num_classes
        self.in_channels = in_channels
        
        # 注意：完整的Swin Transformer实现较为复杂
        # 这里使用了一个简化版本，结合ResNet50作为编码器和Transformer风格的解码器
        
        # 使用预训练的ResNet50作为编码器
        if pretrained:
            weights = ResNet50_Weights.DEFAULT
        else:
            weights = None
        
        encoder = torchvision.models.resnet50(weights=weights)
        
        # 处理输入通道数不为3的情况
        if in_channels != 3:
            self.first_conv = nn.Conv2d(in_channels, 64, kernel_size=7, stride=2, padding=3, bias=False)
        else:
            self.first_conv = encoder.conv1
        
        # 编码器
        self.encoder1 = nn.Sequential(
            self.first_conv,
            encoder.bn1,
            encoder.relu,
            encoder.maxpool
        )  # 64 channels, 1/4 resolution
        self.encoder2 = encoder.layer1  # 256 channels, 1/4 resolution
        self.encoder3 = encoder.layer2  # 512 channels, 1/8 resolution
        self.encoder4 = encoder.layer3  # 1024 channels, 1/16 resolution
        self.encoder5 = encoder.layer4  # 2048 channels, 1/32 resolution
        
        # 用于转换特征的Transformer块（简化版）
        self.transformer_block = nn.TransformerEncoderLayer(
            d_model=2048,
            nhead=8,
            dim_feedforward=2048*4,
            dropout=0.1,
            activation="gelu",
            batch_first=True
        )
        
        # 瓶颈层
        self.bottleneck = nn.Sequential(
            nn.Conv2d(2048, 1024, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(1024),
            nn.ReLU(inplace=True),
            nn.Conv2d(1024, 1024, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(1024),
            nn.ReLU(inplace=True)
        )
        
        # 解码器 (带有上采样模块)
        self.decoder1 = UpsampleBlock(1024, 512)
        self.decoder2 = UpsampleBlock(512, 256) 
        self.decoder3 = UpsampleBlock(256, 64)
        self.decoder4 = UpsampleBlock(64, 32)
        
        # 输出层
        self.final = nn.Conv2d(32, num_classes, kernel_size=1)
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化模型权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, std=0.001)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        # 保存原始输入尺寸
        input_size = x.size()[2:]
        
        # 编码路径
        x1 = self.encoder1(x)        # 1/4
        x2 = self.encoder2(x1)       # 1/4
        x3 = self.encoder3(x2)       # 1/8
        x4 = self.encoder4(x3)       # 1/16
        x5 = self.encoder5(x4)       # 1/32
        
        # 将特征转换为序列形式并应用Transformer
        batch, channels, h, w = x5.shape
        x5_seq = x5.flatten(2).transpose(1, 2)  # B, HW, C
        x5_seq = self.transformer_block(x5_seq)
        x5 = x5_seq.transpose(1, 2).reshape(batch, channels, h, w)
        
        # 瓶颈
        x = self.bottleneck(x5)
        
        # 解码路径 (带有跳跃连接)
        x = self.decoder1(x, x4)     # 1/16
        x = self.decoder2(x, x3)     # 1/8
        x = self.decoder3(x, x2)     # 1/4
        
        # 最后的上采样和处理
        # 上采样到原始大小的1/2
        x = F.interpolate(x, scale_factor=2, mode='bilinear', align_corners=True)
        
        # 进行最后一次解码
        x = self.decoder4(x, x1)
        
        # 最终分类
        x = self.final(x)
        
        # 确保输出尺寸与输入相同
        if x.size()[2:] != input_size:
            x = F.interpolate(x, size=input_size, mode='bilinear', align_corners=True)
        
        return x 