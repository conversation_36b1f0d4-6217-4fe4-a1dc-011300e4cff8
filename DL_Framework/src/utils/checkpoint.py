"""
检查点管理模块
提供保存、加载、恢复和清理检查点的功能。
"""

import glob
import logging
import os
from typing import Any, Optional, Union

import torch
import torch.nn as nn

# 避免循环导入
# from ..training.trainer import Trainer 

def save_checkpoint_from_trainer(
    trainer: 'Trainer',
    output_dir: str,
    epoch: int,
    is_best: bool,
    best_score: float,
    max_kept: int
):
    """
    一个从 Trainer 实例提取信息并保存检查点的高级接口。
    """
    # 获取原始模型（以防被DDP封装）
    model_to_save = trainer.model.module if hasattr(trainer.model, 'module') else trainer.model
    
    checkpoint_state = {
        'epoch': epoch,
        'state_dict': model_to_save.state_dict(),
        'optimizer': trainer.optimizer.state_dict(),
        'best_score': best_score,
    }
    
    # 如果有学习率调度器，也保存它的状态
    if hasattr(trainer, 'lr_scheduler') and trainer.lr_scheduler is not None:
        checkpoint_state['lr_scheduler'] = trainer.lr_scheduler.state_dict()
        
    # 定义文件名
    filename = os.path.join(output_dir, f'epoch_{epoch}.pth')
    if is_best:
        best_filename = os.path.join(output_dir, 'best_model.pth')
        torch.save(checkpoint_state, best_filename)
        logging.debug(f"已保存最佳模型到 {best_filename}")

    torch.save(checkpoint_state, filename)
    logging.debug(f"已保存检查点到 {filename}")

    # 清理旧的检查点
    if max_kept > 0:
        clean_old_checkpoints(output_dir, max_kept)

def clean_old_checkpoints(output_dir: str, max_kept: int):
    """
    在指定目录中，只保留最新的 `max_kept` 个检查点。
    """
    # 查找所有 epoch_*.pth 格式的检查点
    checkpoints = glob.glob(os.path.join(output_dir, 'epoch_*.pth'))
    
    # 按修改时间排序（或者按epoch号排序）
    # 按epoch号排序更可靠
    checkpoints.sort(key=lambda x: int(x.split('_')[-1].split('.')[0]))
    
    # 如果检查点数量超过了最大限制，则删除最旧的
    if len(checkpoints) > max_kept:
        for ckpt_to_delete in checkpoints[:-max_kept]:
            try:
                os.remove(ckpt_to_delete)
                logging.debug(f"已删除旧的检查点: {ckpt_to_delete}")
            except OSError as e:
                logging.error(f"删除检查点失败 {ckpt_to_delete}: {e}")

def load_checkpoint(
    model: nn.Module, 
    optimizer: Optional[torch.optim.Optimizer] = None, 
    lr_scheduler: Optional[Any] = None,
    filepath: str = ""
) -> tuple:
    """
    加载检查点。
    返回 (epoch, best_score)。
    """
    if not os.path.isfile(filepath):
        raise FileNotFoundError(f"检查点文件未找到: {filepath}")

    checkpoint = torch.load(filepath, map_location='cpu')
    
    # 加载模型状态
    # 处理DDP和普通模型的键名差异
    state_dict = checkpoint['state_dict']
    model_state_dict = model.module.state_dict() if hasattr(model, 'module') else model.state_dict()
    
    # 筛选出匹配的键
    new_state_dict = {k: v for k, v in state_dict.items() if k in model_state_dict}
    model_state_dict.update(new_state_dict)
    model.load_state_dict(model_state_dict, strict=False)

    # 加载优化器状态
    if optimizer and 'optimizer' in checkpoint:
        optimizer.load_state_dict(checkpoint['optimizer'])

    # 加载学习率调度器状态
    if lr_scheduler and 'lr_scheduler' in checkpoint:
        lr_scheduler.load_state_dict(checkpoint['lr_scheduler'])

    start_epoch = checkpoint.get('epoch', 0) + 1
    best_score = checkpoint.get('best_score', 0.0)
    
    logging.info(f"成功从 {filepath} 加载检查点。将从 epoch {start_epoch} 继续训练。")
    return start_epoch, best_score 