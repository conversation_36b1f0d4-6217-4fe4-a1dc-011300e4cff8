"""
实验管理模块
提供实验结果记录、配置管理和路径获取功能
"""

import os
import time
import json
import yaml
import shutil
import logging
from typing import Dict, Any, Optional, Union, List, Tuple
from pathlib import Path
import torch
from .config import Config


class ExperimentManager:
    """
    实验管理器类
    负责处理实验目录创建、配置保存、检查点管理和结果记录
    """
    
    def __init__(self, config: Config, version: Optional[str] = None):
        """
        初始化实验管理器，将根据配置自动创建目录结构。

        Args:
            config: 实验配置对象。
            version: 手动指定实验版本号，如果为None则自动生成。
        """
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # 严格从配置中获取信息。如果缺失，后续操作会自然失败。
        experiments_root = self.config.get("experiment_dir")
        model_name = self.config.get("model.name")
        dataset_name = self.config.get("dataset.name")

        # 使用获取到的值。如果为None，Path()会引发TypeError
        if version is None:
            version = time.strftime("%Y%m%d_%H%M%S")
        self.version = f"{version}_{model_name}_{dataset_name}"
        self.experiment_dir = Path(experiments_root) / self.version

        # 定义子目录
        self.checkpoint_dir = self.experiment_dir / "checkpoints"
        self.config_dir = self.experiment_dir / "config"
        self.log_dir = self.experiment_dir / "logs"
        
        # 创建目录并保存配置
        self._create_dirs()
        self._save_config()
    
    def _create_dirs(self) -> None:
        """创建所有实验目录"""
        self.experiment_dir.mkdir(parents=True, exist_ok=True)
        self.checkpoint_dir.mkdir(exist_ok=True)
        self.config_dir.mkdir(exist_ok=True)
        self.log_dir.mkdir(exist_ok=True)
        self.logger.info(f"实验目录已创建: {self.experiment_dir}")
    
    def _save_config(self) -> None:
        """保存配置快照到实验目录"""
        config_path = self.config_dir / "config.yaml"
        self.config.save(config_path)
        self.logger.info(f"实验配置已保存: {config_path}")
    
    def save_checkpoint(
        self,
        state: Dict[str, Any],
        epoch: int,
        is_best: bool = False,
        max_keep: int = 3
    ) -> None:
        """
        保存检查点，并根据策略清理旧文件。

        Args:
            state: 包含模型、优化器等状态的字典。
            epoch: 当前周期数。
            is_best: 是否为当前最佳模型。
            max_keep: 最多保留的检查点数量。
        """
        # 保存带有周期号的检查点
        filename = f"checkpoint_epoch_{epoch}.pth"
        checkpoint_path = self.checkpoint_dir / filename
        torch.save(state, checkpoint_path)
        self.logger.info(f"保存周期性检查点到: {checkpoint_path}")

        # 如果是最佳模型，则额外保存为best_model.pth和checkpoint.pth
        if is_best:
            best_path = self.checkpoint_dir / "best_model.pth"
            latest_path = self.checkpoint_dir / "checkpoint.pth"
            shutil.copyfile(checkpoint_path, best_path)
            shutil.copyfile(checkpoint_path, latest_path) # 覆盖最新的检查点
            self.logger.info(f"保存最佳模型副本到: {best_path}")

        if max_keep > 0:
            self._cleanup_checkpoints(max_keep)

    def _cleanup_checkpoints(self, max_keep: int) -> None:
        """清理旧检查点，只保留最新的 N 个。"""
        # 获取所有检查点文件（排除best_model.pth）
        checkpoint_files = [f for f in self.checkpoint_dir.glob("*.pth") if f.name != "best_model.pth"]
        
        # 按修改时间降序排序
        checkpoint_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        # 删除超过最大保留数量的文件
        for old_ckpt in checkpoint_files[max_keep:]:
            old_ckpt.unlink()
            self.logger.info(f"删除旧检查点: {old_ckpt}")

    def load_checkpoint(self, checkpoint_path: Optional[str] = None, load_best: bool = False, map_location: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        加载检查点。

        Args:
            checkpoint_path: 检查点路径。如果为None，则自动加载最新的或最佳的。
            load_best: 是否强制加载最佳模型。
            map_location: 加载张量的设备映射。

        Returns:
            加载的检查点内容字典，如果找不到文件则返回None。
        """
        if checkpoint_path is None:
            if load_best:
                path_to_load = self.checkpoint_dir / "best_model.pth"
            else:
                # 寻找最新的检查点
                checkpoint_files = [f for f in self.checkpoint_dir.glob("*.pth") if f.name != "best_model.pth"]
                if not checkpoint_files:
                    self.logger.warning("检查点目录中没有找到检查点文件。")
                    return None
                checkpoint_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
                path_to_load = checkpoint_files[0]
        else:
            path_to_load = Path(checkpoint_path)

        if not path_to_load.exists():
            self.logger.warning(f"检查点文件不存在: {path_to_load}")
            return None

        self.logger.info(f"加载检查点: {path_to_load}")
        return torch.load(path_to_load, map_location=map_location)

    def get_log_file_path(self) -> Path:
        """获取主日志文件的路径。"""
        return self.log_dir / "train.log"
    
    def get_tensorboard_dir(self) -> Path:
        """获取TensorBoard日志目录的路径。"""
        tensorboard_dir = self.experiment_dir / "tensorboard"
        tensorboard_dir.mkdir(exist_ok=True)
        return tensorboard_dir
    
    def get_version(self) -> str:
        """
        获取实验版本号
        
        Returns:
            实验版本号
        """
        return self.version
    
    def get_experiment_dir(self) -> Path:
        """
        获取实验目录
        
        Returns:
            实验目录路径
        """
        return self.experiment_dir 