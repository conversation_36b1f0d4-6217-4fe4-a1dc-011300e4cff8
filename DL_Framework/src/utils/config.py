"""
配置加载和处理模块
支持从YAML文件加载配置，合并多个配置文件，并解析变量引用
"""

import copy
import logging
import re
from pathlib import Path
from typing import Any, Dict, Optional, Union

import yaml

# 初始化日志记录器
logger = logging.getLogger(__name__)

# 定义一个唯一的哨兵对象，用于区分"未提供默认值"和"默认值为None"
_sentinel = object()

class Config:
    """配置管理类，支持组织化的配置文件结构"""

    def __init__(self, config_path: Union[str, Path]):
        """
        初始化配置对象。
        加载顺序:
        1. 加载主配置文件。
        2. 根据主配置中的 'name' 字段加载并合并子配置 (如 model, dataset)。
        3. 解析所有变量引用 (e.g., ${dataset.num_classes})。
        4. 如果配置中存在 'name' 字段，则会尝试加载对应的子配置并进行深度合并。
        例如: model: {name: deeplab} -> 会加载 configs/model/deeplab.yaml
        """
        self.config_path = Path(config_path)
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 1. 加载主配置文件
        self.config = self._load_yaml(self.config_path)
        
        # 2. 加载子配置
        self._load_subconfigs()
        
        # 3. 迭代解析变量引用
        self._resolve_references()

        # 4. 自动解析所有相对路径为绝对路径
        self._resolve_paths()

    def _load_yaml(self, path: Path) -> Dict[str, Any]:
        """加载YAML文件并返回字典"""
        if not path.is_file():
            raise FileNotFoundError(f"配置文件未找到: {path}")
        with open(path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f) or {}

    def _deep_update(self, base_dict: Dict[str, Any], update_dict: Dict[str, Any]) -> Dict[str, Any]:
        """
        深度更新字典。
        与 dict.update() 不同，此方法会递归合并嵌套的字典。
        """
        result = base_dict.copy()
        for key, value in update_dict.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_update(result[key], value)
            else:
                result[key] = value
        return result

    def _resolve_references(self):
        """
        迭代解析配置中的所有变量引用，直到稳定。
        如果最终仍有未解析的引用或存在循环引用，则抛出异常。
        """
        pattern = re.compile(r'\${([a-zA-Z0-9_.-]+)}')
        max_passes = 10

        for i in range(max_passes):
            changed_in_pass = [False]

            def _lookup(path_str: str) -> Any:
                keys = path_str.split('.')
                value = self.config
                for key in keys:
                    if not isinstance(value, dict) or key not in value:
                        return None
                    value = value[key]
                return value

            def _recursive_resolve(data: Any) -> Any:
                if isinstance(data, dict):
                    return {k: _recursive_resolve(v) for k, v in data.items()}
                if isinstance(data, list):
                    return [_recursive_resolve(item) for item in data]
                if not isinstance(data, str):
                    return data

                # 完全匹配: e.g., "${model.name}"
                match = pattern.fullmatch(data)
                if match:
                    ref_path = match.group(1)
                    resolved_value = _lookup(ref_path)
                    if resolved_value is not None and not (isinstance(resolved_value, str) and pattern.search(resolved_value)):
                        changed_in_pass[0] = True
                        return resolved_value
                    return data

                # 部分匹配: e.g., "path/to/${data.root}/images"
                def _sub_handler(m: re.Match) -> str:
                    ref_path = m.group(1)
                    resolved_value = _lookup(ref_path)
                    if resolved_value is not None and not isinstance(resolved_value, (dict, list)):
                        changed_in_pass[0] = True
                        return str(resolved_value)
                    return m.group(0)
                
                return pattern.sub(_sub_handler, data)

            self.config = _recursive_resolve(self.config)
            
            if not changed_in_pass[0]:
                self.logger.debug(f"变量引用在 {i + 1} 轮后稳定。")
                final_unresolved = []
                def _final_check(data: Any):
                    if isinstance(data, dict):
                        for v in data.values(): _final_check(v)
                    elif isinstance(data, list):
                        for item in data: _final_check(item)
                    elif isinstance(data, str) and pattern.search(data):
                        final_unresolved.append(data)
                
                _final_check(self.config)
                if final_unresolved:
                    raise RuntimeError(f"配置解析完成，但仍有未解析的变量引用: {final_unresolved[:5]}...")
                return

        raise RuntimeError(f"在 {max_passes} 轮解析后变量引用仍未稳定，可能存在循环引用。")

    def get(self, key_path: str):
        """获取配置值，如果键不存在则抛出错误"""
        parts = key_path.split('.')
        current = self.config
        for i, part in enumerate(parts):
            if not isinstance(current, dict) or part not in current:
                traversed = '.'.join(parts[:i + 1])
                raise KeyError(f"配置项 '{key_path}' 未找到。在路径 '{traversed}' 处中断。")
            current = current[part]
        return current

    def set(self, key_path: str, value: Any):
        """设置配置值"""
        keys = key_path.split('.')
        d = self.config
        for key in keys[:-1]:
            d = d.setdefault(key, {})
        d[keys[-1]] = value

    def save(self, output_path: Union[str, Path]):
        """保存当前配置到YAML文件"""
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            yaml.dump(self.config, f, default_flow_style=False, sort_keys=False)

    def _load_subconfigs(self):
        """
        动态加载子配置文件。
        它会遍历所有顶层配置项，如果某项是一个包含'name'键的字典，
        则会尝试加载对应的子配置并进行深度合并。
        例如: model: {name: deeplab} -> 会加载 configs/model/deeplab.yaml
        """
        # 自动向上查找 'configs' 目录作为根目录
        p = self.config_path.resolve()
        configs_root = None
        while p != p.parent:
            if p.name == 'configs':
                configs_root = p
                break
            p = p.parent
        if not configs_root:
            self.logger.warning(f"无法从 '{self.config_path}' 向上找到 'configs' 目录，将跳过子配置加载。")
            return

        # 动态遍历所有顶层键，而不是使用硬编码列表
        for group, content in self.config.copy().items():
            if isinstance(content, dict) and "name" in content:
                name = content["name"]
                if not name:  # 如果name字段为空，则跳过
                    continue
                config_path = configs_root / group / f"{name}.yaml"
                if config_path.exists():
                    self.logger.debug(f"正在加载子配置: {config_path}")
                    sub_config = self._load_yaml(config_path)
                    # 深度合并：主配置中的同名键会覆盖子配置的值
                    self.config[group] = self._deep_update(sub_config, self.config[group])
                else:
                    raise FileNotFoundError(f"在主配置中引用的子配置文件未找到: {config_path}")

    def _resolve_paths(self):
        """
        递归遍历整个配置字典，将所有存在的、值为相对路径的项转换为绝对路径。
        路径的基准是主配置文件所在目录的父目录（通常是项目根目录或configs目录的父目录）。
        """
        # 以主配置文件为基准，找到项目根目录
        # 假设配置文件在 '.../project_root/configs/...'
        # .parent -> '.../configs'
        # .parent -> '.../project_root'
        base_path = self.config_path.parent.parent

        def _recursive_resolve(cfg_obj):
            items = []
            if isinstance(cfg_obj, dict):
                items = list(cfg_obj.items())
            elif isinstance(cfg_obj, list):
                items = list(enumerate(cfg_obj))

            for key, value in items:
                if isinstance(value, str):
                    # 检查这个字符串是否可能是一个相对路径
                    if not Path(value).is_absolute() and ('/' in value or '.' in value):
                        potential_path = base_path / value
                        if potential_path.exists():
                            resolved_path = str(potential_path.resolve())
                            self.logger.debug(f"Resolving path: '{key}' from '{value}' -> '{resolved_path}'")
                            # 直接修改字典/列表中的值
                            cfg_obj[key] = resolved_path
                
                elif isinstance(value, dict) or isinstance(value, list):
                    _recursive_resolve(value)

        _recursive_resolve(self.config)

    def __str__(self) -> str:
        """返回配置内容的可读字符串表示（YAML格式）"""
        try:
            return yaml.dump(self.config, default_flow_style=False, sort_keys=False)
        except Exception as e:
            self.logger.error(f"无法将配置序列化为YAML: {e}")
            return str(self.config)
