"""
数据集统计模块
提供数据集类别分布统计和类别权重计算功能
"""

import logging
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import torch
from tqdm import tqdm

# def compute_class_statistics(data_loader, num_classes, ignore_index=255, device='cpu', verbose=True):
#     """计算数据集中每个类别的像素数量
#
#     Args:
#         data_loader: 数据加载器或数据批次列表
#         num_classes: 类别数量
#         ignore_index: 忽略的标签值
#         device: 计算设备
#         verbose: 是否输出详细日志
#
#     Returns:
#         torch.Tensor: 每个类别的像素数量
#     """
#     logger = logging.getLogger(__name__)
#     if verbose:
#         logger.info("开始计算数据集类别统计...")
#
#     class_counts = torch.zeros(num_classes, dtype=torch.int64)
#     device = torch.device(device)
#     total_masks = 0
#
#     # 处理不同类型的输入
#     if not isinstance(data_loader, list):
#         loader = data_loader
#     else:
#         # 如果输入是批次列表
#         loader = data_loader
#
#     # 使用tqdm显示进度
#     with torch.no_grad():
#         progress_bar = tqdm(loader, desc="统计数据集类别分布") if verbose else loader
#         for batch in progress_bar:
#             if isinstance(batch, (list, tuple)):
#                 masks = batch[1]
#             elif isinstance(batch, dict):
#                 masks = batch['mask']
#             else:
#                 raise ValueError(f"不支持的批次格式: {type(batch)}")
#
#             # 记录处理的掩码数量
#             total_masks += masks.size(0)
#
#             # 忽略指定索引
#             valid_mask = masks != ignore_index
#
#             # 计算每个类别的像素数
#             for cls in range(num_classes):
#                 class_counts[cls] += ((masks == cls) & valid_mask).sum().item()
#
#     # 计算总像素数和百分比
#     total_valid = class_counts.sum().item()
#
#     if verbose:
#         percentages = [count / total_valid * 100 for count in class_counts]
#
#         # 记录详细统计
#         logger.info(f"数据集统计完成 - 处理 {total_masks} 个掩码，有效像素: {total_valid:,}")
#         logger.info("类别分布:")
#         for cls in range(num_classes):
#             logger.info(f"  类别 {cls}: {class_counts[cls]:,} 像素 ({percentages[cls]:.2f}%)")
#
#     return class_counts

def calculate_class_weights(class_counts, method='inverse', beta=0.99, epsilon=1e-6, max_ratio=10.0):
    """计算类别权重
    
    Args:
        class_counts: 每个类别的像素数量
        method: 权重计算方法，可选:
            - 'inverse': 频率倒数
            - 'effective': 有效样本数 
            - 'log': 对数权重
            - 'sqrt': 平方根权重
        beta: 有效样本数方法的平衡因子
        epsilon: 防止除零错误的小数值
        max_ratio: 最大权重与最小权重的比率限制，防止极端权重
        
    Returns:
        torch.Tensor: 类别权重
    """
    if torch.sum(class_counts) == 0:
        return torch.ones(len(class_counts))
    
    # 计算类别频率
    frequencies = class_counts.float() / torch.sum(class_counts).float()
    
    # 防止零频率
    frequencies = frequencies + epsilon
    
    if method == 'inverse':
        # 使用频率倒数作为权重
        weights = 1.0 / frequencies
    elif method == 'effective':
        # 使用有效样本数平衡公式: (1-beta)/(1-beta^freq)
        weights = (1 - beta) / (1 - beta ** frequencies)
    elif method == 'log':
        # 使用对数权重: 1 / log(c + freq)，c是一个常数
        weights = 1.0 / torch.log(1.1 + frequencies)
    elif method == 'sqrt':
        # 使用平方根权重: 1 / sqrt(freq)
        weights = 1.0 / torch.sqrt(frequencies)
    else:
        raise ValueError(f"不支持的权重计算方法: {method}")
    
    # 过滤无效类别
    valid_mask = ~torch.isnan(weights) & ~torch.isinf(weights) & (weights > 0)
    if not valid_mask.all():
        weights[~valid_mask] = 1.0
    
    # 权重裁剪 - 防止极端权重导致训练不稳定
    if max_ratio > 0:
        min_weight = weights.min()
        max_allowed_weight = min_weight * max_ratio
        weights = torch.clamp(weights, min=min_weight, max=max_allowed_weight)
        
        # 记录权重裁剪信息
        logger = logging.getLogger(__name__)
        logger.info(f"权重裁剪: 最大比率限制为 {max_ratio}x, 最小权重={min_weight:.6f}, 最大允许权重={max_allowed_weight:.6f}")
    
    # 归一化权重，使得权重和等于有效类别数
    valid_classes = valid_mask.sum().item()
    weights = weights / weights.sum() * valid_classes
    
    return weights

def get_weight_stats(weights):
    """获取权重的统计信息字符串
    
    Args:
        weights: 类别权重张量
        
    Returns:
        str: 权重统计信息
    """
    if weights is None:
        return "无权重"
    
    min_w = weights.min().item()
    max_w = weights.max().item()
    mean_w = weights.mean().item()
    std_w = weights.std().item()
    
    return f"最小={min_w:.4f}, 最大={max_w:.4f}, 平均={mean_w:.4f}, 标准差={std_w:.4f}" 