"""
优化器工厂模块
提供创建各种优化器和学习率调度器的功能
支持常用的Adam、AdamW、SGD等优化器
"""

import logging
# from torch.optim.lr_scheduler import (
#     StepLR, CosineAnnealingLR, PolynomialLR, 
#     OneCycleLR, ReduceLROnPlateau, LambdaLR, MultiStepLR,
#     _LRScheduler
# )
from typing import TYPE_CHECKING, Any, Callable, Dict, List, Optional, Union

import torch
import torch.optim as optim
from copy import deepcopy

# 将 torch_optimizer 作为本模块的强制依赖在顶部导入
# 如果用户使用了需要它的优化器（如LARS, Lookahead, RAdam），则必须安装此库
from torch_optimizer import LARS, Lookahead, RAdam

from ..utils.config import Config

# import math

# 类型定义，解决返回类型不兼容问题
# 明确指定scheduler类型，消除类型不兼容警告
# LRSchedulerType = Union[StepLR, CosineAnnealingLR, PolynomialLR, 
#                     OneCycleLR, ReduceLROnPlateau, LambdaLR, MultiStepLR, None]

class OptimizerFactory:
    """
    优化器工厂类，用于根据配置创建优化器
    """
    def __init__(self, model_params, config: Config):
        """
        初始化工厂
        
        Args:
            model_params: 模型参数
            config: 配置对象
        """
        self.config = config
        self.logger = logging.getLogger(__name__)

        # 如果输入是模型，提取参数
        if isinstance(model_params, torch.nn.Module):
            self.params = model_params.parameters()
        else:
            self.params = model_params
        
        try:
            self.lr = float(config.get('optimizer.learning_rate'))
            self.weight_decay = float(config.get('optimizer.weight_decay'))
        except (ValueError, TypeError) as e:
            self.logger.error(f"学习率或权重衰减参数类型错误: {e}")
            raise ValueError(f"配置中的学习率或权重衰减参数无效: {e}")

    def create(self) -> optim.Optimizer:
        """
        创建优化器实例
        """
        optimizer_name = self.config.get('optimizer.name').lower()
        creator_method_name = f"_create_{optimizer_name}_optimizer"
        creator_method = getattr(self, creator_method_name, None)

        if not creator_method:
            self.logger.error(f"不支持的优化器类型: {optimizer_name}")
            raise ValueError(f"不支持的优化器类型: {optimizer_name}")

        try:
            return creator_method()
        except Exception as e:
            self.logger.error(f"创建优化器 '{optimizer_name}' 失败: {str(e)}")
            raise

    def _create_adam_optimizer(self) -> optim.Optimizer:
        try:
            beta1 = float(self.config.get('optimizer.beta1'))
            beta2 = float(self.config.get('optimizer.beta2'))
            eps = float(self.config.get('optimizer.eps'))
        except (ValueError, TypeError) as e:
            self.logger.error(f"Adam参数类型错误: {e}")
            raise ValueError(f"配置中的Adam参数无效: {e}")
        
        optimizer = optim.Adam(
            self.params, lr=self.lr, betas=(beta1, beta2), 
            eps=eps, weight_decay=self.weight_decay
        )
        self.logger.info(f"创建优化器: adam, 学习率: {self.lr}, 权重衰减: {self.weight_decay}")
        self.logger.info(f"Adam参数详情: beta1={beta1}, beta2={beta2}, eps={eps}")
        return optimizer

    def _create_adamw_optimizer(self) -> optim.Optimizer:
        try:
            beta1 = float(self.config.get('optimizer.beta1'))
            beta2 = float(self.config.get('optimizer.beta2'))
            eps = float(self.config.get('optimizer.eps'))
        except (ValueError, TypeError) as e:
            self.logger.error(f"AdamW参数类型错误: {e}")
            raise ValueError(f"配置中的AdamW参数无效: {e}")
        
        optimizer = optim.AdamW(
            self.params, lr=self.lr, betas=(beta1, beta2), 
            eps=eps, weight_decay=self.weight_decay
        )
        self.logger.info(f"创建优化器: adamw, 学习率: {self.lr}, 权重衰减: {self.weight_decay}")
        self.logger.info(f"AdamW参数详情: beta1={beta1}, beta2={beta2}, eps={eps}")
        return optimizer

    def _create_sgd_optimizer(self) -> optim.Optimizer:
        try:
            momentum = float(self.config.get('optimizer.momentum'))
            nesterov = bool(self.config.get('optimizer.nesterov'))
        except (ValueError, TypeError) as e:
            self.logger.error(f"SGD参数类型错误: {e}")
            raise ValueError(f"配置中的SGD参数无效: {e}")
        
        optimizer = optim.SGD(
            self.params, lr=self.lr, momentum=momentum, 
            weight_decay=self.weight_decay, nesterov=nesterov
        )
        self.logger.info(f"创建优化器: sgd, 学习率: {self.lr}, 权重衰减: {self.weight_decay}")
        self.logger.info(f"SGD参数详情: momentum={momentum}, nesterov={nesterov}")
        return optimizer

    def _create_rmsprop_optimizer(self) -> optim.Optimizer:
        try:
            alpha = float(self.config.get('optimizer.alpha'))
            eps = float(self.config.get('optimizer.eps'))
            momentum = float(self.config.get('optimizer.momentum'))
        except (ValueError, TypeError) as e:
            self.logger.error(f"RMSprop参数类型错误: {e}")
            raise ValueError(f"配置中的RMSprop参数无效: {e}")
        
        optimizer = optim.RMSprop(
            self.params, lr=self.lr, alpha=alpha, eps=eps, 
            weight_decay=self.weight_decay, momentum=momentum
        )
        self.logger.info(f"创建优化器: rmsprop, 学习率: {self.lr}, 权重衰减: {self.weight_decay}")
        self.logger.info(f"RMSprop参数详情: alpha={alpha}, eps={eps}, momentum={momentum}")
        return optimizer

    def _create_lars_optimizer(self) -> optim.Optimizer:
        momentum = float(self.config.get('optimizer.momentum'))
        optimizer = LARS(
            self.params, lr=self.lr, momentum=momentum, 
            weight_decay=self.weight_decay
        )
        self.logger.info(f"创建优化器: lars, 学习率: {self.lr}, 权重衰减: {self.weight_decay}")
        self.logger.info(f"LARS参数详情: momentum={momentum}")
        self.logger.info("使用torch_optimizer库的LARS优化器")
        return optimizer

    def _create_lookahead_optimizer(self) -> optim.Optimizer:
            base_optimizer_name = self.config.get('optimizer.base_optimizer')
            base_config = deepcopy(self.config)
            base_config.set('optimizer.name', base_optimizer_name)

            base_optimizer = create_optimizer(self.params, base_config)
            
            k = self.config.get('optimizer.k')
            alpha = self.config.get('optimizer.alpha')
            
            optimizer = Lookahead(base_optimizer, k=k, alpha=alpha)
            self.logger.info(f"创建优化器: lookahead, 基础优化器: {base_optimizer_name}")
            self.logger.info(f"Lookahead参数详情: k={k}, alpha={alpha}")
            return optimizer

    def _create_radam_optimizer(self) -> optim.Optimizer:
            beta1 = float(self.config.get('optimizer.beta1'))
            beta2 = float(self.config.get('optimizer.beta2'))
            eps = float(self.config.get('optimizer.eps'))
            
            optimizer = RAdam(
                self.params, lr=self.lr, betas=(beta1, beta2), 
                eps=eps, weight_decay=self.weight_decay
            )
            self.logger.info(f"创建优化器: radam, 学习率: {self.lr}, 权重衰减: {self.weight_decay}")
            self.logger.info(f"RAdam参数详情: beta1={beta1}, beta2={beta2}, eps={eps}")
            return optimizer


def create_optimizer(
    model_params,
    config: Config
) -> optim.Optimizer:
    """
    根据配置创建优化器
    
    Args:
        model_params: 模型参数，可以是参数列表或单个模型
        config: 配置对象
        
    Returns:
        创建的优化器实例
        
    Raises:
        ValueError: 如果指定的优化器类型不支持
    """
    factory = OptimizerFactory(model_params, config)
    return factory.create()
