"""
学习率调度器工厂模块
提供创建各种学习率调度器的功能
支持step、cosine、poly等学习率调度策略
"""

import logging
import math
from typing import TYPE_CHECKING, Any, Callable, Dict, List, Optional, Union

import torch
import torch.optim as optim
from torch.optim.lr_scheduler import (CosineAnnealingLR, LambdaLR, MultiStepLR,
                                      OneCycleLR, PolynomialLR,
                                      ReduceLROnPlateau, StepLR, _LRScheduler)

from .config import Config

# 类型定义
LRSchedulerType = Union[StepLR, CosineAnnealingLR, PolynomialLR, 
                    OneCycleLR, ReduceLROnPlateau, LambdaLR, MultiStepLR, None]

class SchedulerFactory:
    """
    学习率调度器工厂类，用于根据配置创建调度器
    """
    def __init__(self, optimizer: optim.Optimizer, config: Config, steps_per_epoch: Optional[int] = None):
        """
        初始化工厂
        
        Args:
            optimizer: 优化器实例
            config: 配置对象
            steps_per_epoch: 每个epoch的步数
        """
        self.optimizer = optimizer
        self.config = config
        self.steps_per_epoch = steps_per_epoch
        self.logger = logging.getLogger(__name__)
        
        try:
            self.epochs = int(config.get('train.epochs'))
        except (ValueError, TypeError) as e:
            self.logger.error(f"epochs参数类型错误: {e}")
            raise ValueError(f"配置中的epochs参数无效: {e}")

    def create(self) -> LRSchedulerType:
        """
        创建调度器实例
        """
        scheduler_name_config = self.config.get('scheduler.name')
        if not scheduler_name_config:
            self.logger.info("未在配置中找到'scheduler.name'，将使用固定学习率")
            return None
        scheduler_name = scheduler_name_config.lower()

        creator_method_name = f"_create_{scheduler_name}_scheduler"
        creator_method = getattr(self, creator_method_name, None)

        if not creator_method:
            self.logger.error(f"不支持的学习率调度器类型: {scheduler_name}")
            raise ValueError(f"不支持的学习率调度器类型: {scheduler_name}")
        
        try:
            return creator_method()
        except Exception as e:
            self.logger.error(f"创建学习率调度器 '{scheduler_name}' 失败: {str(e)}")
            raise

    def _create_step_scheduler(self) -> StepLR:
        try:
            step_size = int(self.config.get('scheduler.step_size'))
            gamma = float(self.config.get('scheduler.gamma'))
        except (ValueError, TypeError) as e:
            self.logger.error(f"step_size或gamma参数类型错误: {e}")
            raise ValueError(f"配置中的step_size或gamma参数无效: {e}")
        
        scheduler = StepLR(self.optimizer, step_size=step_size, gamma=gamma)
        self.logger.info("创建学习率调度器: step")
        self.logger.info(f"StepLR参数详情: step_size={step_size}, gamma={gamma}")
        return scheduler

    def _create_cosine_scheduler(self) -> CosineAnnealingLR:
        try:
            T_max = int(self.config.get('scheduler.t_max'))
            eta_min = float(self.config.get('scheduler.eta_min'))
        except (ValueError, TypeError) as e:
            self.logger.error(f"t_max或eta_min参数类型错误: {e}")
            raise ValueError(f"配置中的t_max或eta_min参数无效: {e}")
        
        scheduler = CosineAnnealingLR(self.optimizer, T_max=T_max, eta_min=eta_min)
        self.logger.info("创建学习率调度器: cosine")
        self.logger.info(f"CosineAnnealingLR参数详情: T_max={T_max}, eta_min={eta_min}")
        return scheduler

    def _create_poly_scheduler(self) -> PolynomialLR:
        try:
            power = float(self.config.get('scheduler.power'))
            max_decay_steps = int(self.config.get('scheduler.max_decay_steps'))
        except (ValueError, TypeError) as e:
            self.logger.error(f"power或max_decay_steps参数类型错误: {e}")
            raise ValueError(f"配置中的power或max_decay_steps参数无效: {e}")
        
        scheduler = PolynomialLR(self.optimizer, total_iters=max_decay_steps, power=power)
        self.logger.info("创建学习率调度器: poly")
        self.logger.info(f"PolynomialLR参数详情: power={power}, max_decay_steps={max_decay_steps}")
        return scheduler

    def _create_onecycle_scheduler(self) -> OneCycleLR:
        if self.steps_per_epoch is None:
            self.logger.error("使用OneCycleLR需要提供steps_per_epoch参数")
            raise ValueError("使用OneCycleLR需要提供steps_per_epoch参数")
        
        try:
            max_lr = float(self.config.get('scheduler.max_lr'))
            pct_start = float(self.config.get('scheduler.pct_start'))
            div_factor = float(self.config.get('scheduler.div_factor'))
            final_div_factor = float(self.config.get('scheduler.final_div_factor'))
        except (ValueError, TypeError) as e:
            self.logger.error(f"onecycle参数类型错误: {e}")
            raise ValueError(f"配置中的onecycle参数无效: {e}")
        
        scheduler = OneCycleLR(
            self.optimizer,
            max_lr=max_lr,
            steps_per_epoch=self.steps_per_epoch,
            epochs=self.epochs,
            pct_start=pct_start,
            div_factor=div_factor,
            final_div_factor=final_div_factor
        )
        
        self.logger.info("创建学习率调度器: onecycle")
        self.logger.info(f"OneCycleLR参数详情: max_lr={max_lr}, steps_per_epoch={self.steps_per_epoch}, epochs={self.epochs}")
        self.logger.info(f"OneCycleLR参数详情: pct_start={pct_start}, div_factor={div_factor}, final_div_factor={final_div_factor}")
        return scheduler

    def _create_plateau_scheduler(self) -> ReduceLROnPlateau:
        scheduler = ReduceLROnPlateau(
            self.optimizer,
            mode=self.config.get('scheduler.mode', 'min'),
            factor=float(self.config.get('scheduler.factor', 0.1)),
            patience=int(self.config.get('scheduler.patience', 10)),
            threshold=float(self.config.get('scheduler.threshold', 1e-4)),
            threshold_mode=self.config.get('scheduler.threshold_mode', 'rel'),
            cooldown=int(self.config.get('scheduler.cooldown', 0)),
            min_lr=float(self.config.get('scheduler.min_lr', 0)),
            eps=float(self.config.get('scheduler.eps', 1e-8)),
            verbose=self.config.get('scheduler.verbose', False)
        )
        self.logger.info("创建学习率调度器: plateau")
        # Reducing log spam as ReduceLROnPlateau has its own verbose option
        return scheduler

    def _create_warmup_cosine_scheduler(self) -> LambdaLR:
        warmup_epochs = int(self.config.get('scheduler.warmup_epochs', 0))
        
        def lr_lambda(epoch):
            if epoch < warmup_epochs:
                return float(epoch) / float(max(1, warmup_epochs))
            else:
                progress = float(epoch - warmup_epochs) / float(max(1, self.epochs - warmup_epochs))
                return 0.5 * (1.0 + math.cos(math.pi * progress))
        
        scheduler = LambdaLR(self.optimizer, lr_lambda)
        self.logger.info("创建学习率调度器: warmup_cosine")
        self.logger.info(f"WarmupCosine参数详情: warmup_epochs={warmup_epochs}, total_epochs={self.epochs}")
        return scheduler

    def _create_multistep_scheduler(self) -> MultiStepLR:
        milestones = self.config.get('scheduler.milestones')
        if not isinstance(milestones, list):
            raise ValueError("multistep调度器的milestones参数必须是一个列表")
        gamma = float(self.config.get('scheduler.gamma', 0.1))
        
        scheduler = MultiStepLR(self.optimizer, milestones=milestones, gamma=gamma)
        self.logger.info("创建学习率调度器: multistep")
        self.logger.info(f"MultiStepLR参数详情: milestones={milestones}, gamma={gamma}")
        return scheduler

def create_lr_scheduler(
    optimizer: optim.Optimizer,
    config: Config,
    steps_per_epoch: Optional[int] = None
) -> LRSchedulerType:
    """
    根据配置创建学习率调度器
    
    Args:
        optimizer: 优化器实例
        config: 配置对象
        steps_per_epoch: 每个epoch的步数，对某些调度器必须提供
        
    Returns:
        创建的学习率调度器实例，如果配置不正确则返回None
    """
    factory = SchedulerFactory(optimizer, config, steps_per_epoch)
    return factory.create() 