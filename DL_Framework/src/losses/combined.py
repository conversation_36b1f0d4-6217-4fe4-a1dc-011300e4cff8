"""
组合损失函数
支持多个损失函数组合使用，如CE+Dice
"""

import logging
from typing import Any, Dict, List, Optional, Union

import torch
import torch.nn as nn

from .base import BaseLoss


class CombinedLoss(BaseLoss):
    """组合多个损失函数"""
    
    def __init__(self, losses=None, weights=None, use_dynamic_weights=True):
        """初始化组合损失
        
        Args:
            losses: 损失函数列表或字典 {名称: 损失函数}
            weights: 组合权重列表或字典 {名称: 权重值}，控制各损失函数在总损失中的比例
            use_dynamic_weights: 是否启用子损失函数的动态类别权重更新
        """
        super().__init__()
        self.supports_class_weights = use_dynamic_weights
        
        if losses is None:
            losses = {}
        
        if weights is None:
            # 默认组合权重均为1
            if isinstance(losses, dict):
                weights = {name: 1.0 for name in losses}
            else:
                weights = [1.0] * len(losses)
        
        # 标准化为字典格式
        if not isinstance(losses, dict):
            self.losses = dict((f"loss_{i}", loss) for i, loss in enumerate(losses))
            self.weights = dict((f"loss_{i}", float(weight)) for i, weight in enumerate(weights))
        else:
            self.losses = dict(losses)
            if isinstance(weights, dict):
                self.weights = dict((k, float(v)) for k, v in weights.items())
            else:
                # 如果losses是字典但weights是列表，创建对应的字典
                keys = list(losses.keys())
                self.weights = dict((keys[i], float(weight)) for i, weight in enumerate(weights) if i < len(keys))
            
        # 注册子模块
        for name, loss in self.losses.items():
            self.add_module(name, loss)
        
        # 记录支持类别权重的损失函数
        self.weight_supported_losses = {}
        for name, loss in self.losses.items():
            if hasattr(loss, 'supports_class_weights') and loss.supports_class_weights:
                self.weight_supported_losses[name] = loss
        
        # 检查配置是否合理
        self._validate_config()
        
        # 记录初始化信息
        self.logger.info(f"组合损失: 包含 {len(self.losses)} 个损失函数")
        for name, loss in self.losses.items():
            self.logger.info(f"  - {name}: 组合权重={self.weights[name]}, "
                           f"类型={type(loss).__name__}, "
                           f"支持类别权重: {hasattr(loss, 'supports_class_weights') and loss.supports_class_weights}")
        
        if self.weight_supported_losses:
            self.logger.info(f"组合损失中有 {len(self.weight_supported_losses)} 个损失函数支持动态类别权重更新")
        
        if use_dynamic_weights and not self.weight_supported_losses:
            self.logger.warning("组合损失启用了动态类别权重，但没有子损失函数支持权重更新")
    
    def _validate_config(self):
        """验证配置是否合理"""
        if not self.losses:
            raise ValueError("组合损失必须至少包含一个损失函数")
        
        for name in self.losses:
            if name not in self.weights:
                self.logger.warning(f"损失函数 {name} 没有对应的组合权重，使用默认值1.0")
                self.weights[name] = 1.0
    
    def forward(self, inputs, targets):
        """计算组合损失
        
        各子损失函数的输出按照组合权重加权求和
        """
        losses = {}
        total_loss = 0.0
        
        for name, loss_fn in self.losses.items():
            weight = self.weights[name]  # 组合权重
            loss_value = loss_fn(inputs, targets)
            losses[name] = loss_value.item() if isinstance(loss_value, torch.Tensor) else loss_value
            total_loss = total_loss + weight * loss_value
        
        # 保存分项损失
        self.last_losses = losses
        self.last_loss = total_loss
        
        return total_loss
    
    def update_weights(self, class_counts, epoch=None, alpha=0.8):
        """更新支持类别权重的子损失函数的类别权重
        
        注意：这里更新的是各子损失函数内部的类别权重，而非组合权重
        
        Args:
            class_counts: 类别统计数据
            epoch: 当前训练轮次
            alpha: 指数移动平均系数
            
        Returns:
            bool: 是否至少有一个子损失函数更新了权重
        """
        if not self.supports_class_weights or not self.weight_supported_losses:
            return False
        
        updated = False
        
        # 获取当前设备
        device = next(self.parameters()).device if list(self.parameters()) else torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 记录设备信息
        self.logger.info(f"CombinedLoss - 当前设备: {device}, 类别统计数据设备: {class_counts.device}")
        
        # 确保类别计数在正确设备上
        if device.type != 'cpu' and class_counts.device.type == 'cpu':
            class_counts = class_counts.to(device)
            self.logger.info(f"CombinedLoss - 将类别统计数据移动到设备 {device} 上")
        
        # 记录每个子损失函数的设备信息
        for name, loss in self.weight_supported_losses.items():
            loss_device = next(loss.parameters()).device if list(loss.parameters()) else device
            self.logger.info(f"CombinedLoss - 子损失函数 {name} 设备: {loss_device}")
            
            if loss.update_weights(class_counts, epoch, alpha):
                updated = True
                self.logger.info(f"更新了 {name} 的类别权重")
        
        # 保存最后更新的轮次
        if updated:
            self.last_update_epoch = epoch
        
        return updated
    
    def get_losses(self):
        """获取分项损失"""
        if hasattr(self, 'last_losses'):
            return self.last_losses
        return None
    
    def get_weight_info(self):
        """获取子损失函数的类别权重信息"""
        weight_info = {}
        for name, loss in self.weight_supported_losses.items():
            if hasattr(loss, 'get_weight_info'):
                weight_info[name] = loss.get_weight_info()
        
        if not weight_info:
            return "无支持权重的损失函数"
        
        return weight_info 