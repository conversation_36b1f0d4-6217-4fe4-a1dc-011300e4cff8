"""
交叉熵损失函数
支持动态类别权重更新的交叉熵损失
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

from .base import BaseLoss


class CrossEntropyLoss(BaseLoss):
    """支持类别权重的交叉熵损失"""
    
    def __init__(self, num_classes=7, ignore_index=255, weight=None, reduction='mean', 
                 use_dynamic_weights=True, weight_method='inverse', label_smoothing=0.0):
        """初始化交叉熵损失
        
        Args:
            num_classes: 类别数量
            ignore_index: 忽略的标签值
            weight: 初始类别权重
            reduction: 损失聚合方式
            use_dynamic_weights: 是否使用动态类别权重
            weight_method: 权重计算方法
            label_smoothing: 标签平滑系数
        """
        super().__init__()
        self.num_classes = num_classes
        self.ignore_index = ignore_index
        self.reduction = reduction
        self.supports_class_weights = use_dynamic_weights
        self.weights = weight
        self.weight_method = weight_method
        self.label_smoothing = label_smoothing
        
        self.loss_fn = nn.CrossEntropyLoss(
            weight=weight,
            ignore_index=ignore_index,
            reduction=reduction,
            label_smoothing=label_smoothing
        )
        
        # 记录初始化信息
        self.logger.info(f"交叉熵损失: 类别数={num_classes}, 忽略索引={ignore_index}")
        
        if weight is not None:
            from src.utils.dataset_statistics import get_weight_stats
            self.logger.info(f"初始类别权重: {get_weight_stats(weight)}")
        else:
            self.logger.info("初始化无类别权重")
            
        if use_dynamic_weights:
            self.logger.info(f"启用动态类别权重更新, 方法: {weight_method}")
    
    def forward(self, inputs, targets):
        """计算损失"""
        self.last_loss = self.loss_fn(inputs, targets)
        return self.last_loss
    
    def update_weights(self, class_counts, epoch=None, alpha=0.8):
        """更新类别权重"""
        if not self.supports_class_weights:
            return False
            
        from src.utils.dataset_statistics import (calculate_class_weights,
                                                  get_weight_stats)

        # 获取当前设备
        device = next(self.parameters()).device if list(self.parameters()) else torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 记录设备信息
        self.logger.info(f"CrossEntropyLoss - 当前设备: {device}, 类别统计数据设备: {class_counts.device}")
        
        # 确保类别计数在正确设备上
        if device.type != 'cpu' and class_counts.device.type == 'cpu':
            class_counts = class_counts.to(device)
            self.logger.info(f"CrossEntropyLoss - 将类别统计数据移动到设备 {device} 上")
        
        # 使用通用权重计算策略
        new_weights = calculate_class_weights(class_counts, method=self.weight_method, max_ratio=10.0)
        self.logger.info(f"使用通用权重计算策略 (方法: {self.weight_method})")
        
        self.logger.info(f"CrossEntropyLoss - 计算得到的权重设备: {new_weights.device}")
        
        # 确保权重在正确的设备上
        new_weights = new_weights.to(device)
        self.logger.info(f"CrossEntropyLoss - 移动后的权重设备: {new_weights.device}")
        
        # 记录更新前的权重状态
        old_weight_stats = get_weight_stats(self.weights) if hasattr(self, 'weights') and self.weights is not None else "无权重"
        
        # 如果已经有权重，使用指数移动平均更新
        if hasattr(self, 'weights') and self.weights is not None:
            self.weights = alpha * self.weights + (1 - alpha) * new_weights
        else:
            self.weights = new_weights
        
        # 更新损失函数
        self.loss_fn = nn.CrossEntropyLoss(
            weight=self.weights,
            ignore_index=self.ignore_index,
            reduction=self.reduction,
            label_smoothing=self.label_smoothing
        )
        
        # 记录更新后的权重状态
        new_weight_stats = get_weight_stats(self.weights)
        
        # 记录日志
        self.logger.info(f"类别权重更新: {old_weight_stats} -> {new_weight_stats}")
        if epoch is not None:
            self.logger.info(f"Epoch {epoch}: 类别权重已更新")
        
        # 保存最后更新的轮次
        self.last_update_epoch = epoch
        return True
    
    def get_losses(self):
        """获取分项损失"""
        return getattr(self, 'last_loss', torch.tensor(0.0)).item()
    
    def get_weight_info(self):
        """获取权重信息"""
        if not hasattr(self, 'weights') or self.weights is None:
            return "未初始化权重"
        
        from src.utils.dataset_statistics import get_weight_stats
        return get_weight_stats(self.weights) 