"""
损失函数工厂
根据配置创建各种类型的损失函数实例
"""

import logging
from typing import Any, Callable, Dict, Optional, Type

import torch
from src.utils.config import Config

from .base import BaseLoss
from .combined import CombinedLoss
from .cross_entropy import CrossEntropyLoss
from .dice import DiceLoss
from .focal import FocalLoss
from .lovasz import LovaszLoss


def get_loss_fn(config: Config) -> 'BaseLoss':
    """获取损失函数实例
    
    Args:
        config: 损失函数配置对象
        
    Returns:
        损失函数实例
    """
    # 从配置中读取基础字段
    loss_name = config.get('loss.name').lower()
    num_classes = config.get('loss.num_classes')
    ignore_index = config.get('loss.ignore_index')
    
    # 提取类别权重配置（新格式）
    class_weight_config = {
        'enabled': config.get('loss.class_weights.enabled'),
        'method': config.get('loss.class_weights.method'),
        'update_freq': config.get('loss.class_weights.update_freq'),
        'smoothing': config.get('loss.class_weights.smoothing'),
        'initial': config.get('loss.class_weights.initial')
    }
    
    logger = logging.getLogger(__name__)
    logger.info("="*80)
    logger.info(f"创建损失函数: {loss_name}")
    
    # 记录类别权重信息
    if class_weight_config['enabled']:
        # 检查损失函数是否支持类别权重
        if loss_name in ['dice', 'lovasz']:
            logger.warning("注意: 当前损失函数不支持类别权重")
    else:
        logger.info("类别权重: 不使用动态计算")
    logger.info("="*80)
    
    # 组合损失特殊处理
    if config.get('loss.type') == 'combined':
        logger.info("组合损失: 创建组合损失函数")
        return _create_combined_loss(config, num_classes, ignore_index, class_weight_config, logger)
    else:
    # 基础损失函数创建映射
        loss_creators = {
            'ce': _create_ce_loss,
            'cross_entropy': _create_ce_loss,
            'focal': _create_focal_loss,
            'dice': _create_dice_loss,
            'lovasz': _create_lovasz_loss,
            'bce': _create_bce_loss
        }
        
        # 查找并调用对应的创建函数
        creator = loss_creators.get(config.get('loss.type'))
        if creator:
            return creator(config, num_classes, ignore_index, class_weight_config, logger)
        else:
            raise ValueError(f"不支持的损失函数类型: {loss_name}")

# 通用创建交叉熵损失函数
def _create_ce_loss(config, num_classes, ignore_index, class_weight_config, logger=None, is_sub_loss=False):
    """创建交叉熵损失函数
    
    Args:
        config: 配置对象
        num_classes: 类别数量
        ignore_index: 忽略索引
        class_weight_config: 类别权重配置
        logger: 日志记录器
        is_sub_loss: 是否为组合损失的子损失
        
    Returns:
        交叉熵损失函数实例
    """
    label_smoothing = config.get('loss.label_smoothing')
    reduction = config.get('loss.reduction')
    
    # 只有主损失才记录日志
    if logger and not is_sub_loss:
        logger.info(f"交叉熵损失配置: 标签平滑={label_smoothing}, 聚合方式={reduction}")
    elif logger:
        logger.info(f"CE Loss - label_smoothing: {label_smoothing}, reduction: {reduction}")
    
    return CrossEntropyLoss(
        num_classes=num_classes,
        ignore_index=ignore_index,
        weight=class_weight_config['initial'],  # 初始类别权重
        use_dynamic_weights=class_weight_config['enabled'],
        weight_method=class_weight_config['method'],
        label_smoothing=label_smoothing,
        reduction=reduction
    )

# 创建二元交叉熵损失函数
def _create_bce_loss(config, num_classes, ignore_index, class_weight_config, logger=None, is_sub_loss=False):
    """创建二元交叉熵损失函数
    
    Args:
        config: 配置对象
        num_classes: 类别数量
        ignore_index: 忽略索引
        class_weight_config: 类别权重配置
        logger: 日志记录器
        is_sub_loss: 是否为组合损失的子损失
        
    Returns:
        二元交叉熵损失函数实例
    """
    # 修复：使用默认值防止None覆盖
    label_smoothing = config.get('loss.label_smoothing')
    logger.info(f"label_smoothing: {label_smoothing}")
    reduction = config.get('loss.reduction')
    logger.info(f"reduction: {reduction}")
    pos_weight = config.get('loss.pos_weight')
    logger.info(f"pos_weight: {pos_weight}")
    
    # 只有主损失才记录日志
    if logger and not is_sub_loss:
        logger.info(f"二元交叉熵损失配置: 标签平滑={label_smoothing}, 聚合方式={reduction}")
        if pos_weight is not None:
            logger.info(f"正样本权重: {pos_weight}")
    
    return CrossEntropyLoss(
        num_classes=2,  # 二元分类
        ignore_index=ignore_index,
        weight=class_weight_config['initial'],
        use_dynamic_weights=class_weight_config['enabled'],
        weight_method=class_weight_config['method'],
        label_smoothing=label_smoothing,
        reduction=reduction,
        pos_weight=pos_weight,
        binary=True
    )

# 通用创建Focal损失函数
def _create_focal_loss(config, num_classes, ignore_index, class_weight_config, logger=None, is_sub_loss=False):
    """创建Focal损失函数
    
    Args:
        config: 配置对象
        num_classes: 类别数量
        ignore_index: 忽略索引
        class_weight_config: 类别权重配置
        logger: 日志记录器
        is_sub_loss: 是否为组合损失的子损失
        
    Returns:
        Focal损失函数实例
    """
    alpha = config.get('loss.alpha')
    gamma = config.get('loss.gamma')
    reduction = config.get('loss.reduction')
    
    # 只有主损失才记录日志
    if logger and not is_sub_loss:
        logger.info(f"Focal损失配置: alpha={alpha}, gamma={gamma}, 聚合方式={reduction}")
    elif logger:
        logger.info(f"Focal Loss - alpha: {alpha}, gamma: {gamma}, reduction: {reduction}")
    
    return FocalLoss(
        num_classes=num_classes,
        ignore_index=ignore_index,
        alpha=alpha,
        gamma=gamma,
        weight=class_weight_config['initial'],
        use_dynamic_weights=class_weight_config['enabled'],
        weight_method=class_weight_config['method'],
        reduction=reduction
    )

# 通用创建Dice损失函数
def _create_dice_loss(config, num_classes, ignore_index, class_weight_config, logger=None, is_sub_loss=False):
    """创建Dice损失函数
    
    Args:
        config: 配置对象
        num_classes: 类别数量
        ignore_index: 忽略索引
        class_weight_config: 类别权重配置 (Dice损失不支持)
        logger: 日志记录器
        is_sub_loss: 是否为组合损失的子损失
        
    Returns:
        Dice损失函数实例
    """
    smooth = config.get('loss.smooth')
    square = config.get('loss.square')
    per_class = config.get('loss.per_class')
    reduction = config.get('loss.reduction')
    
    # 只有主损失才记录日志
    if logger and not is_sub_loss:
        logger.info(f"Dice损失配置: 平滑系数={smooth}, 平方={square}, 每类计算={per_class}, 聚合方式={reduction}")
    elif logger:
        logger.info(f"Dice Loss - smooth: {smooth}, square: {square}, per_class: {per_class}, reduction: {reduction}")
        
        if class_weight_config['enabled']:
            logger.warning("注意: Dice损失不支持类别权重，将忽略类别权重配置")
    
    return DiceLoss(
        num_classes=num_classes,
        ignore_index=ignore_index,
        smooth=smooth,
        square=square,
        per_class=per_class,
        reduction=reduction
    )

# 通用创建Lovasz损失函数
def _create_lovasz_loss(config, num_classes, ignore_index, class_weight_config, logger=None, is_sub_loss=False):
    """创建Lovasz损失函数
    
    Args:
        config: 配置对象
        num_classes: 类别数量
        ignore_index: 忽略索引
        class_weight_config: 类别权重配置 (Lovasz损失不支持)
        logger: 日志记录器
        is_sub_loss: 是否为组合损失的子损失
        
    Returns:
        Lovasz损失函数实例
    """
    per_image = config.get('loss.per_image')
    reduction = config.get('loss.reduction')
    
    # 只有主损失才记录日志
    if logger and not is_sub_loss:
        logger.info(f"Lovasz损失配置: 每图计算={per_image}, 聚合方式={reduction}")
    elif logger:
        logger.info(f"Lovasz Loss - per_image: {per_image}, reduction: {reduction}")
        
        if class_weight_config['enabled']:
            logger.warning("注意: Lovasz损失不支持类别权重，将忽略类别权重配置")
    
    return LovaszLoss(
        num_classes=num_classes,
        ignore_index=ignore_index,
        per_image=per_image,
        reduction=reduction
    )

# 辅助函数：创建组合损失
def _create_combined_loss(config, num_classes, ignore_index, class_weight_config, logger):
    """创建组合损失函数
    
    Args:
        config: 配置对象
        num_classes: 类别数量
        ignore_index: 忽略索引
        class_weight_config: 类别权重配置
        logger: 日志记录器
        
    Returns:
        组合损失函数实例
    """
    losses = {}
    weights = {}  # 这是组合权重，控制各子损失函数在总损失中的比例
    
    logger.info(f"组合损失配置")
    
    # 获取损失函数配置（新格式）
    components = config.get('loss.components')
    if not components:
        raise ValueError("组合损失必须在'components'中定义子损失函数")
    
    logger.info(f"组合损失包含以下组件: {', '.join(components.keys())}")
    
    # 创建子损失函数的映射
    loss_creators = {
        'ce': lambda cfg, nc, ii, cwc: _create_ce_loss(cfg, nc, ii, cwc, None, True),
        'cross_entropy': lambda cfg, nc, ii, cwc: _create_ce_loss(cfg, nc, ii, cwc, None, True),
        'focal': lambda cfg, nc, ii, cwc: _create_focal_loss(cfg, nc, ii, cwc, None, True),
        'dice': lambda cfg, nc, ii, cwc: _create_dice_loss(cfg, nc, ii, cwc, None, True),
        'lovasz': lambda cfg, nc, ii, cwc: _create_lovasz_loss(cfg, nc, ii, cwc, None, True),
        'bce': lambda cfg, nc, ii, cwc: _create_bce_loss(cfg, nc, ii, cwc, None, True)
    }
    
    # 是否有支持类别权重的子损失
    has_weight_supported_loss = False

    # 保存原始配置值，以便后续恢复
    original_values = {}

    # 逐个创建损失函数
    for name, component_cfg in components.items():
        # 获取类型和组合权重
        loss_type = component_cfg.get('type')
        combination_weight = component_cfg.get('combination_weight')  # 组合权重
        
        if not loss_type:
            raise ValueError(f"组件 {name} 必须提供type字段")
        
        logger.info(f"  - {name} ({loss_type}), 组合权重: {combination_weight}")
        
        # 检查是否支持类别权重
        supports_weights = loss_type not in ['dice', 'lovasz']
        if supports_weights:
            has_weight_supported_loss = True
        elif class_weight_config['enabled']:
            logger.warning(f"  注意: 组件 {name} ({loss_type}) 不支持类别权重")
        
        # 添加全局参数（如果需要）
        if num_classes is not None and 'num_classes' not in component_cfg:
            component_cfg['num_classes'] = num_classes
        if ignore_index is not None and 'ignore_index' not in component_cfg:
            component_cfg['ignore_index'] = ignore_index
            
        # 创建子损失函数的类别权重配置
        sub_class_weight_config = class_weight_config.copy()
        
        # 临时替换全局配置参数
        for k, v in component_cfg.items():
            if k != 'type' and k != 'combination_weight':
                # 保存原始值以便稍后恢复
                original_key = f'loss.{k}'
                original_values[original_key] = config.get(original_key)
                # 设置临时值
                config.set(original_key, v)
        
        # 使用临时配置创建子损失函数
        creator = loss_creators.get(loss_type)
        if creator:
            losses[name] = creator(config, num_classes, ignore_index, sub_class_weight_config)
        else:
            raise ValueError(f"不支持的子损失类型: {loss_type}")
            
        # 恢复原始配置值
        for k, v in original_values.items():
            if v is not None:
                config.set(k, v)
            # 如果原始值为None（不存在），也清除临时设置的值
            # else:
            #    ...  # 当前Config类不支持删除键，所以保持临时值
        
        # 清空临时存储，准备下一个组件
        original_values.clear()
        
        weights[name] = combination_weight
    
    # 类别权重使用警告
    if class_weight_config['enabled'] and not has_weight_supported_loss:
        logger.warning("组合损失启用了类别权重，但所有子损失都不支持类别权重，此配置无效")

    return CombinedLoss(
        losses=losses,
        weights=weights,  # 组合权重
        use_dynamic_weights=class_weight_config['enabled'] and has_weight_supported_loss  # 是否支持动态类别权重更新
    )
