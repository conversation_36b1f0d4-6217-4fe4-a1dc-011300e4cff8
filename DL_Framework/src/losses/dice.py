"""
Dice损失函数
用于语义分割的基于区域重叠的损失函数，不直接支持类别权重
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

from .base import BaseLoss


class DiceLoss(BaseLoss):
    """Dice损失 - 不支持类别权重"""
    
    def __init__(
        self, 
        num_classes=7, 
        ignore_index=255, 
        smooth=1.0, 
        per_class=False,
        square=False,
        reduction='mean'
    ):
        """初始化Dice损失
        
        Args:
            num_classes: 类别数量
            ignore_index: 忽略的标签值
            smooth: 平滑参数，防止除零
            per_class: 是否返回每个类别的损失
            square: 是否对预测和目标进行平方
            reduction: 损失聚合方式
        """
        super().__init__()
        self.num_classes = num_classes
        self.ignore_index = ignore_index
        self.smooth = smooth
        self.per_class = per_class
        self.square = square
        self.reduction = reduction
        self.supports_class_weights = False
        
        self.logger.info(f"Dice损失: 类别数={num_classes}, 平滑参数={smooth}, 忽略索引={ignore_index}")
        self.logger.info("Dice损失不支持类别权重")
    
    def forward(self, inputs, targets):
        """计算Dice损失"""
        # 修复：使用sigmoid激活函数，而不是softmax
        # 这与原始成功版本保持一致
        inputs = torch.sigmoid(inputs)
        
        # 存储每个类别的Dice系数
        dice_coeffs = []
        
        # 计算每个类别的Dice系数
        for cls in range(self.num_classes):
            # 获取当前类别的预测和真实标签
            target_cls = (targets == cls).float()
            input_cls = inputs[:, cls]
            
            # 忽略指定的索引
            if self.ignore_index is not None:
                mask = (targets != self.ignore_index).float()
                target_cls = target_cls * mask
                input_cls = input_cls * mask
            
            # 计算交集和并集
            intersection = (input_cls * target_cls).sum()
            union = input_cls.sum() + target_cls.sum()
            
            # 计算Dice系数
            dice_coeff = (2. * intersection + self.smooth) / (union + self.smooth)
            dice_coeffs.append(dice_coeff)
        
        # 计算平均Dice损失
        dice_coeffs = torch.stack(dice_coeffs)
        self.last_dice_per_class = 1.0 - dice_coeffs
        
        # 返回整体损失
        self.last_loss = 1.0 - dice_coeffs.mean()
        
        if self.per_class:
            return self.last_dice_per_class
        else:
            return self.last_loss
    
    def get_losses(self):
        """获取分项损失"""
        return getattr(self, 'last_loss', torch.tensor(0.0)).item()
    
    def get_per_class_loss(self):
        """获取每个类别的损失"""
        if hasattr(self, 'last_dice_per_class'):
            return self.last_dice_per_class.detach().cpu().tolist()
        return None 