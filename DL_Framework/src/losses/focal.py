"""
Focal损失函数
专为处理类别不平衡问题设计，支持动态类别权重
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

from .base import BaseLoss


class FocalLoss(BaseLoss):
    """支持类别权重的Focal损失"""
    
    def __init__(self, num_classes=7, alpha=0.25, gamma=2.0, ignore_index=255, 
                 weight=None, reduction='mean', use_dynamic_weights=True, weight_method='inverse'):
        """初始化Focal损失
        
        Args:
            num_classes: 类别数量
            alpha: 平衡因子
            gamma: 聚焦参数
            ignore_index: 忽略的标签值
            weight: 初始类别权重
            reduction: 损失聚合方式
            use_dynamic_weights: 是否使用动态类别权重
            weight_method: 权重计算方法
        """
        super().__init__()
        self.num_classes = num_classes
        self.alpha = alpha
        self.gamma = gamma
        self.ignore_index = ignore_index
        self.reduction = reduction
        self.supports_class_weights = use_dynamic_weights
        self.weights = weight
        self.weight_method = weight_method
        
        # 记录初始化信息
        self.logger.info(f"Focal损失: 类别数={num_classes}, alpha={alpha}, gamma={gamma}, 忽略索引={ignore_index}")
        
        if weight is not None:
            from src.utils.dataset_statistics import get_weight_stats
            self.logger.info(f"初始类别权重: {get_weight_stats(weight)}")
        else:
            self.logger.info("初始化无类别权重")
            
        if use_dynamic_weights:
            self.logger.info(f"启用动态类别权重更新, 方法: {weight_method}")
    
    def forward(self, inputs, targets):
        """计算Focal损失"""
        # 准备忽略掩码
        ignore_mask = targets != self.ignore_index
        
        # 获取类别数
        if inputs.dim() > 2:
            inputs = inputs.permute(0, 2, 3, 1).contiguous().view(-1, self.num_classes)
            targets = targets.view(-1)
            
        # 过滤掉要忽略的位置
        if self.ignore_index is not None:
            valid_idx = torch.nonzero(ignore_mask.view(-1), as_tuple=True)[0]
            if valid_idx.numel() == 0:
                return torch.tensor(0.0, device=inputs.device)
            inputs = inputs[valid_idx]
            targets = targets[valid_idx]
        
        # 计算交叉熵损失
        ce_loss = F.cross_entropy(
            inputs, targets, 
            weight=self.weights,
            reduction='none'
        )
        
        # 计算概率
        pt = torch.exp(-ce_loss)
        # 计算Focal项
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        # 应用alpha平衡因子
        if self.alpha is not None:
            # 创建alpha张量
            if isinstance(self.alpha, (float, int)):
                alpha_t = torch.ones_like(targets, device=inputs.device) * self.alpha
                alpha_t = torch.where(targets > 0, alpha_t, 1 - self.alpha)
            else:
                # 如果alpha是向量
                alpha_t = torch.ones_like(targets, device=inputs.device)
                for cls in range(self.num_classes):
                    alpha_t[targets == cls] = self.alpha[cls]
            
            focal_loss = alpha_t * focal_loss
        
        # 应用reduction
        if self.reduction == 'mean':
            self.last_loss = focal_loss.mean()
        elif self.reduction == 'sum':
            self.last_loss = focal_loss.sum()
        else:
            self.last_loss = focal_loss
            
        return self.last_loss
    
    def update_weights(self, class_counts, epoch=None, alpha=0.8):
        """更新类别权重"""
        if not self.supports_class_weights:
            return False
            
        from src.utils.dataset_statistics import (calculate_class_weights,
                                                  get_weight_stats)

        # 获取当前设备
        device = next(self.parameters()).device if list(self.parameters()) else torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 记录设备信息
        self.logger.info(f"FocalLoss - 当前设备: {device}, 类别统计数据设备: {class_counts.device}")
        
        # 确保类别计数在正确设备上
        if device.type != 'cpu' and class_counts.device.type == 'cpu':
            class_counts = class_counts.to(device)
            self.logger.info(f"FocalLoss - 将类别统计数据移动到设备 {device} 上")
        
        # 计算新的权重
        new_weights = calculate_class_weights(class_counts, method=self.weight_method)
        self.logger.info(f"FocalLoss - 计算得到的权重设备: {new_weights.device}")
        
        # 确保权重在正确的设备上
        new_weights = new_weights.to(device)
        self.logger.info(f"FocalLoss - 移动后的权重设备: {new_weights.device}")
        
        # 记录更新前的权重状态
        old_weight_stats = get_weight_stats(self.weights) if hasattr(self, 'weights') and self.weights is not None else "无权重"
        
        # 使用指数移动平均更新权重
        if hasattr(self, 'weights') and self.weights is not None:
            self.weights = alpha * self.weights + (1 - alpha) * new_weights
        else:
            self.weights = new_weights
        
        # 记录更新后的权重状态
        new_weight_stats = get_weight_stats(self.weights)
        
        # 记录日志
        self.logger.info(f"Focal损失类别权重更新: {old_weight_stats} -> {new_weight_stats}")
        if epoch is not None:
            self.logger.info(f"Epoch {epoch}: Focal损失类别权重已更新")
        
        # 保存最后更新的轮次
        self.last_update_epoch = epoch
        return True
    
    def get_losses(self):
        """获取分项损失"""
        return getattr(self, 'last_loss', torch.tensor(0.0)).item()
    
    def get_weight_info(self):
        """获取权重信息"""
        if not hasattr(self, 'weights') or self.weights is None:
            return "未初始化权重"
        
        from src.utils.dataset_statistics import get_weight_stats
        return get_weight_stats(self.weights) 