"""
Callback 模块

定义了回调系统，用于在训练过程中的特定节点执行自定义逻辑。
这是框架可扩展性的核心。
"""
import logging
import os
import psutil
import time
from typing import Any, Dict, Optional

import torch
from rich import print as rich_print
from rich.panel import Panel
from rich.progress import (BarColumn, Progress, SpinnerColumn, TextColumn,
                           TimeElapsedColumn, MofNCompleteColumn,
                           TimeRemainingColumn)
from rich.table import Table
# 从新项目中导入
from src.training.distributed import is_main_process

from src.training.metrics import ConfusionMatrix
from src.utils.experiment import ExperimentManager
# --- 新增：从新的日志模块导入 ---
from src.logging.rich_handler import (
    RichProgressBar, 
    get_system_stats, 
    create_summary_panel,
    cleanup_pynvml
)
from src.logging.tensorboard_handler import (
    log_confusion_matrix_figure,
    log_segmentation_predictions
)
from rich.console import Console

# 依赖应该是强制性的，而不是可选的。
# 如果功能需要，则必须安装其依赖。
# 移除所有 try-except 和 *_AVAILABLE 标志。
import matplotlib.pyplot as plt
import numpy as np
# ray.tune 的导入将移动到实际使用它的 TuneReportCallback 中，
# 使其成为该特定回调的强制依赖，而不是整个文件的全局依赖。


class Callback:
    """
    回调的抽象基类
    """
    def __init__(self):
        self.trainer: Optional['Trainer'] = None

    def set_trainer(self, trainer: 'Trainer'):
        """由 Trainer 调用，用于将 trainer 实例注入回调"""
        self.trainer = trainer

    def on_train_begin(self, logs: Optional[Dict[str, Any]] = None):
        """在训练开始时调用"""
        pass

    def on_train_end(self, logs: Optional[Dict[str, Any]] = None):
        """在训练结束时调用"""
        pass

    def on_epoch_begin(self, epoch: int, logs: Optional[Dict[str, Any]] = None):
        """在每个周期开始时调用"""
        pass

    def on_epoch_end(self, epoch: int, logs: Optional[Dict[str, Any]] = None):
        """在每个周期结束时调用"""
        pass

    def on_batch_begin(self, batch: int, logs: Optional[Dict[str, Any]] = None):
        """在每个训练批次开始时调用"""
        pass

    def on_batch_end(self, batch: int, logs: Optional[Dict[str, Any]] = None):
        """在每个训练批次结束时调用"""
        pass
        
    def on_validation_batch_end(self, batch: int, logs: Optional[Dict[str, Any]] = None):
        """在每个验证批次结束时调用"""
        pass

    def on_validation_end(self, logs: Optional[Dict[str, Any]] = None):
        """在验证阶段完全结束后调用"""
        pass

class CallbackList:
    """回调容器，统一管理和调用一组回调"""
    def __init__(self, callbacks: Optional[list] = None):
        self.callbacks = callbacks or []

    def set_trainer(self, trainer: 'Trainer'):
        for callback in self.callbacks:
            callback.set_trainer(trainer)

    def on_train_begin(self, logs: Optional[Dict[str, Any]] = None):
        for callback in self.callbacks:
            callback.on_train_begin(logs)

    def on_train_end(self, logs: Optional[Dict[str, Any]] = None):
        for callback in self.callbacks:
            callback.on_train_end(logs)

    def on_epoch_begin(self, epoch: int, logs: Optional[Dict[str, Any]] = None):
        for callback in self.callbacks:
            callback.on_epoch_begin(epoch, logs)

    def on_epoch_end(self, epoch: int, logs: Optional[Dict[str, Any]] = None):
        for callback in self.callbacks:
            callback.on_epoch_end(epoch, logs)

    def on_batch_begin(self, batch: int, logs: Optional[Dict[str, Any]] = None):
        for callback in self.callbacks:
            callback.on_batch_begin(batch, logs)

    def on_batch_end(self, batch: int, logs: Optional[Dict[str, Any]] = None):
        for callback in self.callbacks:
            callback.on_batch_end(batch, logs)
            
    def on_validation_batch_end(self, batch: int, logs: Optional[Dict[str, Any]] = None):
        for callback in self.callbacks:
            callback.on_validation_batch_end(batch, logs)

    def on_validation_end(self, logs: Optional[Dict[str, Any]] = None):
        """在验证阶段完全结束后调用所有回调"""
        for callback in self.callbacks:
            callback.on_validation_end(logs)

    def __iter__(self):
        return iter(self.callbacks)


# ==========================================================================================
# CONCRETE CALLBACK IMPLEMENTATIONS
# ==========================================================================================

class LRSchedulerCallback(Callback):
    """在每个周期结束后更新学习率调度器"""
    def __init__(self, scheduler):
        super().__init__()
        self.scheduler = scheduler

    def on_epoch_end(self, epoch: int, logs: Optional[Dict[str, Any]] = None):
        if self.scheduler:
            self.scheduler.step()
            if is_main_process():
                lr = self.trainer.optimizer.param_groups[0]['lr']
                logging.info(f"Epoch {epoch}: 学习率更新为 {lr:.6f}")


class MetricsCallback(Callback):
    """计算并记录验证集指标 (mIoU)"""
    def __init__(self, num_classes: int, ignore_index: int = -100):
        super().__init__()
        self.confusion_matrix = ConfusionMatrix(num_classes, ignore_index)
        
    def on_epoch_begin(self, epoch: int, logs: Optional[Dict[str, Any]] = None):
        """在每个周期开始时重置混淆矩阵"""
        self.confusion_matrix.reset()

    def on_validation_batch_end(self, batch: int, logs: Optional[Dict[str, Any]] = None):
        """在每个验证批次结束后，累积混淆矩阵"""
        # 从 trainer 的 logs 中获取模型输出和目标
        outputs = self.trainer.logs.get('outputs')
        targets = self.trainer.logs.get('targets')
        
        if outputs is not None and targets is not None:
            preds = outputs.argmax(dim=1)
            self.confusion_matrix.update(preds, targets)

    def on_validation_end(self, logs: Optional[Dict[str, Any]] = None):
        """
        在验证阶段结束后计算并记录所有指标。
        这是计算指标最合适的时间点，因为可以确保所有验证批次都已完成。
        """
            # 修复Bug: 调用 metrics.py 中正确的 compute() 方法
        metrics = self.confusion_matrix.compute()
        
        # 将所有计算出的指标更新到logs中，供其他回调使用
        # logs 字典会在所有回调之间共享和传递
        if logs is not None:
            logs.update(metrics)
        
        # 如果是主进程，则打印详细的指标分析报告
        if is_main_process():
            self.confusion_matrix.print_metrics()

    def on_epoch_end(self, epoch: int, logs: Optional[Dict[str, Any]] = None):
        """
        在周期结束时，只负责打印日志，不再进行计算。
        计算已在 on_validation_end 中完成。
        """
        # 仅在验证集上操作，并且日志中已经包含计算好的指标
        if logs and logs.get('val_loss') is not None and is_main_process():
            # 这里可以保留或添加Epoch结束时的摘要日志
            # 例如，可以从 logs 中读取 miou 并打印
            miou = logs.get('miou', float('nan'))
            logging.info(f"Epoch {epoch} validation summary: mIoU = {miou:.4f}")
                
    def on_train_end(self, logs: Optional[Dict[str, Any]] = None):
        """训练结束时可以执行一些最终操作，例如保存最终的混淆矩阵"""
        pass # 目前不需要实现


# RichProgressBar 类已被移至 src/logging/rich_handler.py


class RichProgressCallback(Callback):
    """
    使用Rich库提供美观的训练进度条，并记录训练/验证损失和指标
    """
    def __init__(self):
        super().__init__()
        self.progress: Optional[Progress] = None
        self.train_task_id = None
        self.val_task_id = None
        self.start_time = None
        # pynvml 的初始化和可用性检查已移至 rich_handler

    def on_train_begin(self, logs: Optional[Dict[str, Any]] = None):
        """训练开始时，初始化Progress对象并记录开始时间。"""
        self.start_time = time.monotonic()
        if is_main_process():
            self.progress = RichProgressBar()
            self.progress.start()
            logging.info("="*30 + "  开始训练  " + "="*30)
            logging.info(f"总共 {logs.get('epochs')} 个训练周期。")

    def on_epoch_begin(self, epoch: int, logs: Optional[Dict[str, Any]] = None):
        """每个epoch开始时，添加新的训练和验证任务到进度条中。"""
        if self.progress is not None and is_main_process():
            # 新增：在下一个 epoch 开始时，打印上一个 epoch 的分隔符
            if epoch > 1:
                separator_text = f" Epoch {epoch - 1} Finished "
                logging.info(f"{separator_text:=^120}")

            # 获取当前的最佳mIoU以供显示
            best_miou = 0.0
            for cb in self.trainer.callbacks:
                if isinstance(cb, CheckpointCallback):
                    best_miou = cb.best_score
                    break
            
            total_train_steps = len(self.trainer.train_loader)
            self.train_task_id = self.progress.add_task(
                f"[bold magenta]Train Epoch {epoch}", 
                total=total_train_steps, 
                loss=float('nan'),
                best_miou=best_miou,
                system_stats=""
            )

            if self.trainer.val_loader:
                total_val_steps = len(self.trainer.val_loader)
                self.val_task_id = self.progress.add_task(
                    f"[bold cyan]Valid Epoch {epoch}", 
                    total=total_val_steps, 
                    loss=float('nan'),
                    best_miou=best_miou,
                    system_stats=""
                )

    def on_batch_end(self, batch: int, logs: Optional[Dict[str, Any]] = None):
        """训练批次结束时，更新训练进度条。"""
        if self.progress is not None and is_main_process():
            loss = logs.get('loss', 0.0)
            # --- 修改：调用新的独立函数 ---
            stats_str = get_system_stats(self.trainer.device)
            if stats_str:
                stats_str = f" | {stats_str}"
            self.progress.update(self.train_task_id, advance=1, loss=loss, system_stats=stats_str)

    def on_validation_batch_end(self, batch: int, logs: Optional[Dict[str, Any]] = None):
        """验证批次结束时，更新验证进度条。"""
        if self.progress is not None and is_main_process():
            val_loss = logs.get('val_loss', 0.0)
            # --- 修改：调用新的独立函数 ---
            stats_str = get_system_stats(self.trainer.device)
            if stats_str:
                stats_str = f" | {stats_str}"
            self.progress.update(self.val_task_id, advance=1, loss=val_loss, system_stats=stats_str)

    def on_epoch_end(self, epoch: int, logs: Optional[Dict[str, Any]] = None):
        """Epoch结束时，用rich的日志功能打印总结。"""
        if self.progress is not None and is_main_process():
             # 准备日志信息
            log_str = f"Epoch {epoch}/{self.trainer.epochs} | "
            log_str += f"Train Loss: [bold green]{logs.get('train_loss'):.4f}[/bold green]"
            
            if 'val_loss' in logs:
                log_str += f" | Val Loss: [bold cyan]{logs.get('val_loss'):.4f}[/bold cyan]"
                
            if 'accuracy' in logs:
                log_str += f" | Val Acc: [bold blue]{logs.get('accuracy'):.4f}[/bold blue]"

            if 'miou' in logs: # 从MetricsCallback计算的结果中获取
                log_str += f" | Val mIoU: [bold yellow]{logs.get('miou'):.4f}[/bold yellow]"
            
            # 使用 logging.info，RichHandler会自动处理markup
            logging.info(log_str)
            
            # 可以在这里短暂停止进度条，打印日志，然后恢复
            self.progress.stop_task(self.train_task_id)
            self.progress.remove_task(self.train_task_id)
            if self.val_task_id is not None:
                self.progress.stop_task(self.val_task_id)
                self.progress.remove_task(self.val_task_id)


    def on_train_end(self, logs: Optional[Dict[str, Any]] = None):
        """训练结束时，停止并清理Progress对象，并打印最终的总结报告。"""
        if self.progress is not None and is_main_process():
            self.progress.stop()
            self.progress = None
            logging.info("="*30 + "  训练结束  " + "="*30)
        
        if is_main_process():
            total_duration_seconds = time.monotonic() - self.start_time
            # --- 修改：调用新的面板创建函数 ---
            summary_panel = create_summary_panel(logs, total_duration_seconds)

            # 将面板转换为纯文本后写入日志文件
            _console_tmp = Console(record=True, width=120)
            _console_tmp.print(summary_panel)
            panel_text = _console_tmp.export_text()
            logging.info("\n" + panel_text)
            logging.info("训练完成。")
            
        # --- 修改：调用新的清理函数 ---
        cleanup_pynvml()


# --- 新增的回调 ---

class CheckpointCallback(Callback):
    """
    负责在训练过程中保存模型检查点。
    - 保存最佳模型
    - 定期保存模型
    - 管理检查点数量
    """
    def __init__(self, exp_manager: ExperimentManager, monitor: str = 'miou', mode: str = 'max',
                 save_freq: int = 1, max_kept: int = 5):
        super().__init__()
        if mode not in ['min', 'max']:
            raise ValueError(f"CheckpointCallback mode '{mode}' is invalid. It should be 'min' or 'max'.")
        
        self.exp_manager = exp_manager
        self.monitor = monitor
        self.mode = mode
        self.save_freq = save_freq
        self.max_kept = max_kept
        
        # 初始化最佳分数的逻辑
        if self.mode == 'max':
            self.best_score = float('-inf')
        else:
            self.best_score = float('inf')
            
    def on_epoch_end(self, epoch: int, logs: Optional[Dict[str, Any]] = None):
        if not is_main_process():
            return

        # 判断是否为最佳模型
        current_score = self._get_monitor_value(logs)
        is_best = False
        if current_score is not None:
            if self.mode == 'max' and current_score > self.best_score:
                self.best_score = current_score
                is_best = True
                logging.info(f"Epoch {epoch}: {self.monitor} 提升至 {self.best_score:.4f}。保存最佳模型。")
            elif self.mode == 'min' and current_score < self.best_score:
                self.best_score = current_score
                is_best = True
                logging.info(f"Epoch {epoch}: {self.monitor} 降低至 {self.best_score:.4f}。保存最佳模型。")

        # 判断是否需要按频率保存
        save_periodically = self.save_freq > 0 and epoch % self.save_freq == 0

        # 只有在需要时才执行保存
        if is_best or save_periodically:
            self._save_checkpoint(epoch, is_best)

    def _save_checkpoint(self, epoch: int, is_best: bool):
        """准备状态并调用实验管理器进行保存"""
        # 准备要保存的状态字典
        state = {
            'epoch': epoch,
            'model': self.trainer.model.state_dict(),
            'optimizer': self.trainer.optimizer.state_dict(),
            'scheduler': self.trainer.scheduler.state_dict() if self.trainer.scheduler else None
        }
        
        # 调用实验管理器保存检查点
        self.exp_manager.save_checkpoint(
            state,
            epoch=epoch,
            is_best=is_best,
            max_keep=self.max_kept
        )
        
    def _get_monitor_value(self, logs: Dict[str, Any]) -> Optional[float]:
        """从logs中获取监控指标的值"""
        return logs.get(self.monitor)

class EarlyStoppingCallback(Callback):
    """
    当监控的指标在一定周期内不再改善时，提前终止训练。
    """
    def __init__(self, monitor: str = 'miou', mode: str = 'max', patience: int = 5, min_delta: float = 0):
        super().__init__()
        if mode not in ['min', 'max']:
            raise ValueError(f"EarlyStoppingCallback mode '{mode}' is invalid. It should be 'min' or 'max'.")
            
        self.monitor = monitor
        self.mode = mode
        self.patience = patience
        self.min_delta = min_delta
        self.wait = 0 # 记录未改善的周期数
        
        if self.mode == 'max':
            self.best_score = float('-inf')
        else:
            self.best_score = float('inf')

    def on_epoch_end(self, epoch: int, logs: Optional[Dict[str, Any]] = None):
        if not is_main_process():
            return
            
        current_score = logs.get(self.monitor)
        if current_score is None:
            return

        improved = False
        if self.mode == 'max':
            if current_score > self.best_score + self.min_delta:
                improved = True
        else: # min mode
            if current_score < self.best_score - self.min_delta:
                improved = True

        if improved:
            self.best_score = current_score
            self.wait = 0
            logging.debug(f"Epoch {epoch}: EarlyStopping monitor '{self.monitor}' 改善至 {self.best_score:.4f}. 重置等待计数。")
        else:
            self.wait += 1
            logging.info(f"Epoch {epoch}: EarlyStopping monitor '{self.monitor}' 未改善。等待计数: {self.wait}/{self.patience}.")
            if self.wait >= self.patience:
                self.trainer.stop_training = True
                logging.info(f"触发早停！指标 '{self.monitor}' 已连续 {self.patience} 个周期未改善。")


try:
    from torch.utils.tensorboard import SummaryWriter
    TENSORBOARD_AVAILABLE = True
except ImportError:
    SummaryWriter = None
    TENSORBOARD_AVAILABLE = False


class TensorBoardCallback(Callback):
    """使用TensorBoard记录训练过程"""

    def __init__(self, vis_freq: int = 10):
        super().__init__()
        self.writer = None
        self.val_batch_for_viz = None
        self.vis_freq = vis_freq

    def on_train_begin(self, logs: Optional[Dict[str, Any]] = None):
        """训练开始时初始化SummaryWriter，并缓存一个用于可视化的验证批次。"""
        if self.writer is None and is_main_process():
            # 修正：调用 ExperimentManager 的标准方法来获取路径，
            # 这使得路径管理逻辑集中在 ExperimentManager 中，更符合单一职责原则。
            log_dir = self.trainer.exp_manager.get_tensorboard_dir()
            self.writer = SummaryWriter(log_dir=log_dir)

        # 缓存一个批次用于可视化
        if self.trainer.val_loader:
            try:
                self.val_batch_for_viz = next(iter(self.trainer.val_loader))

                # --- 新增：记录模型计算图 (使用更健壮的方式) ---
                if self.val_batch_for_viz and isinstance(self.val_batch_for_viz, dict):
                    images = self.val_batch_for_viz.get('image')
                    if isinstance(images, torch.Tensor):
                        self.writer.add_graph(self.trainer.model, images.to(self.trainer.device))

            except StopIteration:
                self.val_batch_for_viz = None
                logging.warning("验证数据加载器为空，无法缓存用于可视化的批次。")


    def on_epoch_end(self, epoch: int, logs: Optional[Dict[str, Any]] = None):
        """在每个epoch结束时记录指标。"""
        if self.writer and is_main_process():
            # 遍历logs字典，记录所有数值指标
            for key, value in logs.items():
                if isinstance(value, (int, float)):
                    # 根据键名智能分组
                    if 'loss' in key:
                        group = 'Loss'
                        tag = key
                    elif 'class' in key:
                        metric, class_id = key.split('_class_')
                        group = f'Per-Class-Metrics/{metric.capitalize()}'
                        tag = f'class_{class_id}'
                    else:
                        group = 'Metrics'
                        tag = key
                    self.writer.add_scalar(f'{group}/{tag}', value, epoch)

            # 记录学习率
            lr = self.trainer.optimizer.param_groups[0]['lr']
            self.writer.add_scalar('LR/learning_rate', lr, epoch)

            # 记录模型参数的直方图
            for name, param in self.trainer.model.named_parameters():
                self.writer.add_histogram(f'Histograms/{name}', param, epoch)

            # 记录混淆矩阵图像
            self._log_confusion_matrix_to_tensorboard(epoch)
            
            # --- 修改：增加频率控制 ---
            is_first_epoch = (epoch == 1)
            is_last_epoch = (epoch == self.trainer.epochs)
            is_vis_epoch = (epoch % self.vis_freq == 0)

            if (is_first_epoch or is_last_epoch or is_vis_epoch) and self.val_batch_for_viz:
                self._log_predictions_to_tensorboard(epoch)

    def _log_predictions_to_tensorboard(self, epoch: int):
        """
        使用缓存的批次进行推理，并将结果可视化到TensorBoard。
        根据已知的字典结构直接访问数据。
        """
        # --- 最终修复：数据批次是字典，直接用键访问 ---
        if not isinstance(self.val_batch_for_viz, dict):
            logging.warning(f"期望的可视化批次类型为 dict，但得到 {type(self.val_batch_for_viz)}，跳过可视化。")
            return

        images = self.val_batch_for_viz.get('image')
        true_masks = self.val_batch_for_viz.get('mask')
        
        if images is None or true_masks is None:
            logging.warning("在验证批次字典中未能通过键 'image'/'mask' 找到张量。")
            return

        # 如果掩码是 [B, 1, H, W] 格式，则压缩为 [B, H, W]
        if true_masks.ndim == 4 and true_masks.shape[1] == 1:
            true_masks = true_masks.squeeze(1)

        images = images.to(self.trainer.device)
        
        # 将模型置于评估模式
        self.trainer.model.eval()
        with torch.no_grad():
            outputs = self.trainer.model(images)
            # 兼容模型输出为字典的情况
            if isinstance(outputs, dict):
                outputs = outputs.get('out')
            pred_masks = outputs.argmax(dim=1)
        
        # 将模型恢复到训练模式
        self.trainer.model.train()

        # 获取类别总数和忽略索引
        num_classes, ignore_index = self._get_class_info()
        if num_classes is None: return

        # 调用处理器函数
        log_segmentation_predictions(
            writer=self.writer,
            epoch=epoch,
            images=images.cpu(),
            true_masks=true_masks.cpu(),
            pred_masks=pred_masks.cpu(),
            num_classes=num_classes,
            ignore_index=ignore_index
        )

    def _get_class_info(self) -> (Optional[int], Optional[int]):
        """从 MetricsCallback 中安全地获取类别数和忽略索引"""
        for callback in self.trainer.callbacks:
            if isinstance(callback, MetricsCallback):
                num_classes = callback.confusion_matrix.num_classes
                # 修复：从 MetricsCallback 实例中获取 ignore_index
                ignore_index = callback.confusion_matrix.ignore_index
                return num_classes, ignore_index
        logging.warning("未找到 MetricsCallback，无法确定类别数/忽略索引以进行可视化。")
        return None, None

    def _log_confusion_matrix_to_tensorboard(self, epoch: int):
        """获取混淆矩阵并调用处理器函数将其记录为图像"""
        metrics_callback = None
        for callback in self.trainer.callbacks:
            if isinstance(callback, MetricsCallback):
                metrics_callback = callback
                break
        
        if not metrics_callback:
            return

        cm_matrix = metrics_callback.confusion_matrix.get_matrix()
        
        # 调用新的独立函数
        log_confusion_matrix_figure(self.writer, epoch, cm_matrix)

    def on_train_end(self, logs: Optional[Dict[str, Any]] = None):
        """训练结束时关闭SummaryWriter"""
        if self.writer and is_main_process():
            self.writer.close()

# ==========================================================================================
# NEW CALLBACK FOR RAY TUNE
# ==========================================================================================
class TuneReportCallback(Callback):
    """
    当与 Ray Tune 集成时，在每次验证结束后向其汇报指标。
    """
    def __init__(self):
        # 将导入语句放在构造函数中。
        # 如果开发者在配置中使用了这个回调，那么 ray[tune] 就必须被安装。
        # 这遵循了“用到时才检查”的原则，避免了对所有用户强制安装 ray。
        try:
            from ray import tune
            self._tune = tune
        except ImportError:
            # 提供清晰的错误信息，指导用户如何修复环境
            raise ImportError(
                "TuneReportCallback 需要 'ray[tune]'。请运行 `pip install 'ray[tune]'` 来安装它。"
            )

    def on_validation_end(self, logs: Optional[Dict[str, Any]] = None):
        """在验证结束时，将关键指标发送给Ray Tune。"""
        if logs:
            # 准备要汇报的指标字典
            metrics_to_report = {}
            
            # 安全地获取指标，如果不存在则跳过
            if 'miou' in logs:
                metrics_to_report['miou'] = logs.get('miou')
            if 'val_loss' in logs:
                metrics_to_report['val_loss'] = logs.get('val_loss')

            # 仅在有指标可汇报时才调用 report
            if metrics_to_report:
                self._tune.report(**metrics_to_report)


# ==========================================================================================
# FACTORY FUNCTION TO BUILD CALLBACKS FROM CONFIG
# ==========================================================================================

# 回调注册表：将配置文件中的名字映射到具体的类
CALLBACKS_REGISTRY = {
    'lr_scheduler': LRSchedulerCallback,
    'metrics': MetricsCallback,
    'rich_progress': RichProgressCallback,
    'checkpoint': CheckpointCallback,
    'early_stopping': EarlyStoppingCallback,
    'tensorboard': TensorBoardCallback,
    'tune_reporter': TuneReportCallback,
}

def build_callbacks(cfg: 'Config', *args, **kwargs) -> list[Callback]:
    callbacks = []
    for callback_config in cfg.callbacks:
        callback_name = callback_config.pop('name')
        
        # 从注册表中查找回调类
        if callback_name not in CALLBACKS_REGISTRY:
            raise ValueError(
                f"未知的回调: '{callback_name}'. "
                f"可用选项: {list(CALLBACKS_REGISTRY.keys())}"
            )
        
        callback_class = CALLBACKS_REGISTRY[callback_name]

        # 准备初始化参数
        # 允许传入额外的上下文参数 (例如 exp_manager)
        init_params = {**callback_config, **kwargs}
        
        # 动态创建回调实例
        try:
            callback_instance = callback_class(**init_params)
            callbacks.append(callback_instance)
        except TypeError as e:
            logging.error(f"创建回调 '{callback_name}' 失败。")
            logging.error(f"提供的参数: {init_params}")
            logging.error(f"原始错误: {e}")
            raise
            
    return callbacks