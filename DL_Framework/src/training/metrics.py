"""
评估指标模块
提供语义分割评估所需的各种指标计算函数
包括混淆矩阵、mIoU、准确率等
"""

import logging
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import torch


class ConfusionMatrix:
    """
    混淆矩阵类，支持增量计算和多类别统计
    """
    def __init__(self, num_classes: int, ignore_index: Optional[int] = None):
        """
        初始化混淆矩阵
        
        Args:
            num_classes: 类别数量
            ignore_index: 忽略的类别索引，通常为255或-1
        """
        self.num_classes = num_classes
        self.ignore_index = ignore_index
        self.mat = np.zeros((num_classes, num_classes), dtype=np.int64)
        self.logger = logging.getLogger(__name__)
        # 存储最近计算的指标
        self._last_metrics: Dict[str, Any] = {}
    
    def update(self, pred: torch.Tensor, target: torch.Tensor) -> None:
        """
        更新混淆矩阵
        
        Args:
            pred: 预测结果，形状为(N, H, W)
            target: 真实标签，形状为(N, H, W)
        """
        # 转换为numpy数组
        pred_np = pred.cpu().numpy()
        target_np = target.cpu().numpy()
        
        # 忽略指定的索引
        if self.ignore_index is not None:
            mask = (target_np != self.ignore_index)
            pred_np = pred_np[mask]
            target_np = target_np[mask]
        
        # 计算混淆矩阵
        indices = self.num_classes * target_np.flatten() + pred_np.flatten()
        m = np.bincount(indices.astype(np.int64), minlength=self.num_classes**2)
        m = m.reshape(self.num_classes, self.num_classes)
        
        self.mat += m
    
    def reset(self) -> None:
        """重置混淆矩阵"""
        self.mat = np.zeros((self.num_classes, self.num_classes), dtype=np.int64)
        self._last_metrics = {}
    
    def compute(self) -> Dict[str, Any]:
        """
        计算各项指标
        
        Returns:
            包含所有计算指标的字典
        """
        h = self.mat.astype(np.float64)
        
        # --- 核心指标计算 ---
        tp = np.diag(h)  # True Positives
        fp = h.sum(axis=0) - tp  # False Positives
        fn = h.sum(axis=1) - tp  # False Negatives
        
        # --- 计算每个类别的指标 ---
        iou = tp / (tp + fp + fn + 1e-10)
        precision = tp / (tp + fp + 1e-10)
        recall = tp / (tp + fn + 1e-10)
        f1 = 2 * precision * recall / (precision + recall + 1e-10)
        acc = tp / (h.sum(axis=1) + 1e-10) # 类别准确率
        
        # --- 计算总体和平均指标 ---
        acc_global = tp.sum() / (h.sum() + 1e-10)
        miou = np.nanmean(iou)
        macc = np.nanmean(acc)
        
        # --- 准备要返回的字典 ---
        self._last_metrics = {
            "miou": miou,
            "accuracy": acc_global,
            "mean_accuracy": macc,
            "iou_per_class": iou,
            "precision_per_class": precision,
            "recall_per_class": recall,
            "f1_per_class": f1,
            "acc_per_class": acc,
        }
        
        # 将每个类别的详细指标也添加到字典中，方便TensorBoard记录
        for i in range(self.num_classes):
            self._last_metrics[f'iou_class_{i}'] = iou[i]
            self._last_metrics[f'precision_class_{i}'] = precision[i]
            self._last_metrics[f'recall_class_{i}'] = recall[i]
            self._last_metrics[f'f1_class_{i}'] = f1[i]
            self._last_metrics[f'acc_class_{i}'] = acc[i]
            
        return self._last_metrics
    
    def print_metrics(self) -> None:
        """
        直接打印混淆矩阵和指标到日志，不返回任何值
        """
        # 确保有最新的指标
        if not self._last_metrics:
            self.compute()
        
        metrics = self._last_metrics
        
        # 打印基本指标
        self.logger.info(f"验证指标：mIoU = {metrics['miou']:.4f}, Accuracy = {metrics['accuracy']:.4f}")
        
        # 打印所有类别的IoU
        iou_per_class = metrics["iou_per_class"]
        iou_str = [f"{i}:{iou:.3f}" for i, iou in enumerate(iou_per_class)]
        self.logger.info(f"各类别IoU: {', '.join(iou_str)}")
        
        # 打印统计信息
        total_pixels = np.sum(self.mat)
        correct_pixels = np.sum(np.diag(self.mat))
        self.logger.info(f"类别统计信息:")
        self.logger.info(f"总像素数: {total_pixels:,}, 正确分类: {correct_pixels:,}, 全局准确率: {metrics['accuracy']*100:.2f}%")
        
        # 打印每个类别的详细统计
        self.logger.info(f"每个类别的详细统计:")
        
        header = f"| {'类别ID':^8} | {'样本数':^10} | {'正确数':^10} | {'精确率(%)':^10} | {'召回率(%)':^10} | {'F1分数(%)':^10} | {'IoU(%)':^8} |"
        separator = f"|{'-'*10}|{'-'*12}|{'-'*12}|{'-'*12}|{'-'*12}|{'-'*12}|{'-'*10}|"
        self.logger.info(header)
        self.logger.info(separator)
        
        for i in range(self.num_classes):
            row_sum = np.sum(self.mat[i, :])
            col_sum = np.sum(self.mat[:, i])
            correct = self.mat[i, i]
            
            precision = 100 * correct / col_sum if col_sum > 0 else 0
            recall = 100 * correct / row_sum if row_sum > 0 else 0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            iou = 100 * metrics["iou_per_class"][i]
            
            self.logger.info(f"| {i:^8d} | {row_sum:^10,d} | {correct:^10,d} | {precision:^10.2f} | {recall:^10.2f} | {f1:^10.2f} | {iou:^8.2f} |")
        
        self.logger.info(separator)
        
        # 找出主要混淆对
        confusion_pairs = []
        for i in range(self.num_classes):
            for j in range(self.num_classes):
                if i != j and self.mat[i, j] > 0:
                    percentage = 100 * self.mat[i, j] / np.sum(self.mat[i, :]) if np.sum(self.mat[i, :]) > 0 else 0
                    confusion_pairs.append((i, j, self.mat[i, j], percentage))
        
        confusion_pairs.sort(key=lambda x: x[2], reverse=True)
        
        self.logger.info("主要混淆对(前10个) - 真实->预测:")
        confusion_header = f"| {'序号':^4} | {'真实类别':^8} | {'预测类别':^8} | {'数量':^12} | {'百分比':^10} |"
        confusion_separator = f"|{'-'*6}|{'-'*10}|{'-'*10}|{'-'*14}|{'-'*12}|"
        
        self.logger.info(confusion_header)
        self.logger.info(confusion_separator)
        
        for idx, (i, j, count, percentage) in enumerate(confusion_pairs[:10]):
            self.logger.info(f"| {idx+1:^4d} | {i:^8d} | {j:^8d} | {count:^12,d} | {percentage:^10.2f}% |")
        
        self.logger.info(confusion_separator)
    
    def get_matrix(self) -> np.ndarray:
        """获取完整混淆矩阵"""
        return self.mat.copy()