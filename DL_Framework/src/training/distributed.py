"""
分布式训练工具
提供PyTorch分布式训练的初始化、同步等基础功能
"""

import logging
import os
from typing import Any, Dict, List, Optional, Tuple

import torch
import torch.distributed as dist
# 新增导入，将分布式相关的组件集中于此
import torch.nn as nn
from torch.utils.data import DistributedSampler, Sampler

from src.utils.env import log_environment_info


def is_dist_available_and_initialized() -> bool:
    """判断分布式环境是否可用且已初始化"""
    if not dist.is_available():
        return False
    if not dist.is_initialized():
        return False
    return True


def get_world_size() -> int:
    """获取分布式环境中的进程总数"""
    if not is_dist_available_and_initialized():
        return 1
    return dist.get_world_size()


def get_rank() -> int:
    """获取当前进程的全局排名"""
    if not is_dist_available_and_initialized():
        return 0
    return dist.get_rank()


def is_main_process() -> bool:
    """判断当前进程是否为主进程"""
    return get_rank() == 0


def init_distributed_mode(master_port: int = 12355, backend: str = "nccl") -> None:
    """
    初始化训练环境。
    - 首先由主进程打印详细的环境信息快照。
    - 之后，如果检测到分布式环境变量，则初始化进程组。
    
    Args:
        master_port: 主进程通信端口
        backend: 通信后端，GPU通常使用"nccl"，CPU使用"gloo"
    """
    logger = logging.getLogger(__name__)

    # --- 1. 环境信息打印 ---
    # 检查是否为分布式模式，并仅让主进程打印信息
    is_distributed = 'RANK' in os.environ and 'WORLD_SIZE' in os.environ
    if not is_distributed or int(os.environ.get('RANK', '0')) == 0:
        # log_environment_info() # 已移动到主训练脚本 train.py 中，此处不再调用
        pass

    # --- 2. 分布式初始化 ---
    if not is_distributed:
        logger.info("检测到单进程模式，跳过分布式初始化。")
        return
    
    # --- 仅在分布式模式下执行以下代码 ---
    
    rank = int(os.environ["RANK"])
    world_size = int(os.environ['WORLD_SIZE'])
    local_rank = int(os.environ['LOCAL_RANK'])
    
    os.environ['MASTER_PORT'] = str(master_port)
    
    dist.init_process_group(
        backend=backend,
        init_method='env://'
    )
    
    torch.cuda.set_device(local_rank)
    dist.barrier()
    
    if rank == 0:
        logger.info("="*50)
        logger.info("分布式训练初始化完成:")
        logger.info(f"  - 进程总数 (World Size): {world_size}")
        logger.info(f"  - 通信后端 (Backend): {backend}")
        logger.info(f"  - 主机地址 (Master Addr): {os.environ.get('MASTER_ADDR', 'N/A')}")
        logger.info(f"  - 主机端口 (Master Port): {os.environ.get('MASTER_PORT', 'N/A')}")
        logger.info("="*50)


def setup_for_distributed(is_master: bool) -> None:
    """
    此函数仅在主进程上设置日志记录，以防止来自所有进程的重复日志记录。
    """
    # 在非分布式模式或主进程上正常设置即可
    pass


def cleanup() -> None:
    """在训练结束时清理分布式环境"""
    if is_dist_available_and_initialized():
        dist.destroy_process_group()
        logger = logging.getLogger(__name__)
        logger.info("分布式训练环境已清理")


def reduce_dict(input_dict: Dict[str, torch.Tensor], average: bool = True) -> Dict[str, torch.Tensor]:
    """
    将所有进程中的字典值进行规约（默认为平均）。
    
    Args:
        input_dict (dict): 包含需要规约的张量的字典。
        average (bool): 是否对规约结果取平均。
        
    Returns:
        dict: 包含规约后结果的字典。
    """
    if not is_dist_available_and_initialized():
        return input_dict

    world_size = get_world_size()
    if world_size < 2:
        return input_dict

    with torch.no_grad():
        names = []
        values = []
        # sort the keys so that they are consistent across processes
        for k in sorted(input_dict.keys()):
            names.append(k)
            values.append(input_dict[k])
        
        values = torch.stack(values, dim=0)
        dist.all_reduce(values)
        
        if average:
            values /= world_size
            
        result = {k: v for k, v in zip(names, values)}
        
        return result


def all_gather(tensor: torch.Tensor) -> List[torch.Tensor]:
    """将所有进程的张量收集到一个列表中"""
    if not is_dist_available_and_initialized():
        return [tensor]
        
    world_size = get_world_size()
    if world_size < 2:
        return [tensor]
        
    tensor_list = [torch.zeros_like(tensor) for _ in range(world_size)]
    dist.all_gather(tensor_list, tensor)
    
    return tensor_list


def synchronize() -> None:
    """在所有进程之间设置一个同步屏障"""
    if not is_dist_available_and_initialized():
        return
    
    dist.barrier()


# --- 新增的分布式封装函数 ---

def create_distributed_sampler(dataset: torch.utils.data.Dataset, shuffle: bool = True) -> Optional[DistributedSampler]:
    """如果处于分布式环境，则创建 DistributedSampler，否则返回 None"""
    if is_dist_available_and_initialized():
        return DistributedSampler(dataset, shuffle=shuffle)
    return None

def wrap_model_for_distributed(model: nn.Module) -> nn.Module:
    """如果处于分布式环境，则用DDP封装模型，否则返回原模型"""
    if is_dist_available_and_initialized():
        # DDP的导入放在函数内部，避免在单卡模式下不必要的导入开销
        from torch.nn.parallel import DistributedDataParallel as DDP
        local_rank = get_rank() # 直接使用 get_rank() 即可，它在非分布式时返回0
        wrapped_model = DDP(model, device_ids=[local_rank], find_unused_parameters=False)
        return wrapped_model
    return model

def set_sampler_epoch(sampler: Optional[Sampler], epoch: int) -> None:
    """如果 sampler 是 DistributedSampler，则设置其 epoch"""
    if isinstance(sampler, DistributedSampler):
        sampler.set_epoch(epoch) 