"""
重构后的 Trainer 模块
"""
from typing import Any, List, Optional

import torch
import torch.amp as amp
import torch.nn as nn
from torch.utils.data import DataLoader

from .callbacks import Callback, CallbackList
from .distributed import (is_main_process, set_sampler_epoch,
                          wrap_model_for_distributed)


class Trainer:
    """
    一个简洁、基于回调的训练器。

    该训练器对具体功能（如日志记录、检查点保存、学习率调度）一无所知。
    所有这些功能都通过回调（Callbacks）系统实现。
    """
    def __init__(
        self,
        model: nn.Module,
        optimizer: torch.optim.Optimizer,
        criterion: nn.Module,
        device: torch.device,
        scheduler: Optional[torch.optim.lr_scheduler.LRScheduler] = None,
        callbacks: Optional[List[Callback]] = None,
        use_amp: bool = False,
        exp_manager: Optional['ExperimentManager'] = None,
    ):
        self.model = model
        self.optimizer = optimizer
        self.criterion = criterion
        self.device = device
        self.scheduler = scheduler
        self.use_amp = use_amp
        self.is_master = is_main_process()
        
        # 初始化回调
        self.callbacks = CallbackList(callbacks)
        self.callbacks.set_trainer(self)
        
        # 使用新的统一接口来封装模型，它会自动处理单/多卡情况
        self.model = wrap_model_for_distributed(self.model)

        # 混合精度设置
        if self.use_amp:
            self.scaler = amp.grad_scaler.GradScaler()

        # 训练过程中的状态变量
        self.epoch = 0
        self.global_step = 0
        self.logs = {}

        self.exp_manager = exp_manager

    def fit(self, train_loader: DataLoader, val_loader: Optional[DataLoader] = None, epochs: int = 1):
        """
        训练主循环

        Args:
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            epochs: 训练周期数
        """
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.epochs = epochs
        
        self.callbacks.on_train_begin(logs={'epochs': epochs})

        for epoch in range(1, epochs + 1):
            self.epoch = epoch
            
            # 使用新的统一接口来设置sampler的epoch，它会自动处理单/多卡情况
            set_sampler_epoch(train_loader.sampler, epoch)
            # 对验证集也应用此逻辑（尽管通常非必须，但保持代码一致性）
            if val_loader:
                set_sampler_epoch(val_loader.sampler, epoch)

            self.callbacks.on_epoch_begin(epoch)
            
            train_logs = self._train_epoch(train_loader)
            self.logs.update(train_logs)
            
            if val_loader:
                val_logs = self._validate_epoch(val_loader)
                self.logs.update(val_logs)

            self.callbacks.on_epoch_end(epoch, logs=self.logs)
            
            # 检查是否有回调请求停止训练 (例如早停)
            if getattr(self, 'stop_training', False):
                break

        self.callbacks.on_train_end(logs=self.logs)

    def _train_epoch(self, data_loader: DataLoader) -> dict:
        self.model.train()
        total_loss = 0.0
        
        for batch_idx, batch in enumerate(data_loader):
            self.callbacks.on_batch_begin(batch_idx)

            loss = self._run_one_batch(batch, is_train=True)
            total_loss += loss

            batch_logs = {'loss': loss}
            self.callbacks.on_batch_end(batch_idx, logs=batch_logs)
            self.global_step += 1

        avg_loss = total_loss / len(data_loader)
        return {'train_loss': avg_loss}

    def _validate_epoch(self, data_loader: DataLoader) -> dict:
        self.model.eval()
        total_loss = 0.0
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(data_loader):
                loss = self._run_one_batch(batch, is_train=False)
                total_loss += loss
                
                batch_logs = {'val_loss': loss}
                self.callbacks.on_validation_batch_end(batch_idx, logs=batch_logs)
                
        avg_loss = total_loss / len(data_loader)
        
        # 准备传递给 on_validation_end 和 on_epoch_end 的日志
        val_logs = {'val_loss': avg_loss}
        
        # 关键补充：在所有验证批次结束后，但在 on_epoch_end 之前，
        # 调用新事件，让 MetricsCallback 有机会先计算完所有指标。
        self.callbacks.on_validation_end(logs=val_logs)

        return val_logs
        
    def _run_one_batch(self, batch: Any, is_train: bool) -> float:
        # 1. 解析批次数据并移动到设备
        if isinstance(batch, dict):
            images = batch['image'].to(self.device, non_blocking=True)
            targets = batch['mask'].to(self.device, non_blocking=True)
        else:
            images, targets = batch
            images = images.to(self.device, non_blocking=True)
            targets = targets.to(self.device, non_blocking=True)

        # 2. 前向传播
        if self.use_amp:
            with amp.autocast_mode.autocast(device_type=self.device.type):
                outputs = self.model(images)
                loss = self.criterion(outputs, targets)
        else:
            outputs = self.model(images)
            loss = self.criterion(outputs, targets)

        # 3. 如果是训练，则执行反向传播和优化
        if is_train:
            self.optimizer.zero_grad()
            if self.use_amp:
                self.scaler.scale(loss).backward()
                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                loss.backward()
                self.optimizer.step()

        # 将 self.model 和 self.criterion 存入 self.logs，以便回调函数访问
        self.logs['model'] = self.model
        self.logs['criterion'] = self.criterion
        
        # 将 outputs 和 targets 存入 self.logs，以便回调函数访问
        self.logs['outputs'] = outputs
        self.logs['targets'] = targets
        
        return loss.item() 