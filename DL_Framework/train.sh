#!/bin/bash

# 训练启动脚本
# 最终修正版v2：通过设置PYTHONPATH来解决模块导入问题

# 如果任何命令失败，则立即退出脚本
set -e

# 检查是否提供了配置文件作为参数
if [ -z "$1" ]; then
    echo "错误: 请提供配置文件的路径作为第一个参数。"
    echo "用法: ./train.sh <path_to_config.yaml>"
    exit 1
fi

# 从第一个参数获取配置文件路径
CONFIG_FILE="$1"

# 关键步骤：确保当前工作目录是脚本所在的目录（即 DL_Framework 根目录）
cd "$(dirname "$0")"

# 关键步骤2：将当前目录（项目根目录）添加到PYTHONPATH。
# 这会告诉Python在查找模块时，也要搜索这个目录。
# 这样，`from src...` 这样的导入就能被正确解析。
export PYTHONPATH=$(pwd):$PYTHONPATH

echo "======================================================"
echo "项目根目录: $(pwd)"
echo "PYTHONPATH 已临时设置为: $PYTHONPATH"
echo "准备启动训练..."
echo "配置文件: ${CONFIG_FILE}"
echo "======================================================"
python scripts/train.py --config "${CONFIG_FILE}" 