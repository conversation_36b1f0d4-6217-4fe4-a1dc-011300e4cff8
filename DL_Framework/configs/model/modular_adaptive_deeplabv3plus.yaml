# ModularAdaptiveDeepLabV3Plus模型配置
# 模块化自适应DeepLabV3+语义分割模型配置文件

# 基础模型配置
type: "modular_adaptive_deeplabv3plus"
backbone: "resnet50"
pretrained: true

# 插件配置 - 所有插件默认启用，可以单独开关
plugins:
  # 色彩增强插件配置
  color_enhancement:
    enabled: true
    residual: true
    use_multi_colorspace: true  # 是否使用多颜色空间处理
    
  # 边缘增强插件配置  
  edge_enhancement:
    enabled: true
    residual: true
    
  # 注意力增强插件配置
  attention_enhancement:
    enabled: true
    residual: true
    reduction: 16
    
  # 自适应直方图均衡化插件配置
  histogram_equalization:
    enabled: true
    residual: true
    num_bins: 256
    
  # 色彩敏感通道注意力插件配置
  color_sensitive_attention:
    enabled: true
    residual: true
    reduction: 16
    
  # 类别不平衡处理插件配置
  class_balance:
    enabled: true
    residual: true
    
  # 地貌先验知识插件配置
  geomorphology_prior:
    enabled: true

# 训练策略配置
training:
  # 渐进式插件启用策略
  progressive_plugin_enable: false  # 是否使用渐进式插件启用
  plugin_warmup_epochs: 10          # 插件预热轮数
  
  # 插件权重衰减
  plugin_weight_decay: 0.0001
  
  # 特征融合权重
  base_weight: 0.7      # 基础模型权重
  enhanced_weight: 0.3  # 增强模型权重

# 模型复杂度控制
complexity:
  max_parameter_increase_ratio: 50  # 最大参数增长比例(%)
  enable_gradient_checkpointing: false  # 是否启用梯度检查点节省内存 