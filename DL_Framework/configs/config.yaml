# 绥德县遥感影像语义分割项目主配置文件
# 这是整个项目的主配置文件，用于指定其他配置文件路径和全局设置

# 实验可复现性配置
seed: 42  # 全局随机种子, null 表示不设置

# 数据集配置
dataset:
  name: suide_v2.1 # 使用configs/dataset/suide_v2.1.yaml的配置
  num_classes: 14 # 更新为SuiDe数据集的类别数量

# 模型配置
model:
  name: deeplabv3plus # 改回成功实验使用的标准DeepLabV3+
  in_channels: 3
  num_classes: ${dataset.num_classes} # 引用数据集的类别数，确保唯一真实来源

# 优化器配置
optimizer:
  name: adamw # 使用configs/optimizer/adamw.yaml的配置


# 学习率调度器配置
scheduler:
  name: cosine # 使用configs/scheduler/cosine.yaml的配置

# 损失函数配置
loss:
  name: combined_ce_dice_loss # 使用configs/loss/combined_ce_dice_loss.yaml的配置
  ignore_index: 255
  num_classes: ${model.num_classes}

# 训练参数
train:
  name: train # 使用configs/train/train.yaml的配置

# 实验配置 - 指定实验保存路径
experiment_dir: "./experiments" # 实验数据保存的根目录

# 日志级别
logging:
  level: "INFO" # 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL

# ===================================================================
# 回调函数配置
# ===================================================================
callbacks:
  checkpoint:
    enabled: true
    monitor: "miou"
    mode: "max"
    save_freq: 1
    max_kept: 2
    
  early_stopping:
    enabled: false
    monitor: "miou"
    mode: "max"
    patience: 10
    min_delta: 0.0001
    
  tensorboard:
    enabled: true

