# SuiDe Dataset 预测配置

# 测试配置文件
# 用于配置测试/评估过程中的参数

# 模型配置文件路径
model_config_path: "configs/config.yaml"

# 模型权重文件路径（请替换为实际训练好的模型路径）
model_path: "outputs/models/best_model.pth"

# 输出目录
output_dir: "outputs/test_predictions"

# 测试基本参数
test:
  batch_size: 8  # 测试时的批次大小
  num_workers: 4  # 数据加载线程数
  save_predictions: true  # 是否保存预测结果
  ensemble: false  # 是否使用模型集成
  tta: false  # 是否使用测试时增强
  metrics: ["miou", "dice", "accuracy", "f1"]  # 评估指标列表

# 可视化配置
visualization:
  enabled: true  # 是否启用可视化
  colormap_file: "metadata/colormap.json"  # 类别颜色映射文件路径
  save_masks: true  # 是否保存单独的掩码图
  save_overlay: true  # 是否保存叠加效果图
  save_side_by_side: true  # 是否保存原图-标注-预测的并排效果图
  confidence_map: true  # 是否生成置信度热图
  confusion_matrix: true  # 是否生成混淆矩阵
  save_legend: true  # 是否生成图例

# 结果导出配置
export:
  format: ["png", "geotiff"]  # 结果保存格式
  class_probabilities: false  # 是否保存每个类别的概率图
  compress: true  # 是否压缩输出文件

# 测试时增强配置
test_augmentation:
  enabled: false  # 是否启用测试时增强
  flips: true  # 是否使用翻转增强
  scales: [0.75, 1.0, 1.25]  # 多尺度测试的缩放因子
  rotation: false  # 是否使用旋转增强
  merge_method: "mean"  # 合并增强结果的方法: "mean", "max", "weighted"

# 推理配置
inference:
  overlap: 64  # 图像块重叠像素数
  use_sliding_window: true  # 是否使用滑动窗口推理
  crop_size: 512  # 推理时的裁剪尺寸
  batch_inference: true  # 是否批量推理
  cuda_benchmark: true  # 是否启用CUDA性能基准测试 