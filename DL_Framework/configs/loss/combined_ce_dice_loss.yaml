# 交叉熵+Dice组合损失（推荐：边界精度）
type: "combined"

# 全局类别权重配置 (对支持的子损失生效)
class_weights:
  enabled: true # 是否启用类别权重
  method: "inverse" # 计算方法
  update_freq: 1 # 多少个epoch更新一次
  smoothing: 0.8 # 平滑系数(0-1)
  initial: null # 初始权重

# --- 全局损失函数参数 (作为默认值或模板) ---
reduction: 'mean'
label_smoothing: 0.0  # 全局默认不使用标签平滑
smooth: 1.0           # Dice Loss 平滑系数
square: false         # Dice Loss 是否平方
per_class: false      # Dice Loss 是否按类计算

# --- 子损失函数定义 ---
# 适用场景：改善边界精度，适用于大多数分割任务
components:
  ce:
    type: "ce"
    combination_weight: 0.5
    # CE的局部配置: 覆盖全局的 label_smoothing
    label_smoothing: 0.1

  dice:
    type: "dice"
    combination_weight: 0.5
    # Dice没有局部配置，将完全使用全局参数
