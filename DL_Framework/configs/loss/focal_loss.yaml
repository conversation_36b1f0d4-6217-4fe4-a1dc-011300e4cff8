# Focal Loss (处理类别不平衡和难易样本问题)
type: "focal"

# --- 基本参数 ---
reduction: "mean"  # 聚合方式: "mean", "sum", "none"
alpha: 0.25        # 类别平衡因子, 用于平衡正负样本的重要性
gamma: 2.0         # 聚焦参数, 用于让模型更关注难分的样本

# --- 类别权重配置 ---
# 用于处理类别不平衡问题, 与 alpha 参数作用类似但更灵活
class_weights:
  enabled: false           # 是否启用动态类别权重
  method: "inverse"        # 计算方法: "inverse", "effective_samples"
  update_freq: 1           # 权重更新频率 (epochs)
  smoothing: 0.8           # EMA平滑系数 (0-1), 越大历史权重占比越高
  initial: null            # 初始权重, null表示自动计算, 也可以提供一个列表