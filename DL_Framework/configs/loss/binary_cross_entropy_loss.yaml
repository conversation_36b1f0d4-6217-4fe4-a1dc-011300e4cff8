# 二元交叉熵损失 (Binary Cross-Entropy Loss)
# 适用于二分类或多标签分类任务
type: "bce"

# --- 基本参数 ---
reduction: "mean"          # 聚合方式: "mean", "sum", "none"
label_smoothing: 0.0       # 标签平滑系数, 推荐范围 0.0-0.1
pos_weight: null           # 正样本权重, 一个浮点数或列表, 用于处理正负样本不平衡

# --- 类别权重配置 ---
# 注意: 在二分类中, 作用与 pos_weight 类似, 通常只使用其中一种
class_weights:
  enabled: false           # 是否启用动态类别权重
  method: "inverse"        # 计算方法: "inverse", "effective_samples"
  update_freq: 1           # 权重更新频率 (epochs)
  smoothing: 0.8           # EMA平滑系数 (0-1), 越大历史权重占比越高
  initial: null            # 初始权重, null表示自动计算, 也可以提供一个列表