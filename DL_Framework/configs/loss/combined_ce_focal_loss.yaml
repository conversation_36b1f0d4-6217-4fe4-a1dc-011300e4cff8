# 交叉熵+Focal组合损失 (推荐：类别不平衡场景)
type: "combined"

# 全局类别权重配置 (对支持的子损失生效)
class_weights:
  enabled: true
  method: "inverse"
  update_freq: 1
  smoothing: 0.8
  initial: null

# --- 全局损失函数参数 (作为默认值或模板) ---
reduction: 'mean'
label_smoothing: 0.0  # 全局默认不使用标签平滑
alpha: 0.25           # Focal Loss 全局 alpha
gamma: 2.0            # Focal Loss 全局 gamma

# --- 子损失函数定义 ---
# 适用场景: 改善类别不平衡问题, 增强对困难样本的分割精度
components:
  ce:
    type: "ce"
    combination_weight: 0.5
    # CE的局部配置: 覆盖全局的 label_smoothing
    label_smoothing: 0.1

  focal:
    type: "focal"
    combination_weight: 0.5
    # Focal Loss没有局部配置, 将完全使用全局参数