# 交叉熵+Lovasz组合损失 (推荐：IoU优化)
type: "combined"

# 全局类别权重配置 (仅对CE Loss生效)
class_weights:
  enabled: true
  method: "inverse"
  update_freq: 1
  smoothing: 0.8
  initial: null

# --- 全局损失函数参数 (作为默认值或模板) ---
reduction: 'mean'
label_smoothing: 0.0  # 全局默认不使用标签平滑
per_image: true       # Lovasz Loss 是否按图像计算

# --- 子损失函数定义 ---
# 适用场景: 直接优化IoU指标, 提高分割掩码质量
components:
  ce:
    type: "ce"
    combination_weight: 0.7
    # CE的局部配置: 覆盖全局的 label_smoothing
    label_smoothing: 0.1

  lovasz:
    type: "lovasz"
    combination_weight: 0.3
    # Lovasz Loss没有局部配置, 将完全使用全局参数