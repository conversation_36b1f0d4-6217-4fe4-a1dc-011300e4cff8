# Dice+Focal组合损失 (推荐：边界精度+类别均衡)
type: "combined"

# 全局类别权重配置 (仅对Focal Loss生效)
class_weights:
  enabled: true
  method: "inverse"
  update_freq: 1
  smoothing: 0.8
  initial: null

# --- 全局损失函数参数 (作为默认值或模板) ---
reduction: 'mean'
# Dice Loss 参数
smooth: 1.0
square: false
per_class: false
# Focal Loss 参数
alpha: 0.25
gamma: 2.0

# --- 子损失函数定义 ---
# 适用场景: 同时优化边界精度和类别平衡问题
components:
  dice:
    type: "dice"
    combination_weight: 0.5
    # Dice Loss没有局部配置, 将完全使用全局参数

  focal:
    type: "focal"
    combination_weight: 0.5
    # Focal Loss没有局部配置, 将完全使用全局参数