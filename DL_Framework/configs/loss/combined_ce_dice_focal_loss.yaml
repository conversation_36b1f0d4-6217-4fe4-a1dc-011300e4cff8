# 交叉熵+<PERSON>ce+Focal组合损失 (推荐：复杂场景综合优化)
type: "combined"

# 全局类别权重配置 (对CE和Focal Loss生效)
class_weights:
  enabled: true
  method: "inverse"
  update_freq: 1
  smoothing: 0.8
  initial: null

# --- 全局损失函数参数 (作为默认值或模板) ---
reduction: 'mean'
# CE Loss 参数
label_smoothing: 0.0
# Dice Loss 参数
smooth: 1.0
square: false
per_class: false
# Focal Loss 参数
alpha: 0.25
gamma: 2.0

# --- 子损失函数定义 ---
# 适用场景: 综合优化像素精度、边界精度和类别平衡
components:
  ce:
    type: "ce"
    combination_weight: 0.4 # 加大CE权重
    # CE的局部配置: 覆盖全局的 label_smoothing
    label_smoothing: 0.1

  dice:
    type: "dice"
    combination_weight: 0.3
    # Dice Loss没有局部配置, 将完全使用全局参数

  focal:
    type: "focal"
    combination_weight: 0.3
    # Focal Loss没有局部配置, 将完全使用全局参数