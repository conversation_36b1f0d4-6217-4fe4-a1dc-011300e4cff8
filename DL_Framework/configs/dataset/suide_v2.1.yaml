# SuiDe_v2.1数据集配置文件
# 配置绥德县高分辨率遥感影像语义分割数据集 (v2.1)

# 数据集基本信息
name: "SuiDe_v2.1"  # 数据集名称
type: "SuiDeDatasetV2"  # 数据集类型，对应 factory.py 中的新键
root_dir: "../Data_SRC/Dataset_v2.1"  # 数据集根目录

# 以下为数据集结构信息，新版脚本会自动推断，此处仅为方便查阅
images_dir: "images"  # 图像目录 (自动推断)
masks_dir: "masks"  # 掩码图目录 (自动推断)
metadata_dir: "metadata" # 元数据目录 (自动推断)
train_annotation: "annotations/train.json"  # 训练集标注 (自动推断)
val_annotation: "annotations/val.json"  # 验证集标注 (自动推断)
test_annotation: "annotations/test.json"  # 测试集标注 (自动推断)
num_classes: 14  # 类别数量 (自动从class_info.json计算)

# 训练配置
training_level: 2  # 训练级别: 1 for level-1 classes, 2 for level-2 classes
ignore_index: 255  # 忽略的标签索引
sampling_strategy: "dynamic"  # 多尺度采样策略: "dynamic"(动态随机) 或 "fixed"(固定比例) 

# 多尺度配置
scales:
  use_multiscale: true  # 是否使用多尺度
  scale_weights:  # 各尺度权重
    "1_1": 0.7  # 原始尺寸
    "1_2": 0.2  # 高度2倍
    "1_0.5": 0.1  # 高度0.5倍

# 预处理参数
preprocessing:
  tile_size: 512  # 图像块大小
  overlap: 128  # 重叠像素数
  mean: [0.485, 0.456, 0.406]  # 归一化均值
  std: [0.229, 0.224, 0.225]  # 归一化标准差

# 数据增强策略
augmentation:
  random_crop: true  # 随机裁剪
  random_rotate90: true  # 随机90度旋转
  flip: true  # 随机翻转
  transpose: false # 随机转置
  random_brightness_contrast: true  # 亮度和对比度调整
  brightness_limit: 0.2  # 亮度变化范围
  contrast_limit: 0.2  # 对比度变化范围
  # gauss_noise: false  # 高斯噪声 (代码中已注释)
  
  # 弹性变换参数 (代码中已注释)
  # elastic_transform: false  # 弹性变换
  # elastic_alpha: 1.0  # 弹性变换alpha参数
  # elastic_sigma: 50.0  # 弹性变换sigma参数
  
  # 网格扭曲参数 (代码中已注释)
  # grid_distortion: false  # 网格扭曲
  # grid_num_steps: 5  # 网格扭曲步数
  # grid_distort_limit: 0.3  # 网格扭曲限制
  
  # 光学扭曲参数 (代码中已注释)
  # optical_distortion: false  # 光学扭曲
  # optical_distort_limit: 0.05  # 光学扭曲限制
  
  # Cutout参数
  cutout: false  # 是否使用Cutout
  cutout_max_size: 0.1  # Cutout最大尺寸，相对于图像尺寸的比例
  
  # HSV颜色空间增强 (代码中已注释)
  # hsv_shift: true  # HSV颜色空间增强
  # hue_shift_limit: 20  # 色相偏移范围
  # sat_shift_limit: 30  # 饱和度偏移范围
  # val_shift_limit: 20  # 明度偏移范围
  
  # clahe: true  # 对比度受限自适应直方图均衡化 (代码中已注释) 