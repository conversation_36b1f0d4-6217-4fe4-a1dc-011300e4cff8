#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
DL_Framework 主训练脚本
"""
import argparse
import logging

import torch
from src.data.factory import create_data_loaders
from src.losses.factory import get_loss_fn as create_loss
from src.models.factory import create_model
from src.training.callbacks import (CallbackList, CheckpointCallback,
                                    EarlyStoppingCallback, LRSchedulerCallback,
                                    MetricsCallback, RichProgressCallback,
                                    TensorBoardCallback)
from src.training.distributed import init_distributed_mode
from src.training.trainer import Trainer
from src.utils.config import Config
from src.utils.env import set_seed, log_environment_info
from src.utils.experiment import ExperimentManager
from src.logging.log_config import setup_logging
from src.utils.optimizer import OptimizerFactory
from src.utils.scheduler import SchedulerFactory


def main(args):
    """主训练函数"""
    # --- 1. 初始化实验管理器 ---
    # 它将负责创建目录、保存配置，并提供所有路径
    config = Config(args.config)
    exp_manager = ExperimentManager(config)

    # --- 2. 配置日志 ---
    # 日志将保存到由管理器指定的实验目录中
    setup_logging(log_level="INFO", log_file=exp_manager.get_log_file_path())
    logging.info(f"实验 '{exp_manager.version}' 开始, 目录: {exp_manager.experiment_dir}")

    # 记录环境信息
    log_environment_info()

    # --- 3. 设置环境 ---
    # 设置随机种子以确保实验可复现性
    seed = config.get('train.seed')
    if seed is not None:
        set_seed(seed)
    else:
        logging.warning("配置文件中未指定 'train.seed'，本次训练将不可复现！")
    
    init_distributed_mode()
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # --- 4. 创建核心组件 ---
    logging.info("开始创建模型、数据加载器和训练组件...")
    model = create_model(config).to(device)
    dataloaders = create_data_loaders(config)
    optimizer = OptimizerFactory(model, config).create()
    criterion = create_loss(config).to(device)
    scheduler = SchedulerFactory(optimizer, config).create()
    
    # --- 5. 创建回调 ---
    callbacks = []
    
    if scheduler:
        callbacks.append(LRSchedulerCallback(scheduler))
        
    if config.get('model.num_classes') is not None:
        callbacks.append(MetricsCallback(
            num_classes=config.get('model.num_classes'),
            ignore_index=config.get('loss.ignore_index')
        ))

    callbacks.append(RichProgressCallback())

    # 检查点回调 - 现在依赖于 ExperimentManager
    if config.get('callbacks.checkpoint.enabled'):
        callbacks.append(CheckpointCallback(
            exp_manager=exp_manager,
            monitor=config.get('callbacks.checkpoint.monitor'),
            mode=config.get('callbacks.checkpoint.mode'),
            save_freq=config.get('callbacks.checkpoint.save_freq'),
            max_kept=config.get('callbacks.checkpoint.max_kept')
        ))

    # 早停回调
    if config.get('callbacks.early_stopping.enabled'):
        callbacks.append(EarlyStoppingCallback(
            monitor=config.get('callbacks.early_stopping.monitor'),
            mode=config.get('callbacks.early_stopping.mode'),
            patience=config.get('callbacks.early_stopping.patience'),
            min_delta=config.get('callbacks.early_stopping.min_delta')
        ))
        
    # TensorBoard 回调 - 从 ExperimentManager 获取路径
    if config.get('callbacks.tensorboard.enabled'):
        callbacks.append(TensorBoardCallback())

    logging.info(f"成功加载 {len(callbacks)} 个回调。")

    # --- 6. 初始化 Trainer ---
    logging.info("初始化 Trainer...")
    trainer = Trainer(
        model=model,
        optimizer=optimizer,
        scheduler=scheduler,
        criterion=criterion,
        device=device,
        callbacks=callbacks,
        use_amp=config.get('train.use_amp'),
        exp_manager=exp_manager
    )
    
    # --- 7. 启动训练 ---
    epochs = config.get('train.epochs')
    if epochs is None:
        raise ValueError("'train.epochs' 必须在配置中明确指定。")

    logging.info(f"开始训练，共 {epochs} 个周期...")
    trainer.fit(
        train_loader=dataloaders['train'],
        val_loader=dataloaders.get('val'),
        epochs=epochs
    )

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="DL_Framework Training Script")
    parser.add_argument('--config', type=str, required=True, help="Path to the configuration file.")
    args = parser.parse_args()
    main(args)