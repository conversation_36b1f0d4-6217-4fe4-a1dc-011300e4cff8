# =============================================================================
# Ray Tune 超参数优化 (HPO) 基础配置
# =============================================================================
# 这是一个独立的HPO配置文件，用于Ray Tune超参数搜索
# 特点：
# - 避免与主配置系统的嵌套冲突
# - 提供完整的训练配置
# - 支持多种搜索算法和调度器
# - 集成WandB进行实验跟踪
#
# 适用场景：
# - 自动超参数搜索
# - 模型性能优化
# - 大规模实验管理
# - 资源高效的参数调优
#
# 使用方式：
#   python scripts/hpo_search.py hpo=tune_basic
#   python scripts/hpo_search.py hpo=tune_basic hpo.run_params.num_samples=10
# =============================================================================

# =============================================================================
# 项目基础配置
# =============================================================================
project_name: "SuiDe_RemoteSensing"          # 项目名称
experiment_name: "baseline"                  # 实验名称
run_name: "${experiment_name}_${now:%Y-%m-%d_%H-%M-%S}"
                                             # 运行名称，包含时间戳

# =============================================================================
# WandB 实验跟踪配置
# =============================================================================
wandb:
  project: ${project_name}                   # WandB项目名称
  name: ${run_name}                          # 运行名称
  tags: ["baseline", "unet"]                 # 实验标签
  notes: "A baseline run for segmentation."  # 实验描述

# =============================================================================
# Logger 配置 (tune.py脚本需要)
# =============================================================================
logger:
  project: ${project_name}                   # WandB项目名称
  tags: ["hpo", "raytune", "unet"]          # HPO实验标签
  notes: "Ray Tune hyperparameter optimization"

# =============================================================================
# PyTorch Lightning 训练器配置
# =============================================================================
trainer:
  _target_: lightning.pytorch.Trainer

  accelerator: auto                          # 自动检测加速器 (GPU/CPU)
  devices: auto                              # 自动检测设备数量
  precision: 16-mixed                        # 混合精度训练
  max_epochs: 5                              # 最大训练轮数
  check_val_every_n_epoch: 1                 # 验证频率
  log_every_n_steps: 10                      # 日志记录频率
  enable_progress_bar: true                  # 启用进度条

# 模型配置
model:
  _target_: src.modules.segmentation_module.SegmentationModule
  optimizer_cfg: ${optimizer}
  scheduler_cfg: ${scheduler}
  loss_cfg: ${loss}
  num_classes: ${data.dataset_config.num_classes}
  architecture:
    _target_: src.models.architectures.unet.UNet
    n_channels: 3
    n_classes: ${data.dataset_config.num_classes}
    bilinear: true

# 数据配置
data:
  _target_: src.data.suide_datamodule.SuiDeDataModule

  # 数据集核心配置
  dataset_config:
    data_root: ${oc.env:DATA_DIR, /home/<USER>/DeepLearing/SuiDe_Project/Dataset_v2.2}
    training_level: 2
    num_classes: 14
    ignore_index: 255
    auto_detect_classes: false

    # 多尺度数据配置
    training_scales:
      "1:1": 0.7
      "1:2": 0.2
      "1:0.5": 0.1

    validation_scales: ["1:1"]
    test_scales: ["1:1"]

    # 数据增强配置
    augmentation:
      enabled: true
      horizontal_flip: 0.5
      vertical_flip: 0.5
      rotation: 0.3
      brightness: 0.2
      contrast: 0.2
      saturation: 0.2
      hue: 0.1

  # 数据加载器配置
  dataloader_config:
    batch_size: 16
    val_batch_size: 32
    test_batch_size: 32
    num_workers: 4
    pin_memory: true
    persistent_workers: true
    prefetch_factor: 2

# =============================================================================
# 优化器配置
# =============================================================================
optimizer:
  _target_: torch.optim.AdamW
  lr: 1e-4                                   # 默认学习率，会被HPO搜索覆盖
  weight_decay: 1e-4
  betas: [0.9, 0.999]
  eps: 1e-8

# =============================================================================
# 学习率调度器配置
# =============================================================================
scheduler:
  _target_: torch.optim.lr_scheduler.CosineAnnealingLR
  T_max: 50                                  # 余弦退火周期
  eta_min: 1e-6                              # 最小学习率

# =============================================================================
# 损失函数配置
# =============================================================================
loss:
  _target_: src.losses.dice_loss.CEDiceLoss
  ce_weight: 0.5
  dice_weight: 0.5
  class_weights: null


  name: ${wandb.name}
  save_dir: ${get_run_dir:}
  log_model: false
  tags: ${wandb.tags}
  notes: ${wandb.notes}

# 回调函数配置
callbacks:
  model_checkpoint:
    _target_: lightning.pytorch.callbacks.ModelCheckpoint
    dirpath: ${path_join:${get_run_dir:}, "checkpoints"}
    filename: "epoch_{epoch:03d}-iou_{val/iou:.4f}"
    monitor: "val/iou"
    mode: "max"
    save_top_k: 1
    auto_insert_metric_name: false
    save_last: true
  learning_rate_monitor:
    _target_: lightning.pytorch.callbacks.LearningRateMonitor
    logging_interval: step
  loguru_model_summary:
    _target_: src.callbacks.loguru_progress_callback.LoguruModelSummaryCallback

# =============================================================================
# Ray Tune HPO 专用配置
# =============================================================================
hpo:
  # ---------------------------------------------------------------------------
  # 实验基础配置
  # ---------------------------------------------------------------------------
  experiment_name: "unet_baseline_hpo"       # HPO实验名称

  # ---------------------------------------------------------------------------
  # Ray Tune 运行参数
  # ---------------------------------------------------------------------------
  run_params:
    name: "unet_hpo_basic"                   # Tune运行名称
    num_samples: 2                           # 搜索的试验数量
                                             # 建议：初始测试用2-5，正式搜索用20-100

    local_dir: "${get_project_root:}/experiments_output/ray_results"
                                             # Ray Tune结果保存目录
                                             # 包含所有试验的日志和检查点

  # ---------------------------------------------------------------------------
  # Ray 集群初始化配置
  # ---------------------------------------------------------------------------
  ray_init_args:
    _target_: ray.init                       # Ray初始化函数
    num_cpus: 4                              # CPU核心数
    num_gpus: 1                              # GPU数量

  # ---------------------------------------------------------------------------
  # 每个试验的资源配置
  # ---------------------------------------------------------------------------
  resources_per_trial:
    use_gpu: true                            # 是否使用GPU
    CPU: 1                                   # 每个试验分配的CPU数
    GPU: 1                                   # 每个试验分配的GPU数
                                             # 注意：总资源 = 试验数 × 每试验资源

  # ---------------------------------------------------------------------------
  # 调度器配置 (ASHA - 异步连续减半算法)
  # ---------------------------------------------------------------------------
  scheduler:
    _target_: ray.tune.schedulers.ASHAScheduler
                                             # ASHA调度器：高效的早停策略

    metric: "val_iou"                        # 优化目标指标
                                             # 语义分割通常使用IoU

    mode: "max"                              # 优化方向
                                             # "max": 最大化指标 (IoU, Accuracy)
                                             # "min": 最小化指标 (Loss)

    max_t: 5                                 # 最大训练轮数
                                             # 表现好的试验最多训练5个epoch

    grace_period: 1                          # 宽限期
                                             # 前1个epoch不进行早停判断

    reduction_factor: 2                      # 减少因子
                                             # 每轮淘汰一半表现差的试验

  # ---------------------------------------------------------------------------
  # 超参数搜索空间定义
  # ---------------------------------------------------------------------------
  search_space:
    lr:                                      # 学习率搜索范围
      min: 1e-5                              # 最小学习率
      max: 1e-2                              # 最大学习率
                                             # Ray Tune会在此范围内采样

    batch_size:                              # 批次大小选择
      - 16                                   # 选项1：小批次，适合显存受限
      - 32                                   # 选项2：大批次，训练更稳定
                                             # 可以添加更多选项：[8, 16, 32, 64]
