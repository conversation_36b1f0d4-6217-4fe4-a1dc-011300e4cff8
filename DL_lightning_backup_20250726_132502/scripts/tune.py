import hydra
from omegaconf import DictConfig, OmegaConf
import ray
from ray import tune
from ray.train import ScalingConfig
from ray.tune import C<PERSON><PERSON><PERSON><PERSON><PERSON>, RunConfig
from ray.tune.integration.pytorch_lightning import TuneReportCallback
from ray.tune.schedulers import ASHAScheduler
import lightning.pytorch as pl
from lightning.pytorch.loggers import Wan<PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import List
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.hydra_resolvers import register_hydra_resolvers
from src.utils.console_manager import setup_console_manager
from src.callbacks.raytune_callback import create_raytune_callback

# 注册自定义的hydra解析器，确保在任何Hydra配置加载之前就被执行
register_hydra_resolvers()

def log_and_print(message):
    print(message)

def tune_function(config: dict):
    base_cfg = hydra.utils.instantiate(config["defaults"])
    hyperparams = config["hyperparams"]
    
    base_cfg.optimizer.lr = hyperparams["lr"]
    base_cfg.data.batch_size = hyperparams["batch_size"]
    
    datamodule: pl.LightningDataModule = hydra.utils.instantiate(base_cfg.data)
    model: pl.LightningModule = hydra.utils.instantiate(
        base_cfg.model,
        optimizer_cfg=base_cfg.optimizer,
        scheduler_cfg=base_cfg.scheduler,
        loss_cfg=base_cfg.loss,
        _recursive_=False
    )
    
    metrics = {"val_loss": "val/loss", "val_iou": "val/iou"}
    tune_callback = TuneReportCallback(metrics, on="validation_end")
    callbacks: List[pl.Callback] = [tune_callback]
    
    if "callbacks" in base_cfg:
        for _, cb_conf in base_cfg.callbacks.items():
            if "_target_" in cb_conf and "TuneReportCallback" not in cb_conf._target_:
                callbacks.append(hydra.utils.instantiate(cb_conf))

    trial_id = "unknown_trial"
    try:
        trial_id = tune.get_context().get_trial_id()
    except (AttributeError, RuntimeError):
        pass

    wandb_logger = WandbLogger(
        project=base_cfg.logger.project,
        name=f"trial_{trial_id}",
        group=base_cfg.hpo.experiment_name,
        tags=base_cfg.logger.tags,
        notes=f"lr={hyperparams['lr']:.5f}, batch_size={hyperparams['batch_size']}",
        log_model=False,
    )
    
    trainer: pl.Trainer = hydra.utils.instantiate(
        base_cfg.trainer, 
        callbacks=callbacks, 
        logger=wandb_logger,
        enable_progress_bar=False,
        enable_model_summary=False,
    )

    trainer.fit(model=model, datamodule=datamodule)

@hydra.main(version_base=None, config_path="../configs/hpo", config_name="tune_basic.yaml")
def main(cfg: DictConfig) -> None:
    OmegaConf.set_struct(cfg, False)

    # 设置统一控制台管理器 (Ray Tune模式)
    console_manager = setup_console_manager(cfg, verbose=True)

    log_and_print("------ Ray Tune HPO 配置 ------")
    log_and_print(OmegaConf.to_yaml(cfg))
    log_and_print("---------------------------------")
    
    # 显式解析所有配置中的插值，确保所有自定义解析器可用
    OmegaConf.resolve(cfg)

    # 使用 Hydra 原生扩展方案E：通过 instantiate 来初始化 Ray
    hydra.utils.instantiate(cfg.hpo.ray_init_args)

    hyperparam_space = {
        "lr": tune.loguniform(cfg.hpo.search_space.lr.min, cfg.hpo.search_space.lr.max),
        "batch_size": tune.choice(cfg.hpo.search_space.batch_size),
    }

    config_space = {
        "defaults": cfg,  # 传递整个基础配置
        "hyperparams": hyperparam_space
    }

    scheduler = hydra.utils.instantiate(cfg.hpo.scheduler)
    reporter = CLIReporter(
        parameter_columns=["hyperparams/lr", "hyperparams/batch_size"],
        metric_columns=["val_iou", "val_loss", "training_iteration"])

    resources = {"CPU": cfg.hpo.resources_per_trial.CPU, "GPU": cfg.hpo.resources_per_trial.GPU}

    # 调试：打印解析后的 local_dir
    resolved_local_dir = cfg.hpo.run_params.get("local_dir", "./ray_results")
    log_and_print(f"调试信息: local_dir 原始值 = {cfg.hpo.run_params.local_dir}")
    log_and_print(f"调试信息: 解析后的 local_dir = {resolved_local_dir}")

    # 创建Ray Tune回调
    raytune_callback = create_raytune_callback(console_manager)
    callbacks = [raytune_callback] if raytune_callback else []

    run_config=RunConfig(
        name=cfg.hpo.run_params.name,
        storage_path=os.path.abspath(str(resolved_local_dir)),
        progress_reporter=reporter,
        callbacks=callbacks,
    )

    tuner = tune.Tuner(
        trainable=tune.with_resources(tune_function, resources),
        param_space=config_space,
        tune_config=tune.TuneConfig(
            scheduler=scheduler,
            num_samples=cfg.hpo.run_params.num_samples,
        ),
        run_config=run_config,
    )

    try:
        results = tuner.fit()

        # 停止仪表盘
        console_manager.stop_dashboard()

        log_and_print("------ HPO 完成 ------")
    except KeyboardInterrupt:
        console_manager.print_warning("用户中断了超参数优化")
        return
    except Exception as e:
        console_manager.print_error(e, "Ray Tune超参数优化")
        raise
    best_result = results.get_best_result(metric=cfg.hpo.scheduler.metric, mode=cfg.hpo.scheduler.mode)
    
    if best_result:
        log_and_print(f"最佳试验路径: {best_result.path}")
        log_and_print(f"最佳试验配置: {best_result.config}")
        log_and_print(f"最佳试验最终验证指标: {best_result.metrics}")
    else:
        log_and_print("未找到成功的试验。")

if __name__ == "__main__":
    main()
