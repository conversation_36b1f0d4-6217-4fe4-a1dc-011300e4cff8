#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
WandB离线数据同步脚本

功能:
- 同步本地离线WandB数据到云端
- 支持批量同步和单个运行同步
- 提供详细的同步状态报告
- 支持配置文件和命令行参数

使用示例:
    # 同步所有离线数据
    python scripts/sync_wandb.py
    
    # 同步特定目录的数据
    python scripts/sync_wandb.py --run-path ./outputs/wandb_offline/run-20240114-123456
    
    # 使用特定配置文件
    python scripts/sync_wandb.py --config-path ../configs --config-name config
    
    # 检查状态而不同步
    python scripts/sync_wandb.py --status-only
"""

import argparse
import logging
import sys
from pathlib import Path
from typing import Optional, List
import hydra
from omegaconf import DictConfig
import wandb

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from src.utils.wandb_utils import WandbModeManager, sync_wandb_data, get_wandb_status


def setup_logging(verbose: bool = False):
    """设置日志配置"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )


def find_offline_runs(base_dir: Path) -> List[Path]:
    """
    查找所有离线运行目录
    
    Args:
        base_dir: 基础搜索目录
        
    Returns:
        离线运行目录列表
    """
    offline_runs = []
    
    # 查找包含wandb运行文件的目录
    for wandb_file in base_dir.rglob("run-*.wandb"):
        run_dir = wandb_file.parent
        if run_dir not in offline_runs:
            offline_runs.append(run_dir)
    
    return sorted(offline_runs)


def sync_single_run(run_path: Path, dry_run: bool = False) -> bool:
    """
    同步单个运行
    
    Args:
        run_path: 运行目录路径
        dry_run: 是否为试运行
        
    Returns:
        同步是否成功
    """
    logger = logging.getLogger(__name__)
    
    if not run_path.exists():
        logger.error(f"运行目录不存在: {run_path}")
        return False
    
    # 检查是否包含wandb文件
    wandb_files = list(run_path.glob("run-*.wandb"))
    if not wandb_files:
        logger.warning(f"目录中没有找到wandb文件: {run_path}")
        return False
    
    logger.info(f"{'[试运行] ' if dry_run else ''}同步运行: {run_path}")
    logger.info(f"找到 {len(wandb_files)} 个wandb文件")
    
    if dry_run:
        logger.info("试运行模式，跳过实际同步")
        return True
    
    try:
        # 使用wandb sync命令同步
        result = wandb.sync(str(run_path))
        logger.info(f"同步成功: {run_path}")
        return True
    except Exception as e:
        logger.error(f"同步失败 {run_path}: {e}")
        return False


def sync_all_runs(base_dir: Path, dry_run: bool = False) -> tuple:
    """
    同步所有离线运行
    
    Args:
        base_dir: 基础目录
        dry_run: 是否为试运行
        
    Returns:
        (成功数量, 总数量)
    """
    logger = logging.getLogger(__name__)
    
    offline_runs = find_offline_runs(base_dir)
    if not offline_runs:
        logger.info(f"在 {base_dir} 中没有找到离线运行数据")
        return 0, 0
    
    logger.info(f"找到 {len(offline_runs)} 个离线运行")
    
    success_count = 0
    for run_path in offline_runs:
        if sync_single_run(run_path, dry_run):
            success_count += 1
    
    logger.info(f"同步完成: {success_count}/{len(offline_runs)} 个运行成功")
    return success_count, len(offline_runs)


def print_status_report(config: DictConfig):
    """打印WandB状态报告"""
    logger = logging.getLogger(__name__)
    
    try:
        status = get_wandb_status(config)
        
        print("\n" + "="*60)
        print("WandB 状态报告")
        print("="*60)
        print(f"当前模式: {status['mode']}")
        print(f"本地目录: {status['local_dir']}")
        print(f"网络连接: {'可用' if status['internet_available'] else '不可用'}")
        
        if status['wandb_service_available'] is not None:
            print(f"WandB服务: {'可用' if status['wandb_service_available'] else '不可用'}")
        
        print(f"离线运行数量: {status['offline_runs_count']}")
        
        # 显示离线运行详情
        if status['offline_runs_count'] > 0:
            base_dir = Path(status['local_dir'])
            offline_runs = find_offline_runs(base_dir)
            print(f"\n离线运行列表:")
            for i, run_path in enumerate(offline_runs, 1):
                wandb_files = list(run_path.glob("run-*.wandb"))
                print(f"  {i}. {run_path} ({len(wandb_files)} 个文件)")
        
        print("="*60)
        
    except Exception as e:
        logger.error(f"获取状态信息失败: {e}")


@hydra.main(version_base=None, config_path="../configs", config_name="config")
def main(cfg: DictConfig):
    """主函数"""
    parser = argparse.ArgumentParser(description="WandB离线数据同步工具")
    parser.add_argument("--run-path", type=str, help="特定运行目录路径")
    parser.add_argument("--base-dir", type=str, help="搜索基础目录")
    parser.add_argument("--dry-run", action="store_true", help="试运行，不实际同步")
    parser.add_argument("--status-only", action="store_true", help="只显示状态，不同步")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.verbose)
    logger = logging.getLogger(__name__)
    
    logger.info("WandB离线数据同步工具启动")
    
    # 显示状态报告
    if args.status_only:
        print_status_report(cfg)
        return
    
    # 确定基础目录
    if args.base_dir:
        base_dir = Path(args.base_dir)
    else:
        # 使用配置中的本地目录
        manager = WandbModeManager(cfg)
        base_dir = manager.local_dir
    
    logger.info(f"使用基础目录: {base_dir}")
    
    # 执行同步
    if args.run_path:
        # 同步特定运行
        run_path = Path(args.run_path)
        success = sync_single_run(run_path, args.dry_run)
        sys.exit(0 if success else 1)
    else:
        # 同步所有运行
        success_count, total_count = sync_all_runs(base_dir, args.dry_run)
        
        if total_count == 0:
            logger.info("没有找到需要同步的数据")
            sys.exit(0)
        elif success_count == total_count:
            logger.info("所有数据同步成功")
            sys.exit(0)
        else:
            logger.error(f"部分同步失败: {success_count}/{total_count}")
            sys.exit(1)


if __name__ == "__main__":
    main()
