import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 🔧 禁用Albumentations版本检查以避免SSL警告
# 这是一个非关键功能，禁用不会影响实际使用
os.environ['NO_ALBUMENTATIONS_UPDATE'] = '1'

# 🔧 抑制TorchMetrics DiceScore警告
# 我们已经显式设置了average参数，可以安全忽略此警告
import warnings
warnings.filterwarnings("ignore", message=".*DiceScore metric currently defaults.*", category=UserWarning)

import hydra
from omegaconf import DictConfig, OmegaConf
import lightning.pytorch as pl
from lightning.pytorch.loggers import Logger
from typing import List, Optional
import torch

from src.utils.hydra_resolvers import register_hydra_resolvers
from src.utils.wandb_utils import get_wandb_status
from src.utils.loguru_console_manager import setup_console_manager

# 注册自定义的hydra解析器，必须在 @hydra.main 之前调用
register_hydra_resolvers()

@hydra.main(version_base=None, config_path="../configs", config_name="config.yaml")
def main(cfg: DictConfig) -> Optional[float]:
    """
    DL_Lightning 训练入口.
    
    Args:
        cfg (DictConfig): Hydra配置对象.
    """
    # 🚀 Tensor Cores优化：针对RTX 3090等支持Tensor Cores的GPU
    # 设置矩阵乘法精度以提升性能
    if torch.cuda.is_available():
        torch.set_float32_matmul_precision('medium')  # 平衡精度和性能

    # 设置统一控制台管理器
    console_manager = setup_console_manager(cfg, verbose=True)

    # 启动全局输出拦截
    console_manager.start_output_intercept()

    # 打印美化的启动横幅
    console_manager.print_startup_banner(cfg)

    # 打印WandB状态信息
    if "wandb" in cfg:
        try:
            wandb_status = get_wandb_status(cfg)
            console_manager.print_wandb_status(wandb_status)
        except Exception as e:
            console_manager.print_error(e, "WandB状态检查")

    # 如果需要查看详细配置，可以取消注释下面的代码
    # print("------ Hydra 配置信息 ------")
    # print(OmegaConf.to_yaml(cfg))
    # print("--------------------------")

    # --- 1. 实例化 DataModule ---
    console_manager.print_info("正在实例化 DataModule...", "📊")
    datamodule: pl.LightningDataModule = hydra.utils.instantiate(cfg.data)

    # --- 2. 实例化 Model (LightningModule) ---
    console_manager.print_info("正在实例化 Model...", "🤖")
    # 这里我们将 model, optimizer, scheduler, loss 的配置传入
    model: pl.LightningModule = hydra.utils.instantiate(
        cfg.model,
        optimizer_cfg=cfg.optimizer,
        scheduler_cfg=cfg.scheduler,
        loss_cfg=cfg.loss,
        _recursive_=False
    )

    # --- 3. 实例化 Callbacks ---
    console_manager.print_info("正在实例化 Callbacks...", "🔧")
    callbacks: List[pl.Callback] = []
    if "callbacks" in cfg:
        for callback_name, cb_conf in cfg.callbacks.items():
            if cb_conf is None:
                console_manager.print_info(f"跳过回调 (配置为null): {callback_name}", "⏭️")
                continue
            if "_target_" in cb_conf:
                # 在 fast_dev_run 模式下跳过 LearningRateMonitor
                if (callback_name == "learning_rate_monitor" and
                    cfg.trainer.get("fast_dev_run", False)):
                    console_manager.print_info(f"跳过回调 (fast_dev_run模式): {callback_name}", "⏭️")
                    continue

                callbacks.append(hydra.utils.instantiate(cb_conf))
                console_manager.print_info(f"添加回调: {callback_name}", "✅")
    
    # --- 4. 实例化 Logger ---
    console_manager.print_info("正在实例化 Logger...", "📝")
    loggers: List[Logger] = []

    # 在 fast_dev_run 模式下禁用 logger
    if cfg.trainer.get("fast_dev_run", False):
        console_manager.print_info("跳过Logger (fast_dev_run模式)", "⏭️")
    elif "logger" in cfg:
        # 检查 cfg.logger 是否直接包含 _target_ (单个logger配置)
        if "_target_" in cfg.logger:
            # 这是一个单个logger配置
            logger_instance = hydra.utils.instantiate(cfg.logger)

            # 处理partial函数的情况
            if callable(logger_instance) and hasattr(logger_instance, 'func'):
                # 这是一个partial函数，需要调用它
                logger_instance = logger_instance(cfg)

            if logger_instance is not None:
                loggers.append(logger_instance)
                console_manager.print_info(f"添加Logger: wandb", "✅")
            else:
                console_manager.print_info("Logger创建失败，返回None", "❌")
        else:
            # 支持多个logger的情况 (多个logger配置的字典)
            for logger_name, lg_conf in cfg.logger.items():
                if isinstance(lg_conf, DictConfig) and "_target_" in lg_conf:
                    logger_instance = hydra.utils.instantiate(lg_conf)

                    # 处理partial函数的情况
                    if callable(logger_instance) and hasattr(logger_instance, 'func'):
                        # 这是一个partial函数，需要调用它
                        logger_instance = logger_instance(cfg)

                    if logger_instance is not None:
                        loggers.append(logger_instance)
                        console_manager.print_info(f"添加Logger: {logger_name}", "✅")
                    else:
                        console_manager.print_info(f"跳过Logger (已禁用): {logger_name}", "⏭️")

    # --- 5. 实例化 Trainer ---
    console_manager.print_info("正在实例化 Trainer...", "🏋️")

    # 🔇 抑制Lightning的verbose输出
    import os
    os.environ['PYTHONWARNINGS'] = 'ignore'

    # 使用上下文管理器临时拦截Trainer创建时的输出
    with console_manager.intercept_output():
        trainer: pl.Trainer = hydra.utils.instantiate(
            cfg.trainer, callbacks=callbacks, logger=loggers
        )

    console_manager.print_success("Trainer创建完成", "✅")

    # --- 6. 开始训练 ---
    console_manager.print_info("开始训练!", "🚀")
    trainer.fit(model=model, datamodule=datamodule)

    # --- 7. 训练结束 ---
    # 获取并返回一个关键指标，可用于HPO
    val_iou = trainer.callback_metrics.get("val/iou", 0.0)
    console_manager.print_success(f"训练完成! 最终 val/iou: {val_iou:.4f}", "🎉")

    # 确保返回的是 float 类型
    if isinstance(val_iou, torch.Tensor):
        return val_iou.item()

    return val_iou

if __name__ == "__main__":
    main()
