# 🔄 WandB本地化和云端模式切换指南

## 📋 概述

DL_lightning项目现在支持WandB的灵活模式切换，包括在线模式、离线模式和禁用模式。这个功能允许您在不同的网络环境和使用场景下无缝切换WandB的运行模式。

### 🎯 支持的模式

| 模式 | 描述 | 使用场景 |
|------|------|---------|
| **online** | 在线模式，数据实时同步到云端 | 网络稳定，需要实时协作 |
| **offline** | 离线模式，数据保存到本地，可后续同步 | 网络不稳定，或需要本地备份 |
| **disabled** | 禁用模式，不记录任何WandB数据 | 调试阶段，或不需要实验跟踪 |
| **auto** | 自动模式，根据网络状况自动选择 | 推荐的默认模式 |

---

## 🚀 快速开始

### 1. 基本使用

**默认模式 (auto)**:
```bash
# 自动检测网络状况并选择最佳模式
python scripts/train.py
```

**指定特定模式**:
```bash
# 强制在线模式
python scripts/train.py wandb.mode=online

# 强制离线模式  
python scripts/train.py wandb.mode=offline

# 禁用WandB
python scripts/train.py wandb.mode=disabled
```

### 2. 环境变量控制

```bash
# 通过环境变量设置模式
export WANDB_MODE=offline
python scripts/train.py

# 设置API密钥
export WANDB_API_KEY=your_api_key_here
python scripts/train.py
```

### 3. 使用预定义配置

```bash
# 使用在线模式配置
python scripts/train.py logger=wandb_online

# 使用离线模式配置
python scripts/train.py logger=wandb_offline

# 使用禁用模式配置
python scripts/train.py logger=wandb_disabled
```

---

## ⚙️ 配置详解

### 1. 主配置文件 (configs/config.yaml)

```yaml
wandb:
  # 基本配置
  project: ${project_name}
  name: ${run_name}
  tags: ["baseline", "${model.name}"]
  notes: "A baseline run for segmentation."
  
  # 模式配置
  mode: auto  # online/offline/disabled/auto
  
  # 本地存储配置
  local_dir: null  # 使用Hydra输出目录
  
  # 高级配置
  log_model: false
  silent: true
  api_key: null  # 建议通过环境变量设置
  timeout: 10
  retry_attempts: 3
```

### 2. 模式特定配置

**在线模式** (`configs/logger/wandb_online.yaml`):
```yaml
wandb:
  mode: online
  log_model: true
  silent: false
  sync_tensorboard: true
  tags: ["online", "cloud-sync", "${model.name}"]
```

**离线模式** (`configs/logger/wandb_offline.yaml`):
```yaml
wandb:
  mode: offline
  log_model: true
  local_dir: "./outputs/wandb_offline"
  tags: ["offline", "local-storage", "${model.name}"]
```

**禁用模式** (`configs/logger/wandb_disabled.yaml`):
```yaml
wandb:
  mode: disabled
  tags: ["disabled", "no-logging"]
```

---

## 🔧 高级功能

### 1. 离线数据同步

**同步所有离线数据**:
```bash
python scripts/sync_wandb.py
```

**同步特定运行**:
```bash
python scripts/sync_wandb.py --run-path ./outputs/wandb_offline/run-20240114-123456
```

**检查状态**:
```bash
python scripts/sync_wandb.py --status-only
```

**试运行 (不实际同步)**:
```bash
python scripts/sync_wandb.py --dry-run
```

### 2. 网络容错

系统会自动检测网络状况：
- 如果网络不可用，自动切换到离线模式
- 如果WandB服务不可用，降级到离线模式
- 提供详细的状态信息和错误处理

### 3. 本地数据管理

**本地存储位置**:
- 默认: `{hydra_output_dir}/wandb_local/`
- 自定义: 通过 `wandb.local_dir` 配置

**数据结构**:
```
wandb_local/
├── run-20240114-123456/
│   ├── run-20240114-123456.wandb
│   ├── files/
│   └── logs/
└── run-20240114-234567/
    ├── run-20240114-234567.wandb
    ├── files/
    └── logs/
```

---

## 📊 使用场景示例

### 场景1: 开发和调试

```bash
# 禁用WandB，专注于代码调试
python scripts/train.py wandb.mode=disabled trainer.fast_dev_run=true
```

### 场景2: 本地实验

```bash
# 离线模式，本地保存实验数据
python scripts/train.py wandb.mode=offline model=unet data.batch_size=16
```

### 场景3: 云端协作

```bash
# 在线模式，实时同步到云端
python scripts/train.py wandb.mode=online logger=wandb_online
```

### 场景4: 网络不稳定环境

```bash
# 自动模式，根据网络状况自动选择
python scripts/train.py wandb.mode=auto
```

### 场景5: 批量实验后同步

```bash
# 1. 运行多个离线实验
python scripts/train.py wandb.mode=offline model=unet
python scripts/train.py wandb.mode=offline model=deeplabv3plus
python scripts/train.py wandb.mode=offline model=unetpp

# 2. 批量同步到云端
python scripts/sync_wandb.py
```

---

## 🔍 状态监控

### 1. 训练时状态显示

训练开始时会显示WandB状态信息：
```
------ WandB 状态信息 ------
WandB模式: offline
本地目录: /path/to/outputs/wandb_local
网络连接: 不可用
离线运行数量: 3
---------------------------
```

### 2. 详细状态检查

```bash
python scripts/sync_wandb.py --status-only
```

输出示例：
```
============================================================
WandB 状态报告
============================================================
当前模式: offline
本地目录: /path/to/outputs/wandb_local
网络连接: 可用
WandB服务: 可用
离线运行数量: 3

离线运行列表:
  1. /path/to/outputs/wandb_local/run-20240114-123456 (2 个文件)
  2. /path/to/outputs/wandb_local/run-20240114-234567 (2 个文件)
  3. /path/to/outputs/wandb_local/run-20240115-012345 (2 个文件)
============================================================
```

---

## 🛠️ 故障排除

### 常见问题

**1. WandB API密钥问题**
```bash
# 设置API密钥
export WANDB_API_KEY=your_api_key_here
# 或者在配置文件中设置 wandb.api_key
```

**2. 网络连接问题**
```bash
# 强制离线模式
python scripts/train.py wandb.mode=offline
```

**3. 同步失败**
```bash
# 检查网络连接和API密钥
python scripts/sync_wandb.py --status-only

# 尝试单个运行同步
python scripts/sync_wandb.py --run-path /path/to/specific/run
```

**4. 权限问题**
```bash
# 检查本地目录权限
ls -la outputs/wandb_local/

# 手动创建目录
mkdir -p outputs/wandb_local
```

### 调试模式

```bash
# 启用详细日志
python scripts/train.py wandb.mode=offline --verbose

# 同步时启用详细输出
python scripts/sync_wandb.py --verbose
```

---

## 🎯 最佳实践

### 1. 推荐的工作流程

1. **开发阶段**: 使用 `disabled` 模式
2. **本地实验**: 使用 `offline` 模式
3. **正式训练**: 使用 `auto` 或 `online` 模式
4. **定期同步**: 使用同步脚本上传离线数据

### 2. 配置建议

- 使用 `auto` 模式作为默认设置
- 通过环境变量设置敏感信息（API密钥）
- 定期清理本地离线数据
- 为不同场景创建专门的配置文件

### 3. 性能优化

- 在离线模式下启用 `log_model` 以保存完整信息
- 使用 `silent: true` 减少控制台输出
- 合理设置 `timeout` 和 `retry_attempts`

---

## 📚 API参考

### WandbModeManager类

```python
from src.utils.wandb_utils import WandbModeManager

# 创建管理器
manager = WandbModeManager(config)

# 创建logger
logger = manager.create_logger()

# 同步离线数据
success = manager.sync_offline_data()

# 获取状态信息
status = manager.get_status_info()
```

### 便捷函数

```python
from src.utils.wandb_utils import create_wandb_logger, sync_wandb_data, get_wandb_status

# 创建logger
logger = create_wandb_logger(config)

# 同步数据
success = sync_wandb_data(config)

# 获取状态
status = get_wandb_status(config)
```

---

这个指南涵盖了WandB模式切换的所有功能和使用方法。通过灵活的模式切换，您可以在任何环境下高效地进行深度学习实验！🚀
