#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
DL_lightning项目统一Rich主题配置

设计原则:
- 统一的颜色主题和样式
- 一致的视觉体验
- 易于维护和扩展
- 支持不同输出场景
"""

from typing import Dict, Any
from rich.theme import Theme
from rich.style import Style

# =============================================================================
# 统一颜色主题定义
# =============================================================================

# 主色调配置
PRIMARY_COLORS = {
    "primary": "#00D4AA",           # 主色调 - 青绿色
    "secondary": "#FF6B6B",         # 次要色 - 珊瑚红
    "accent": "#4ECDC4",            # 强调色 - 浅青色
    "success": "#51CF66",           # 成功色 - 绿色
    "warning": "#FFD43B",           # 警告色 - 黄色
    "error": "#FF6B6B",             # 错误色 - 红色
    "info": "#74C0FC",              # 信息色 - 蓝色
}

# 语义化颜色
SEMANTIC_COLORS = {
    "text_primary": "#FFFFFF",      # 主要文本
    "text_secondary": "#A0A0A0",    # 次要文本
    "text_muted": "#666666",        # 静音文本
    "background": "#1A1A1A",        # 背景色
    "border": "#333333",            # 边框色
    "highlight": "#FFE066",         # 高亮色
}

# 状态颜色
STATUS_COLORS = {
    "training": "#00D4AA",          # 训练状态
    "validation": "#74C0FC",        # 验证状态
    "testing": "#FFD43B",           # 测试状态
    "completed": "#51CF66",         # 完成状态
    "failed": "#FF6B6B",            # 失败状态
    "pending": "#A0A0A0",           # 等待状态
}

# =============================================================================
# Rich主题配置
# =============================================================================

# 创建统一的Rich主题
DL_LIGHTNING_THEME = Theme({
    # 基础样式
    "primary": PRIMARY_COLORS["primary"],
    "secondary": PRIMARY_COLORS["secondary"],
    "accent": PRIMARY_COLORS["accent"],
    
    # 状态样式
    "success": f"bold {PRIMARY_COLORS['success']}",
    "warning": f"bold {PRIMARY_COLORS['warning']}",
    "error": f"bold {PRIMARY_COLORS['error']}",
    "info": f"bold {PRIMARY_COLORS['info']}",
    
    # 文本样式
    "title": f"bold {SEMANTIC_COLORS['text_primary']}",
    "subtitle": f"{SEMANTIC_COLORS['text_secondary']}",
    "muted": f"{SEMANTIC_COLORS['text_muted']}",
    "highlight": f"bold {SEMANTIC_COLORS['highlight']}",
    
    # 训练相关样式
    "training": f"bold {STATUS_COLORS['training']}",
    "validation": f"bold {STATUS_COLORS['validation']}",
    "testing": f"bold {STATUS_COLORS['testing']}",
    "completed": f"bold {STATUS_COLORS['completed']}",
    "failed": f"bold {STATUS_COLORS['failed']}",
    "pending": f"{STATUS_COLORS['pending']}",
    
    # 进度条样式
    "progress.bar": PRIMARY_COLORS["primary"],
    "progress.percentage": f"bold {SEMANTIC_COLORS['text_primary']}",
    "progress.data.speed": SEMANTIC_COLORS["text_secondary"],
    "progress.description": SEMANTIC_COLORS["text_primary"],
    "progress.elapsed": SEMANTIC_COLORS["text_secondary"],
    "progress.remaining": SEMANTIC_COLORS["text_secondary"],
    
    # 表格样式
    "table.header": f"bold {PRIMARY_COLORS['primary']}",
    "table.row_even": SEMANTIC_COLORS["text_primary"],
    "table.row_odd": SEMANTIC_COLORS["text_secondary"],
    
    # 面板样式
    "panel.border": SEMANTIC_COLORS["border"],
    "panel.title": f"bold {PRIMARY_COLORS['primary']}",
})

# =============================================================================
# 样式配置函数
# =============================================================================

def get_theme() -> Theme:
    """获取统一的Rich主题"""
    return DL_LIGHTNING_THEME

def get_color(color_name: str) -> str:
    """
    获取指定的颜色值
    
    Args:
        color_name: 颜色名称
        
    Returns:
        颜色值字符串
    """
    # 优先从主色调查找
    if color_name in PRIMARY_COLORS:
        return PRIMARY_COLORS[color_name]
    
    # 从语义化颜色查找
    if color_name in SEMANTIC_COLORS:
        return SEMANTIC_COLORS[color_name]
    
    # 从状态颜色查找
    if color_name in STATUS_COLORS:
        return STATUS_COLORS[color_name]
    
    # 默认返回主色调
    return PRIMARY_COLORS["primary"]

def get_style(style_name: str) -> str:
    """
    获取指定的样式
    
    Args:
        style_name: 样式名称
        
    Returns:
        样式字符串
    """
    style_map = {
        "title": f"bold {get_color('text_primary')}",
        "subtitle": get_color('text_secondary'),
        "success": f"bold {get_color('success')}",
        "warning": f"bold {get_color('warning')}",
        "error": f"bold {get_color('error')}",
        "info": f"bold {get_color('info')}",
        "muted": get_color('text_muted'),
        "highlight": f"bold {get_color('highlight')}",
    }
    
    return style_map.get(style_name, get_color('text_primary'))

# =============================================================================
# 预定义样式组合
# =============================================================================

# 横幅样式
BANNER_STYLE = {
    "border_style": get_color("primary"),
    "title_style": get_style("title"),
    "padding": (1, 2),
}

# 状态面板样式
STATUS_PANEL_STYLE = {
    "border_style": get_color("accent"),
    "title_style": get_style("subtitle"),
    "padding": (0, 1),
}

# 进度条样式配置
PROGRESS_STYLE = {
    "bar_width": 40,
    "complete_style": get_color("primary"),
    "finished_style": get_color("success"),
    "pulse_style": get_color("accent"),
}

# 表格样式配置
TABLE_STYLE = {
    "header_style": get_style("title"),
    "row_styles": [get_color("text_primary"), get_color("text_secondary")],
    "border_style": get_color("border"),
}

def get_banner_style() -> Dict[str, Any]:
    """获取横幅样式配置"""
    return BANNER_STYLE.copy()

def get_status_panel_style() -> Dict[str, Any]:
    """获取状态面板样式配置"""
    return STATUS_PANEL_STYLE.copy()

def get_progress_style() -> Dict[str, Any]:
    """获取进度条样式配置"""
    return PROGRESS_STYLE.copy()

def get_table_style() -> Dict[str, Any]:
    """获取表格样式配置"""
    return TABLE_STYLE.copy()

# =============================================================================
# 表情符号和图标配置
# =============================================================================

# 统一的表情符号映射
EMOJI_MAP = {
    # 状态表情
    "success": "✅",
    "error": "❌", 
    "warning": "⚠️",
    "info": "ℹ️",
    "loading": "⏳",
    "completed": "🎉",
    
    # 功能表情
    "training": "🏋️",
    "validation": "🔍",
    "testing": "🧪",
    "model": "🤖",
    "data": "📊",
    "config": "⚙️",
    "logger": "📝",
    "callback": "🔧",
    "checkpoint": "💾",
    
    # 系统表情
    "gpu": "🎮",
    "cpu": "💻",
    "memory": "🧠",
    "network": "🌐",
    "offline": "💾",
    "online": "🌐",
    "disabled": "🚫",
    "auto": "🔄",
}

def get_emoji(name: str) -> str:
    """
    获取统一的表情符号
    
    Args:
        name: 表情符号名称
        
    Returns:
        表情符号字符串
    """
    return EMOJI_MAP.get(name, "📌")
