#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
系统资源监控组件

功能特性:
- CPU使用率监控
- 内存使用率监控  
- GPU使用率和显存监控
- 多GPU环境支持
- 优雅降级机制
- 实时数据获取
"""

import os
import time
import psutil
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

# GPU监控依赖
try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

try:
    import pynvml
    pynvml.nvmlInit()
    PYNVML_AVAILABLE = True
except (ImportError, Exception):
    pynvml = None
    PYNVML_AVAILABLE = False


@dataclass
class SystemStats:
    """系统资源统计数据结构"""
    cpu_percent: float
    memory_used_gb: float
    memory_total_gb: float
    memory_percent: float
    gpu_stats: List[Dict[str, float]]
    timestamp: float


@dataclass
class GPUStats:
    """GPU统计数据结构"""
    gpu_id: int
    name: str
    utilization_percent: float
    memory_used_gb: float
    memory_total_gb: float
    memory_percent: float
    temperature: Optional[float] = None
    power_usage: Optional[float] = None


class SystemMonitor:
    """系统资源监控器"""
    
    def __init__(self, update_interval: float = 1.0, enable_gpu: bool = True):
        """
        初始化系统监控器
        
        Args:
            update_interval: 更新间隔（秒）
            enable_gpu: 是否启用GPU监控
        """
        self.update_interval = update_interval
        self.enable_gpu = enable_gpu and TORCH_AVAILABLE and PYNVML_AVAILABLE
        
        # GPU设备信息
        self.gpu_count = 0
        self.gpu_devices = []
        
        if self.enable_gpu:
            self._init_gpu_monitoring()
        
        # 缓存最新数据
        self._last_update = 0
        self._cached_stats: Optional[SystemStats] = None
    
    def _init_gpu_monitoring(self):
        """初始化GPU监控"""
        try:
            if torch.cuda.is_available():
                self.gpu_count = torch.cuda.device_count()
                
                # 获取GPU设备信息
                for i in range(self.gpu_count):
                    if PYNVML_AVAILABLE:
                        try:
                            handle = pynvml.nvmlDeviceGetHandleByIndex(i)
                            name = pynvml.nvmlDeviceGetName(handle).decode('utf-8')
                            self.gpu_devices.append({
                                'id': i,
                                'name': name,
                                'handle': handle
                            })
                        except Exception:
                            # 如果无法获取GPU信息，使用基础信息
                            self.gpu_devices.append({
                                'id': i,
                                'name': torch.cuda.get_device_name(i),
                                'handle': None
                            })
                    else:
                        self.gpu_devices.append({
                            'id': i,
                            'name': torch.cuda.get_device_name(i),
                            'handle': None
                        })
        except Exception:
            self.enable_gpu = False
            self.gpu_count = 0
            self.gpu_devices = []
    
    def get_cpu_stats(self) -> Tuple[float, int]:
        """
        获取CPU统计信息
        
        Returns:
            (cpu_percent, cpu_count)
        """
        try:
            cpu_percent = psutil.cpu_percent(interval=0.1)
            cpu_count = psutil.cpu_count()
            return cpu_percent, cpu_count
        except Exception:
            return 0.0, 1
    
    def get_memory_stats(self) -> Tuple[float, float, float]:
        """
        获取内存统计信息
        
        Returns:
            (used_gb, total_gb, percent)
        """
        try:
            memory = psutil.virtual_memory()
            used_gb = memory.used / (1024**3)
            total_gb = memory.total / (1024**3)
            percent = memory.percent
            return used_gb, total_gb, percent
        except Exception:
            return 0.0, 0.0, 0.0
    
    def get_gpu_stats(self) -> List[GPUStats]:
        """
        获取GPU统计信息
        
        Returns:
            GPU统计信息列表
        """
        if not self.enable_gpu:
            return []
        
        gpu_stats = []
        
        for device in self.gpu_devices:
            try:
                gpu_id = device['id']
                name = device['name']
                handle = device['handle']
                
                # 基础统计
                stats = GPUStats(
                    gpu_id=gpu_id,
                    name=name,
                    utilization_percent=0.0,
                    memory_used_gb=0.0,
                    memory_total_gb=0.0,
                    memory_percent=0.0
                )
                
                # 获取显存信息
                if torch.cuda.is_available():
                    try:
                        # 使用torch获取显存信息
                        memory_reserved = torch.cuda.memory_reserved(gpu_id) / (1024**3)
                        memory_allocated = torch.cuda.memory_allocated(gpu_id) / (1024**3)
                        
                        # 获取总显存
                        if handle and PYNVML_AVAILABLE:
                            mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                            memory_total = mem_info.total / (1024**3)
                            memory_used = mem_info.used / (1024**3)
                        else:
                            # 使用torch的方式估算
                            memory_total = torch.cuda.get_device_properties(gpu_id).total_memory / (1024**3)
                            memory_used = memory_allocated
                        
                        stats.memory_used_gb = memory_used
                        stats.memory_total_gb = memory_total
                        stats.memory_percent = (memory_used / memory_total * 100) if memory_total > 0 else 0.0
                        
                    except Exception:
                        pass
                
                # 获取GPU利用率和其他信息
                if handle and PYNVML_AVAILABLE:
                    try:
                        # GPU利用率
                        util = pynvml.nvmlDeviceGetUtilizationRates(handle)
                        stats.utilization_percent = float(util.gpu)
                        
                        # 温度
                        try:
                            temp = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)
                            stats.temperature = float(temp)
                        except Exception:
                            pass
                        
                        # 功耗
                        try:
                            power = pynvml.nvmlDeviceGetPowerUsage(handle) / 1000.0  # 转换为瓦特
                            stats.power_usage = power
                        except Exception:
                            pass
                            
                    except Exception:
                        pass
                
                gpu_stats.append(stats)
                
            except Exception:
                # 如果获取特定GPU信息失败，添加空统计
                gpu_stats.append(GPUStats(
                    gpu_id=device['id'],
                    name=device['name'],
                    utilization_percent=0.0,
                    memory_used_gb=0.0,
                    memory_total_gb=0.0,
                    memory_percent=0.0
                ))
        
        return gpu_stats
    
    def get_current_stats(self, force_update: bool = False) -> SystemStats:
        """
        获取当前系统统计信息
        
        Args:
            force_update: 是否强制更新（忽略缓存）
            
        Returns:
            系统统计信息
        """
        current_time = time.time()
        
        # 检查是否需要更新
        if (not force_update and 
            self._cached_stats and 
            current_time - self._last_update < self.update_interval):
            return self._cached_stats
        
        # 获取CPU统计
        cpu_percent, _ = self.get_cpu_stats()
        
        # 获取内存统计
        memory_used, memory_total, memory_percent = self.get_memory_stats()
        
        # 获取GPU统计
        gpu_stats = self.get_gpu_stats()
        
        # 转换GPU统计为字典格式
        gpu_stats_dict = []
        for gpu in gpu_stats:
            gpu_dict = {
                'gpu_id': gpu.gpu_id,
                'name': gpu.name,
                'utilization': gpu.utilization_percent,
                'memory_used': gpu.memory_used_gb,
                'memory_total': gpu.memory_total_gb,
                'memory_percent': gpu.memory_percent
            }
            if gpu.temperature is not None:
                gpu_dict['temperature'] = gpu.temperature
            if gpu.power_usage is not None:
                gpu_dict['power'] = gpu.power_usage
            gpu_stats_dict.append(gpu_dict)
        
        # 创建统计对象
        stats = SystemStats(
            cpu_percent=cpu_percent,
            memory_used_gb=memory_used,
            memory_total_gb=memory_total,
            memory_percent=memory_percent,
            gpu_stats=gpu_stats_dict,
            timestamp=current_time
        )
        
        # 更新缓存
        self._cached_stats = stats
        self._last_update = current_time
        
        return stats
    
    def get_formatted_stats(self, include_details: bool = False) -> Dict[str, str]:
        """
        获取格式化的统计信息字符串
        
        Args:
            include_details: 是否包含详细信息
            
        Returns:
            格式化的统计信息字典
        """
        stats = self.get_current_stats()
        formatted = {}
        
        # CPU信息
        formatted['cpu'] = f"💻 CPU {stats.cpu_percent:.1f}%"
        
        # 内存信息
        formatted['memory'] = f"🧠 RAM {stats.memory_used_gb:.1f}/{stats.memory_total_gb:.1f}GB"
        
        # GPU信息
        if stats.gpu_stats:
            for i, gpu in enumerate(stats.gpu_stats):
                gpu_key = f"gpu_{i}" if len(stats.gpu_stats) > 1 else "gpu"
                gpu_text = f"🎮 GPU {gpu['utilization']:.0f}%"
                
                if include_details and gpu.get('memory_total', 0) > 0:
                    gpu_text += f" | 📺 VRAM {gpu['memory_used']:.1f}/{gpu['memory_total']:.1f}GB"
                
                if include_details and gpu.get('temperature'):
                    gpu_text += f" | 🌡️ {gpu['temperature']:.0f}°C"
                
                formatted[gpu_key] = gpu_text
        
        return formatted
    
    def cleanup(self):
        """清理资源"""
        if PYNVML_AVAILABLE and pynvml:
            try:
                pynvml.nvmlShutdown()
            except Exception:
                pass


# 全局监控器实例
_system_monitor: Optional[SystemMonitor] = None


def get_system_monitor() -> SystemMonitor:
    """获取全局系统监控器实例"""
    global _system_monitor
    if _system_monitor is None:
        _system_monitor = SystemMonitor()
    return _system_monitor


def get_system_stats_string(include_details: bool = False) -> str:
    """
    获取系统统计信息字符串
    
    Args:
        include_details: 是否包含详细信息
        
    Returns:
        格式化的系统统计字符串
    """
    monitor = get_system_monitor()
    stats_dict = monitor.get_formatted_stats(include_details)
    return " | ".join(stats_dict.values())


def cleanup_system_monitor():
    """清理全局系统监控器"""
    global _system_monitor
    if _system_monitor:
        _system_monitor.cleanup()
        _system_monitor = None
