#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
统一控制台输出管理器

功能特性:
- 统一的Rich风格输出
- 自动检测训练模式 (Lightning / Lightning+WandB / Lightning+WandB+RayTune)
- 拦截所有输出源 (代码输出、Lightning输出、Python Logging、第三方库输出)
- 系统资源监控
- 美观的进度条和状态显示
- 统一主题配置
"""

import os
import sys
import time
import psutil
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from contextlib import contextmanager
from enum import Enum
from io import StringIO
import threading
import re
import logging

import torch
from rich.console import Console
from rich.logging import RichHandler
from rich.panel import Panel
from rich.table import Table
from rich.columns import Columns
from rich.text import Text
from rich.progress import (
    Progress, SpinnerColumn, TextColumn, BarColumn,
    MofNCompleteColumn, TimeRemainingColumn, TimeElapsedColumn
)
from rich.live import Live
from rich.layout import Layout
from rich.align import Align
from omegaconf import DictConfig

# 导入统一主题配置
try:
    from .rich_theme import (
        get_theme, get_color, get_style, get_emoji,
        get_banner_style, get_status_panel_style
    )
    THEME_AVAILABLE = True
except ImportError:
    THEME_AVAILABLE = False

# GPU监控
try:
    import pynvml
    pynvml.nvmlInit()
    PYNVML_AVAILABLE = True
except (ImportError, Exception):
    pynvml = None
    PYNVML_AVAILABLE = False


class OutputInterceptor:
    """全局输出拦截器 - 拦截所有stdout/stderr输出并重定向到ConsoleManager"""

    def __init__(self, console_manager):
        self.console_manager = console_manager
        self.original_stdout = sys.stdout
        self.original_stderr = sys.stderr
        self.buffer = StringIO()
        self.lock = threading.Lock()
        self.active = False

        # 用于识别不同类型输出的正则表达式
        self.patterns = {
            'lightning_info': re.compile(r'GPU available: .+|TPU available: .+|IPU available: .+|Using .+ backend|Trainer already configured'),
            'model_summary': re.compile(r'┏━━━|┃.*┃|┗━━━|Name.*Type.*Params.*Mode|architecture.*DeepLabV3Plus'),
            'progress_bar': re.compile(r'Epoch \d+: \d+%\||\d+%\|.*\|'),
            'data_info': re.compile(r'📊|🎯|📏|✅.*总计:|权重配置:|多尺度采样:|选择.*权重:|总计:.*个样本'),
            'debug_info': re.compile(r'DEBUG.*|INFO.*初始化|已构建.*映射|SuiDeClassMapper.*|为.*阶段创建了.*个变换|优化器创建成功|调度器创建成功|开始训练 Epoch'),
            'parameters_info': re.compile(r'Trainable params:|Non-trainable params:|Total params:|Total estimated model params size'),
        }

    def write(self, text):
        """拦截写入操作"""
        if not self.active or not text.strip():
            return self.original_stdout.write(text)

        with self.lock:
            # 临时恢复原始输出，避免递归
            sys.stdout = self.original_stdout
            sys.stderr = self.original_stderr

            try:
                # 根据内容类型进行不同处理
                text_stripped = text.strip()

                # Lightning模型摘要表格
                if self.patterns['model_summary'].search(text_stripped):
                    self._handle_model_summary(text_stripped)
                # 参数信息
                elif self.patterns['parameters_info'].search(text_stripped):
                    self._handle_parameters_info(text_stripped)
                # 数据加载信息
                elif self.patterns['data_info'].search(text_stripped):
                    self._handle_data_info(text_stripped)
                # Lightning系统信息
                elif self.patterns['lightning_info'].search(text_stripped):
                    self._handle_lightning_info(text_stripped)
                # 进度条信息
                elif self.patterns['progress_bar'].search(text_stripped):
                    self._handle_progress_info(text_stripped)
                # 调试信息
                elif self.patterns['debug_info'].search(text_stripped):
                    self._handle_debug_info(text_stripped)
                else:
                    # 其他信息直接通过Rich输出
                    if text_stripped:  # 只输出非空内容
                        self.console_manager.console.print(text_stripped, style="dim")
            finally:
                # 恢复拦截
                if self.active:
                    sys.stdout = self
                    sys.stderr = self

        # 必须返回写入的字符数
        return len(text)

    def _handle_model_summary(self, text):
        """处理模型摘要表格"""
        # 保持原有的表格格式，但通过Rich输出
        self.console_manager.console.print(text, style="bold blue")

    def _handle_parameters_info(self, text):
        """处理参数信息"""
        self.console_manager.console.print(text, style="bold cyan")

    def _handle_data_info(self, text):
        """处理数据加载信息"""
        if "📊" in text:
            self.console_manager.print_info(text.replace("📊", "").strip(), "📊")
        elif "🎯" in text:
            self.console_manager.print_info(text.replace("🎯", "").strip(), "🎯")
        elif "📏" in text:
            self.console_manager.print_info(text.replace("📏", "").strip(), "📏")
        elif "✅" in text and "总计:" in text:
            self.console_manager.print_success(text.replace("✅", "").strip(), "✅")
        elif "多尺度采样:" in text:
            self.console_manager.print_info(text, "📊")
        elif "权重配置:" in text:
            self.console_manager.print_info(text, "⚙️")
        elif "选择" in text and "权重:" in text:
            self.console_manager.print_info(text, "📏")
        elif "总计:" in text and "个样本" in text:
            self.console_manager.print_success(text, "✅")
        else:
            self.console_manager.console.print(text, style="cyan")

    def _handle_lightning_info(self, text):
        """处理Lightning系统信息"""
        self.console_manager.print_info(text, "⚡")

    def _handle_progress_info(self, text):
        """处理进度信息"""
        self.console_manager.console.print(text, style="green")

    def _handle_debug_info(self, text):
        """处理调试信息"""
        # 移除时间戳和日志级别前缀，只保留核心信息
        clean_text = re.sub(r'^\s*\[.*?\]\s*(DEBUG|INFO)\s*', '', text).strip()

        if "SuiDeClassMapper" in clean_text:
            self.console_manager.print_info(clean_text, "🗺️")
        elif "变换" in clean_text:
            self.console_manager.print_info(clean_text, "🔄")
        elif "优化器" in clean_text:
            self.console_manager.print_info(clean_text, "⚡")
        elif "调度器" in clean_text:
            self.console_manager.print_info(clean_text, "📈")
        elif "开始训练" in clean_text:
            self.console_manager.print_info(clean_text, "🚀")
        else:
            self.console_manager.console.print(clean_text, style="dim cyan")

    def flush(self):
        """刷新缓冲区"""
        pass

    def start_intercept(self):
        """开始拦截"""
        self.active = True
        sys.stdout = self
        sys.stderr = self

    def stop_intercept(self):
        """停止拦截"""
        self.active = False
        sys.stdout = self.original_stdout
        sys.stderr = self.original_stderr


class TrainingMode(Enum):
    """训练模式枚举"""
    LIGHTNING = "lightning"
    LIGHTNING_WANDB = "lightning_wandb"
    LIGHTNING_WANDB_RAYTUNE = "lightning_wandb_raytune"


class ConsoleManager:
    """统一控制台输出管理器"""

    def __init__(self, config: Optional[DictConfig] = None, verbose: bool = True):
        """
        初始化控制台管理器

        Args:
            config: 配置对象
            verbose: 是否启用详细输出
        """
        # 🔇 首先设置环境变量抑制地理空间库的verbose输出
        self._suppress_geospatial_verbose()

        self.config = config
        self.verbose = verbose

        # 自动检测训练模式
        self.training_mode = self._detect_training_mode(config)

        # 环境检测
        self.is_ray_tune_worker = self._is_ray_tune_worker()
        self.is_jupyter = self._is_jupyter()
        self.is_ci = self._is_ci()

        # 输出模式配置
        self.quiet_mode = getattr(config, 'quiet', False) if config else False
        self.no_color = os.getenv('NO_COLOR', '').lower() in ('1', 'true', 'yes')
        self.enabled = not (self.is_ray_tune_worker and self.training_mode == TrainingMode.LIGHTNING_WANDB_RAYTUNE)

        # 设置Rich控制台
        self._setup_rich_console()

        # 状态跟踪
        self.training_started = False
        self.current_epoch = 0
        self.total_epochs = 0
        self.best_metrics = {}

        # 进度条管理
        self.progress = None
        self.live_display = None

        # 初始化输出拦截器
        self.output_interceptor = OutputInterceptor(self)

        # 设置统一输出管理
        self._setup_logging()

    def _suppress_geospatial_verbose(self):
        """抑制地理空间数据处理库的verbose输出"""
        # 设置GDAL环境变量
        os.environ['CPL_LOG'] = 'OFF'  # 关闭GDAL日志
        os.environ['GDAL_DISABLE_READDIR_ON_OPEN'] = 'EMPTY_DIR'  # 减少文件系统访问
        os.environ['CPL_VSIL_CURL_ALLOWED_EXTENSIONS'] = '.tif,.tiff'  # 限制网络访问

        # 设置其他地理空间库的环境变量
        os.environ['RASTERIO_ENV'] = 'False'  # 抑制rasterio环境信息
        os.environ['PROJ_DEBUG'] = '0'  # 关闭PROJ调试信息

        # 尝试通过Python接口设置GDAL选项
        try:
            from osgeo import gdal
            gdal.SetConfigOption('CPL_LOG', 'OFF')
            gdal.SetConfigOption('CPL_DEBUG', 'OFF')
            gdal.UseExceptions()  # 使用异常而不是错误输出
        except ImportError:
            pass

    def _detect_training_mode(self, config: Optional[DictConfig]) -> TrainingMode:
        """自动检测训练模式"""
        if not config:
            return TrainingMode.LIGHTNING

        # 检测Ray Tune模式
        if self._is_ray_tune_environment():
            return TrainingMode.LIGHTNING_WANDB_RAYTUNE

        # 检测WandB模式
        try:
            if 'wandb' in config and config.wandb.get('mode', 'disabled') != 'disabled':
                return TrainingMode.LIGHTNING_WANDB
        except Exception:
            # 配置访问失败，使用默认模式
            pass

        # 默认Lightning模式
        return TrainingMode.LIGHTNING

    def _is_ray_tune_environment(self) -> bool:
        """检测是否在Ray Tune环境中"""
        return (
            'TUNE_TRIAL_ID' in os.environ or
            'RAY_TRIAL_ID' in os.environ or
            hasattr(sys.modules.get('ray', None), 'tune')
        )

    def _is_ray_tune_worker(self) -> bool:
        """检测是否为Ray Tune worker进程"""
        return (
            'TUNE_TRIAL_ID' in os.environ or
            'RAY_TRIAL_ID' in os.environ
        )

    def _is_jupyter(self) -> bool:
        """检测是否在Jupyter环境中"""
        try:
            from IPython import get_ipython
            return get_ipython() is not None
        except ImportError:
            return False

    def _is_ci(self) -> bool:
        """检测是否在CI环境中"""
        ci_vars = ['CI', 'CONTINUOUS_INTEGRATION', 'GITHUB_ACTIONS', 'GITLAB_CI']
        return any(os.getenv(var) for var in ci_vars)

    def _setup_rich_console(self):
        """设置Rich控制台"""
        if not self.enabled:
            self.console = None
            return

        # 使用统一主题配置
        theme = get_theme() if THEME_AVAILABLE else None

        self.console = Console(
            force_terminal=True,
            color_system="auto" if not self.no_color else None,
            width=120,
            legacy_windows=False,
            theme=theme
        )
    
    def _setup_logging(self):
        """设置统一的日志格式"""
        if not self.enabled or not self.console:
            return

        # 清除现有处理器
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 设置日志级别
        log_level = logging.DEBUG if self.verbose else logging.INFO
        if self.quiet_mode:
            log_level = logging.WARNING

        # 创建Rich处理器
        rich_handler = RichHandler(
            console=self.console,
            show_time=True,
            show_level=True,
            show_path=False,
            rich_tracebacks=True,
            tracebacks_suppress=[torch],
            markup=True,
            log_time_format="[%H:%M:%S]"
        )
        rich_handler.setLevel(log_level)

        # 设置格式
        formatter = logging.Formatter(
            fmt="%(message)s",
            datefmt="[%X]"
        )
        rich_handler.setFormatter(formatter)

        # 配置根日志器
        root_logger.setLevel(log_level)
        root_logger.addHandler(rich_handler)

        # 🔇 设置特定模块的日志级别 - 更激进的抑制
        logging.getLogger("lightning").setLevel(logging.ERROR)  # 更严格
        logging.getLogger("lightning.pytorch").setLevel(logging.ERROR)
        logging.getLogger("lightning.pytorch.trainer").setLevel(logging.ERROR)
        logging.getLogger("lightning.pytorch.core").setLevel(logging.ERROR)
        logging.getLogger("lightning.pytorch.utilities").setLevel(logging.ERROR)
        logging.getLogger("torch").setLevel(logging.ERROR)  # 更严格
        logging.getLogger("torchmetrics").setLevel(logging.ERROR)  # 更严格

        # 🔇 抑制地理空间数据处理库的verbose输出
        logging.getLogger("rasterio").setLevel(logging.ERROR)
        logging.getLogger("fiona").setLevel(logging.ERROR)
        logging.getLogger("GDAL").setLevel(logging.ERROR)
        logging.getLogger("osgeo").setLevel(logging.ERROR)

        # 🔇 抑制其他常见的verbose库
        logging.getLogger("PIL").setLevel(logging.WARNING)
        logging.getLogger("matplotlib").setLevel(logging.WARNING)
        logging.getLogger("urllib3").setLevel(logging.WARNING)
        logging.getLogger("requests").setLevel(logging.WARNING)

    def _setup_output_interceptor(self):
        """设置输出拦截器 - 拦截所有输出流"""
        if not self.enabled or not self.console:
            return

        # 保存原始函数和流
        self._original_print = print
        self._original_stdout = sys.stdout
        self._original_stderr = sys.stderr

        # 重定向print到Rich console
        import builtins
        builtins.print = self._rich_print

        # 🎯 关键：重定向标准输出流到Rich
        sys.stdout = self._create_rich_stream('stdout')
        sys.stderr = self._create_rich_stream('stderr')

    def _create_rich_stream(self, stream_type):
        """创建Rich流包装器"""
        class RichStream:
            def __init__(self, console, original_stream, stream_type):
                self.console = console
                self.original_stream = original_stream
                self.stream_type = stream_type

            def write(self, text):
                if not text:
                    return

                try:
                    # 直接使用原始流，避免Rich处理导致的问题
                    self.original_stream.write(text)
                    self.original_stream.flush()
                except Exception:
                    # 如果原始流也失败，则忽略
                    pass

            def flush(self):
                try:
                    self.original_stream.flush()
                except Exception:
                    pass

            def __getattr__(self, name):
                # 代理其他属性到原始流
                return getattr(self.original_stream, name)

        return RichStream(self.console, getattr(sys, stream_type), stream_type)

    def _rich_print(self, *args, **kwargs):
        """Rich格式化的print函数"""
        if not self.enabled or not self.console:
            # 降级到原始print
            self._original_print(*args, **kwargs)
            return

        # 移除Rich Console不支持的参数
        rich_kwargs = kwargs.copy()
        if 'file' in rich_kwargs:
            del rich_kwargs['file']
        if 'flush' in rich_kwargs:
            del rich_kwargs['flush']

        # 使用Rich console输出
        try:
            self.console.print(*args, **rich_kwargs)
        except Exception:
            # 如果Rich输出失败，降级到原始print
            self._original_print(*args, **kwargs)
    
    def print_startup_banner(self, config: DictConfig):
        """打印启动横幅"""
        if self.quiet_mode:
            return
        
        # 创建项目信息表格
        info_table = Table(show_header=False, box=None, padding=(0, 2))
        info_table.add_column("Key", style="cyan", width=20)
        info_table.add_column("Value", style="white")
        
        info_table.add_row("🚀 项目", "DL_lightning")
        info_table.add_row("📊 实验", config.get('experiment_name', 'Unknown'))
        info_table.add_row("🏷️ 运行名称", config.get('run_name', 'Unknown'))

        # 安全访问模型信息
        try:
            model_name = config.model.get('name', config.model.get('_target_', 'Unknown'))
            info_table.add_row("🤖 模型", model_name)
        except Exception:
            info_table.add_row("🤖 模型", "Unknown")

        # 安全访问数据信息
        try:
            if hasattr(config.data, 'dataset_config') and hasattr(config.data.dataset_config, 'data_dir'):
                data_dir = config.data.dataset_config.data_dir
            else:
                data_dir = config.data.get('data_dir', 'Unknown')
            info_table.add_row("📁 数据集", Path(str(data_dir)).name if data_dir != 'Unknown' else 'Unknown')
        except Exception:
            info_table.add_row("📁 数据集", "Unknown")

        # 安全访问类别数
        try:
            if hasattr(config.data, 'dataset_config') and hasattr(config.data.dataset_config, 'num_classes'):
                num_classes = config.data.dataset_config.num_classes
            else:
                num_classes = config.data.get('num_classes', 'Unknown')
            info_table.add_row("🎯 类别数", str(num_classes))
        except Exception:
            info_table.add_row("🎯 类别数", "Unknown")
        
        # WandB状态
        if 'wandb' in config:
            wandb_mode = config.wandb.get('mode', 'unknown')
            wandb_status = {
                'online': '🌐 在线模式',
                'offline': '💾 离线模式', 
                'disabled': '🚫 已禁用',
                'auto': '🔄 自动模式'
            }.get(wandb_mode, f'❓ {wandb_mode}')
            info_table.add_row("📈 WandB", wandb_status)
        
        # 系统信息
        device_info = self._get_device_info()
        info_table.add_row("💻 设备", device_info)
        
        # 创建面板
        banner_panel = Panel(
            info_table,
            title="[bold blue]🔥 DL_lightning 训练启动[/bold blue]",
            border_style="blue",
            padding=(1, 2)
        )
        
        self.console.print()
        self.console.print(banner_panel)
        self.console.print()

    def print_info(self, message: str, emoji: str = "ℹ️"):
        """打印信息消息"""
        if not self.enabled or not self.console:
            print(f"{emoji} {message}")
            return

        if THEME_AVAILABLE:
            emoji = get_emoji("info") if emoji == "ℹ️" else emoji

        self.console.print(f"{emoji} {message}", style="cyan")

    def print_success(self, message: str, emoji: str = "✅"):
        """打印成功消息"""
        if not self.enabled or not self.console:
            print(f"{emoji} {message}")
            return

        if THEME_AVAILABLE:
            emoji = get_emoji("success") if emoji == "✅" else emoji

        self.console.print(f"{emoji} {message}", style="green")

    def print_warning(self, message: str, emoji: str = "⚠️"):
        """打印警告消息"""
        if not self.enabled or not self.console:
            print(f"{emoji} {message}")
            return

        if THEME_AVAILABLE:
            emoji = get_emoji("warning") if emoji == "⚠️" else emoji

        self.console.print(f"{emoji} {message}", style="yellow")

    def print_error(self, error: Union[str, Exception], context: str = "", emoji: str = "❌"):
        """打印错误消息"""
        if not self.enabled or not self.console:
            print(f"{emoji} 错误 {context}: {error}")
            return

        if THEME_AVAILABLE:
            emoji = get_emoji("error") if emoji == "❌" else emoji

        error_msg = str(error)
        if context:
            self.console.print(f"{emoji} 错误 {context}: {error_msg}", style="red")
        else:
            self.console.print(f"{emoji} {error_msg}", style="red")

    def print_wandb_status(self, wandb_status: Dict[str, Any]):
        """打印WandB状态信息"""
        if not self.enabled or not self.console or self.quiet_mode:
            return

        # 根据训练模式显示不同的WandB信息
        if self.training_mode == TrainingMode.LIGHTNING:
            return  # Lightning模式不显示WandB信息

        mode = wandb_status.get('mode', 'unknown')
        project = wandb_status.get('project', 'Unknown')
        run_name = wandb_status.get('run_name', 'Unknown')

        # 状态表格
        status_table = Table(show_header=False, box=None, padding=(0, 1))
        status_table.add_column("Key", style="cyan", width=15)
        status_table.add_column("Value", style="white")

        # 模式图标
        mode_icons = {
            'online': '🌐',
            'offline': '💾',
            'disabled': '🚫',
            'auto': '🔄'
        }
        mode_icon = mode_icons.get(mode, '❓')

        status_table.add_row("📈 模式", f"{mode_icon} {mode}")
        status_table.add_row("📁 项目", project)
        status_table.add_row("🏷️ 运行", run_name)

        if 'url' in wandb_status and wandb_status['url']:
            status_table.add_row("🔗 链接", wandb_status['url'])

        # 创建面板
        wandb_panel = Panel(
            status_table,
            title="[bold magenta]📈 Weights & Biases 状态[/bold magenta]",
            border_style="magenta",
            padding=(0, 1)
        )

        self.console.print(wandb_panel)
        self.console.print()
        self.console.print()
    

    def print_config_summary(self, config: DictConfig):
        """打印配置摘要"""
        if self.quiet_mode:
            return
        
        # 创建配置摘要表格
        config_table = Table(show_header=True, box=None)
        config_table.add_column("组件", style="cyan", width=15)
        config_table.add_column("配置", style="white")
        
        # 模型配置
        model_info = f"{config.model.name}"
        if hasattr(config.model, 'model_params') and 'backbone' in config.model.model_params:
            model_info += f" ({config.model.model_params.backbone})"
        config_table.add_row("🤖 模型", model_info)
        
        # 数据配置
        data_info = f"批次大小: {config.data.batch_size}, 图像尺寸: {config.data.image_size}"
        config_table.add_row("📊 数据", data_info)
        
        # 训练配置
        train_info = f"最大轮数: {config.trainer.max_epochs}, 精度: {config.trainer.precision}"
        config_table.add_row("🏋️ 训练", train_info)
        
        # 优化器配置
        opt_name = config.optimizer._target_.split('.')[-1]
        opt_info = f"{opt_name} (lr: {config.optimizer.lr})"
        config_table.add_row("⚡ 优化器", opt_info)
        
        # 调度器配置
        if config.scheduler:
            sched_name = config.scheduler._target_.split('.')[-1]
            config_table.add_row("📈 调度器", sched_name)
        
        config_panel = Panel(
            config_table,
            title="[bold yellow]⚙️ 配置摘要[/bold yellow]",
            border_style="yellow",
            padding=(0, 1)
        )
        
        self.console.print(config_panel)
        self.console.print()
    
    def _get_device_info(self) -> str:
        """获取设备信息"""
        if torch.cuda.is_available():
            device_name = torch.cuda.get_device_name(0)
            device_count = torch.cuda.device_count()
            return f"🎮 {device_name} (x{device_count})"
        else:
            return "💻 CPU"
    
    def _get_system_stats(self) -> Dict[str, str]:
        """获取系统资源状态"""
        stats = {}
        
        # CPU和内存
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        stats['cpu'] = f"💻 CPU {cpu_percent:.1f}%"
        stats['memory'] = f"🧠 RAM {memory.used/1024**3:.1f}/{memory.total/1024**3:.1f}GB"
        
        # GPU信息
        if PYNVML_AVAILABLE and torch.cuda.is_available():
            try:
                handle = pynvml.nvmlDeviceGetHandleByIndex(0)
                util = pynvml.nvmlDeviceGetUtilizationRates(handle)
                mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                
                stats['gpu'] = f"🎮 GPU {util.gpu}%"
                stats['vram'] = f"📺 VRAM {mem_info.used/1024**3:.1f}/{mem_info.total/1024**3:.1f}GB"
            except Exception:
                stats['gpu'] = "🎮 GPU N/A"
                stats['vram'] = "📺 VRAM N/A"
        
        return stats
    
    def create_training_progress(self, total_epochs: int) -> Progress:
        """创建训练进度条"""
        if self.quiet_mode:
            return None
        
        self.total_epochs = total_epochs
        
        progress = Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(bar_width=40),
            MofNCompleteColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            "•",
            TimeElapsedColumn(),
            "<",
            TimeRemainingColumn(),
            console=self.console,
            transient=False
        )
        
        return progress
    
    def print_training_summary(self, logs: Dict[str, Any], duration: float):
        """打印训练总结"""
        if self.quiet_mode:
            return
        
        # 创建总结表格
        summary_table = Table(show_header=False, box=None, padding=(0, 2))
        summary_table.add_column("指标", style="cyan", width=20)
        summary_table.add_column("值", style="bold white")
        
        # 训练时间
        hours, remainder = divmod(duration, 3600)
        minutes, seconds = divmod(remainder, 60)
        time_str = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
        summary_table.add_row("⏱️ 训练时间", time_str)
        
        # 最终指标
        if 'val/loss' in logs:
            summary_table.add_row("📉 最终验证损失", f"{logs['val/loss']:.4f}")
        if 'val/iou' in logs:
            summary_table.add_row("🎯 最终IoU", f"{logs['val/iou']:.4f}")
        if 'val/dice' in logs:
            summary_table.add_row("🎲 最终Dice", f"{logs['val/dice']:.4f}")
        
        # 最佳指标
        for metric, value in self.best_metrics.items():
            summary_table.add_row(f"🏆 最佳{metric}", f"{value:.4f}")
        
        # 系统资源
        final_stats = self._get_system_stats()
        for key, value in final_stats.items():
            summary_table.add_row(f"📊 {key.upper()}", value)
        
        summary_panel = Panel(
            summary_table,
            title="[bold green]🎉 训练完成总结[/bold green]",
            border_style="green",
            padding=(1, 2)
        )
        
        self.console.print()
        self.console.print(summary_panel)
        self.console.print()
    

    
    @contextmanager
    def status(self, message: str):
        """状态上下文管理器"""
        if self.quiet_mode:
            yield
        else:
            with self.console.status(f"[bold blue]{message}[/bold blue]") as status:
                yield status
    
    def update_best_metrics(self, metrics: Dict[str, float]):
        """更新最佳指标"""
        for key, value in metrics.items():
            if key not in self.best_metrics or value > self.best_metrics[key]:
                self.best_metrics[key] = value
    
    def info(self, message: str):
        """输出信息消息"""
        if self.console:
            self.console.print(f"ℹ️  {message}", style="blue")
        else:
            print(f"INFO: {message}")

    def success(self, message: str):
        """输出成功消息"""
        if self.console:
            self.console.print(f"✅ {message}", style="green")
        else:
            print(f"SUCCESS: {message}")

    def warning(self, message: str):
        """输出警告消息"""
        if self.console:
            self.console.print(f"⚠️  {message}", style="yellow")
        else:
            print(f"WARNING: {message}")

    def error(self, message: str):
        """输出错误消息"""
        if self.console:
            self.console.print(f"❌ {message}", style="red")
        else:
            print(f"ERROR: {message}")

    def cleanup(self):
        """清理资源"""
        # 恢复原始输出流
        if hasattr(self, '_original_stdout'):
            sys.stdout = self._original_stdout
        if hasattr(self, '_original_stderr'):
            sys.stderr = self._original_stderr
        if hasattr(self, '_original_print'):
            import builtins
            builtins.print = self._original_print

        if PYNVML_AVAILABLE and pynvml:
            try:
                pynvml.nvmlShutdown()
            except Exception:
                pass

    def start_output_intercept(self):
        """开始全局输出拦截"""
        if self.enabled and self.output_interceptor:
            # 先输出信息，再启动拦截
            self.print_info("启动全局输出拦截", "🔇")
            self.output_interceptor.start_intercept()

    def stop_output_intercept(self):
        """停止全局输出拦截"""
        if self.output_interceptor:
            self.output_interceptor.stop_intercept()
            # 停止拦截后再输出信息
            self.print_info("停止全局输出拦截", "🔊")

    @contextmanager
    def intercept_output(self):
        """上下文管理器：临时拦截输出"""
        self.start_output_intercept()
        try:
            yield
        finally:
            self.stop_output_intercept()


# 全局控制台管理器实例
_console_manager: Optional[ConsoleManager] = None


def get_console_manager() -> ConsoleManager:
    """获取全局控制台管理器实例"""
    global _console_manager
    if _console_manager is None:
        _console_manager = ConsoleManager()
    return _console_manager


def setup_console_manager(config: Optional[DictConfig] = None, verbose: bool = True) -> ConsoleManager:
    """设置全局控制台管理器"""
    global _console_manager
    _console_manager = ConsoleManager(config, verbose)
    return _console_manager
