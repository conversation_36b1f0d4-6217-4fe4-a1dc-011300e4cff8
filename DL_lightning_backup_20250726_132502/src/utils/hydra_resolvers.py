import os
import hydra
from omegaconf import OmegaConf
from hydra.core.hydra_config import HydraConfig

def register_hydra_resolvers() -> None:
    """在程序启动时，一次性注册所有自定义的Hydra功能。"""

    # 注册一个函数，用于获取项目根目录
    # 在 .yaml 文件中可以通过 ${get_project_root:} 调用
    # 它将返回当前Python进程的工作目录，假定为项目根目录
    OmegaConf.register_new_resolver(
        "get_project_root",
        lambda: os.getcwd()
    )

    # 注册一个函数，用于获取当前Hydra运行的输出目录
    # 在 .yaml 文件中可以通过 ${get_run_dir:} 调用
    # 它将返回一个唯一的路径，例如: outputs/YYYY-MM-DD/HH-MM-SS
    OmegaConf.register_new_resolver(
        "get_run_dir",
        lambda: HydraConfig.get().runtime.output_dir
    )

    # 注册一个函数，用于安全地拼接路径
    # 在 .yaml 文件中可以通过 ${path_join:path1,path2,...} 调用
    OmegaConf.register_new_resolver(
        "path_join",
        lambda *args: os.path.join(*args)
    )

    # 这里可以继续添加更多自定义功能，例如获取git commit hash等
