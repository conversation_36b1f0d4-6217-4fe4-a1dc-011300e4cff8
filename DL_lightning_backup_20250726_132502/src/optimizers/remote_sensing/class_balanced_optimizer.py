#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
类别平衡优化器实现
专为遥感图像分割中的类别不平衡问题设计
"""

import torch
from torch.optim.optimizer import Optimizer
from typing import Any, Dict, Optional, Union, Iterable, List
import math


class ClassBalancedOptimizer(Optimizer):
    """
    类别平衡优化器
    
    专为遥感图像分割中的类别不平衡问题设计，主要特点：
    - 根据类别频率动态调整学习率
    - 对稀有类别给予更多关注
    - 基于梯度的类别重要性评估
    - 自适应的类别权重更新
    
    Args:
        params: 模型参数
        lr: 基础学习率 (默认: 1e-3)
        betas: Adam的beta参数 (默认: (0.9, 0.999))
        eps: 数值稳定性参数 (默认: 1e-8)
        weight_decay: 权重衰减 (默认: 1e-2)
        class_weights: 类别权重 (默认: None, 自动计算)
        balance_factor: 平衡因子 (默认: 0.1)
        adaptive_weights: 是否使用自适应权重 (默认: True)
    """
    
    def __init__(
        self,
        params: Iterable[torch.Tensor],
        lr: float = 1e-3,
        betas: tuple = (0.9, 0.999),
        eps: float = 1e-8,
        weight_decay: float = 1e-2,
        class_weights: Optional[List[float]] = None,
        balance_factor: float = 0.1,
        adaptive_weights: bool = True,
        **kwargs
    ):
        if not 0.0 <= lr:
            raise ValueError(f"Invalid learning rate: {lr}")
        if not 0.0 <= eps:
            raise ValueError(f"Invalid epsilon value: {eps}")
        if not 0.0 <= betas[0] < 1.0:
            raise ValueError(f"Invalid beta parameter at index 0: {betas[0]}")
        if not 0.0 <= betas[1] < 1.0:
            raise ValueError(f"Invalid beta parameter at index 1: {betas[1]}")
        if not 0.0 <= weight_decay:
            raise ValueError(f"Invalid weight_decay value: {weight_decay}")
        if not 0.0 <= balance_factor <= 1.0:
            raise ValueError(f"Invalid balance_factor value: {balance_factor}")
        
        defaults = dict(
            lr=lr,
            betas=betas,
            eps=eps,
            weight_decay=weight_decay,
            class_weights=class_weights,
            balance_factor=balance_factor,
            adaptive_weights=adaptive_weights
        )
        super().__init__(params, defaults)
        
        # 初始化类别统计
        self.class_stats = {}
        self.global_step = 0
    
    def step(self, closure: Optional[callable] = None, class_gradients: Optional[Dict[int, float]] = None) -> Optional[float]:
        """
        执行单步优化
        
        Args:
            closure: 闭包函数
            class_gradients: 各类别的梯度统计 {class_id: gradient_norm}
        """
        loss = None
        if closure is not None:
            with torch.enable_grad():
                loss = closure()
        
        self.global_step += 1
        
        # 更新类别统计
        if class_gradients is not None:
            self._update_class_stats(class_gradients)
        
        for group in self.param_groups:
            for p in group['params']:
                if p.grad is None:
                    continue
                
                grad = p.grad.data
                if grad.dtype in {torch.float16, torch.bfloat16}:
                    grad = grad.float()
                
                state = self.state[p]
                
                # 状态初始化
                if len(state) == 0:
                    state['step'] = 0
                    state['exp_avg'] = torch.zeros_like(p.data).float()
                    state['exp_avg_sq'] = torch.zeros_like(p.data).float()
                    # 参数重要性权重
                    state['importance_weight'] = 1.0
                
                exp_avg, exp_avg_sq = state['exp_avg'], state['exp_avg_sq']
                beta1, beta2 = group['betas']
                
                state['step'] += 1
                
                # 计算类别平衡的学习率
                if group['adaptive_weights'] and class_gradients is not None:
                    importance_weight = self._compute_importance_weight(p, class_gradients, group)
                    state['importance_weight'] = importance_weight
                else:
                    importance_weight = state['importance_weight']
                
                adaptive_lr = group['lr'] * importance_weight
                
                # 权重衰减
                if group['weight_decay'] != 0:
                    p.data.mul_(1 - adaptive_lr * group['weight_decay'])
                
                # 指数移动平均
                exp_avg.mul_(beta1).add_(grad, alpha=1 - beta1)
                exp_avg_sq.mul_(beta2).addcmul_(grad, grad, value=1 - beta2)
                
                # 偏差修正
                bias_correction1 = 1 - beta1 ** state['step']
                bias_correction2 = 1 - beta2 ** state['step']
                
                denom = (exp_avg_sq.sqrt() / math.sqrt(bias_correction2)).add_(group['eps'])
                step_size = adaptive_lr / bias_correction1
                
                # 参数更新
                p.data.addcdiv_(exp_avg, denom, value=-step_size)
        
        return loss
    
    def _update_class_stats(self, class_gradients: Dict[int, float]):
        """更新类别统计信息"""
        for class_id, grad_norm in class_gradients.items():
            if class_id not in self.class_stats:
                self.class_stats[class_id] = {
                    'total_gradient': 0.0,
                    'count': 0,
                    'avg_gradient': 0.0
                }
            
            stats = self.class_stats[class_id]
            stats['total_gradient'] += grad_norm
            stats['count'] += 1
            stats['avg_gradient'] = stats['total_gradient'] / stats['count']
    
    def _compute_importance_weight(self, param: torch.Tensor, class_gradients: Dict[int, float], group: Dict) -> float:
        """
        计算参数的重要性权重
        基于类别梯度统计和参数特征
        """
        if not self.class_stats:
            return 1.0
        
        # 计算全局平均梯度
        total_avg_grad = sum(stats['avg_gradient'] for stats in self.class_stats.values())
        num_classes = len(self.class_stats)
        global_avg_grad = total_avg_grad / num_classes if num_classes > 0 else 1.0
        
        # 计算当前批次的类别不平衡程度
        current_gradients = list(class_gradients.values())
        if len(current_gradients) > 1:
            grad_std = torch.tensor(current_gradients).std().item()
            grad_mean = torch.tensor(current_gradients).mean().item()
            imbalance_ratio = grad_std / (grad_mean + 1e-8)
        else:
            imbalance_ratio = 0.0
        
        # 基于参数类型调整权重
        param_type_weight = self._get_param_type_weight(param)
        
        # 计算最终的重要性权重
        balance_factor = group['balance_factor']
        importance_weight = 1.0 + balance_factor * imbalance_ratio * param_type_weight
        
        # 限制权重范围
        importance_weight = max(0.1, min(5.0, importance_weight))
        
        return importance_weight
    
    def _get_param_type_weight(self, param: torch.Tensor) -> float:
        """
        根据参数类型获取权重
        不同类型的参数对类别平衡的敏感度不同
        """
        if param.dim() == 4:  # 卷积层权重
            # 输出通道数通常与类别数相关
            out_channels = param.shape[0]
            if out_channels <= 64:  # 早期特征提取层
                return 0.5
            elif out_channels <= 256:  # 中期特征层
                return 1.0
            else:  # 高级特征层
                return 1.5
        elif param.dim() == 2:  # 全连接层权重
            # 分类头通常对类别平衡最敏感
            return 2.0
        elif param.dim() == 1:  # 偏置或归一化层参数
            return 1.0
        else:
            return 1.0
    
    def get_class_statistics(self) -> Dict:
        """获取类别统计信息"""
        return {
            'class_stats': self.class_stats,
            'global_step': self.global_step,
            'num_classes': len(self.class_stats)
        }
    
    def reset_class_statistics(self):
        """重置类别统计信息"""
        self.class_stats = {}
        self.global_step = 0


# 为了兼容性，也提供一个别名
ClassBalancedAdamW = ClassBalancedOptimizer
