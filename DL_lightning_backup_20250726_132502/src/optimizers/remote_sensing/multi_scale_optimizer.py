#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
多尺度优化器实现
专为遥感图像的多尺度特征学习设计
"""

import torch
from torch.optim.optimizer import Optimizer
from typing import Any, Dict, Optional, Union, Iterable
import math


class MultiScaleOptimizer(Optimizer):
    """
    多尺度优化器
    
    专为遥感图像的多尺度特征学习设计，主要特点：
    - 对不同尺度的特征使用不同的学习率
    - 自适应调整各层的更新强度
    - 针对遥感图像的空间层次结构优化
    - 基于AdamW但增加了尺度感知机制
    
    Args:
        params: 模型参数
        lr: 基础学习率 (默认: 1e-3)
        betas: Adam的beta参数 (默认: (0.9, 0.999))
        eps: 数值稳定性参数 (默认: 1e-8)
        weight_decay: 权重衰减 (默认: 1e-2)
        scale_factor: 尺度因子 (默认: 0.8)
        layer_decay: 层级衰减因子 (默认: 0.9)
    """
    
    def __init__(
        self,
        params: Iterable[torch.Tensor],
        lr: float = 1e-3,
        betas: tuple = (0.9, 0.999),
        eps: float = 1e-8,
        weight_decay: float = 1e-2,
        scale_factor: float = 0.8,
        layer_decay: float = 0.9,
        **kwargs
    ):
        if not 0.0 <= lr:
            raise ValueError(f"Invalid learning rate: {lr}")
        if not 0.0 <= eps:
            raise ValueError(f"Invalid epsilon value: {eps}")
        if not 0.0 <= betas[0] < 1.0:
            raise ValueError(f"Invalid beta parameter at index 0: {betas[0]}")
        if not 0.0 <= betas[1] < 1.0:
            raise ValueError(f"Invalid beta parameter at index 1: {betas[1]}")
        if not 0.0 <= weight_decay:
            raise ValueError(f"Invalid weight_decay value: {weight_decay}")
        if not 0.0 < scale_factor <= 1.0:
            raise ValueError(f"Invalid scale_factor value: {scale_factor}")
        if not 0.0 < layer_decay <= 1.0:
            raise ValueError(f"Invalid layer_decay value: {layer_decay}")
        
        defaults = dict(
            lr=lr,
            betas=betas,
            eps=eps,
            weight_decay=weight_decay,
            scale_factor=scale_factor,
            layer_decay=layer_decay
        )
        super().__init__(params, defaults)
    
    def step(self, closure: Optional[callable] = None) -> Optional[float]:
        """执行单步优化"""
        loss = None
        if closure is not None:
            with torch.enable_grad():
                loss = closure()
        
        for group_idx, group in enumerate(self.param_groups):
            for param_idx, p in enumerate(group['params']):
                if p.grad is None:
                    continue
                
                grad = p.grad.data
                if grad.dtype in {torch.float16, torch.bfloat16}:
                    grad = grad.float()
                
                state = self.state[p]
                
                # 状态初始化
                if len(state) == 0:
                    state['step'] = 0
                    state['exp_avg'] = torch.zeros_like(p.data).float()
                    state['exp_avg_sq'] = torch.zeros_like(p.data).float()
                    # 计算参数的尺度特征
                    state['scale_level'] = self._compute_scale_level(p)
                
                exp_avg, exp_avg_sq = state['exp_avg'], state['exp_avg_sq']
                beta1, beta2 = group['betas']
                
                state['step'] += 1
                
                # 计算尺度自适应学习率
                scale_level = state['scale_level']
                layer_factor = group['layer_decay'] ** group_idx
                scale_adaptive_lr = group['lr'] * layer_factor * (group['scale_factor'] ** scale_level)
                
                # 权重衰减
                if group['weight_decay'] != 0:
                    p.data.mul_(1 - scale_adaptive_lr * group['weight_decay'])
                
                # 指数移动平均
                exp_avg.mul_(beta1).add_(grad, alpha=1 - beta1)
                exp_avg_sq.mul_(beta2).addcmul_(grad, grad, value=1 - beta2)
                
                # 偏差修正
                bias_correction1 = 1 - beta1 ** state['step']
                bias_correction2 = 1 - beta2 ** state['step']
                
                denom = (exp_avg_sq.sqrt() / math.sqrt(bias_correction2)).add_(group['eps'])
                step_size = scale_adaptive_lr / bias_correction1
                
                # 参数更新
                p.data.addcdiv_(exp_avg, denom, value=-step_size)
        
        return loss
    
    def _compute_scale_level(self, param: torch.Tensor) -> int:
        """
        计算参数的尺度级别
        基于参数张量的维度和大小来确定其在网络中的尺度级别
        """
        if param.dim() == 4:  # 卷积层权重
            # 基于卷积核大小确定尺度级别
            kernel_size = param.shape[2] * param.shape[3]
            if kernel_size >= 49:  # 7x7或更大
                return 0  # 最大尺度
            elif kernel_size >= 25:  # 5x5
                return 1
            elif kernel_size >= 9:  # 3x3
                return 2
            else:  # 1x1
                return 3  # 最小尺度
        elif param.dim() == 2:  # 全连接层权重
            # 基于参数数量确定尺度级别
            param_count = param.numel()
            if param_count >= 1000000:  # 大型全连接层
                return 1
            elif param_count >= 10000:  # 中型全连接层
                return 2
            else:  # 小型全连接层
                return 3
        elif param.dim() == 1:  # 偏置或归一化层参数
            return 2  # 中等尺度
        else:
            return 2  # 默认中等尺度


# 为了兼容性，也提供一个别名
MultiScaleAdamW = MultiScaleOptimizer
