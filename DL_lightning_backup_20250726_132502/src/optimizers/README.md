# 自定义优化器目录

## 📋 用途说明

此目录用于存放自定义的优化器实现。当前项目默认使用 PyTorch 原生优化器，通过配置文件进行管理。

## 🔧 当前实现方式

### 配置文件位置
```
configs/optimizer/
├── adamw.yaml          # AdamW 优化器配置
└── sgd.yaml            # SGD 优化器配置
```

### 使用方式
```bash
# 使用 AdamW 优化器
python scripts/train.py optimizer=adamw

# 使用 SGD 优化器  
python scripts/train.py optimizer=sgd

# 覆盖学习率
python scripts/train.py optimizer=adamw optimizer.lr=0.01
```

### 实例化机制
优化器通过 `SegmentationModule.configure_optimizers()` 方法中的 `hydra.utils.instantiate()` 动态创建：

```python
optimizer = hydra.utils.instantiate(
    self.optimizer_cfg,
    params=self.architecture.parameters(),
    _partial_=False
)
```

## 🚀 何时需要自定义优化器

### 适用场景
- **研究新的优化算法**: 实验性的优化方法
- **遥感数据特化**: 针对遥感图像分割的特殊优化策略
- **第三方集成**: 集成外部优化器库（如 `timm.optim`）
- **性能优化**: 针对特定硬件的优化实现

### 不需要自定义的情况
- 使用标准优化器（Adam、AdamW、SGD等）
- 只需要调整超参数
- 使用 PyTorch 原生支持的优化器

## 💡 实现示例

### 1. 创建自定义优化器

```python
# src/optimizers/custom_adamw.py
import torch
from torch.optim.optimizer import Optimizer

class RemoteSensingAdamW(Optimizer):
    """
    针对遥感图像分割优化的 AdamW
    添加了梯度裁剪和自适应权重衰减
    """
    
    def __init__(self, params, lr=1e-3, weight_decay=1e-2, 
                 gradient_clip=1.0, adaptive_wd=True):
        defaults = dict(
            lr=lr, 
            weight_decay=weight_decay,
            gradient_clip=gradient_clip,
            adaptive_wd=adaptive_wd
        )
        super().__init__(params, defaults)
    
    def step(self, closure=None):
        # 自定义优化步骤
        # 可以添加梯度裁剪、自适应权重衰减等
        pass
```

### 2. 创建配置文件

```yaml
# configs/optimizer/custom_adamw.yaml
_target_: src.optimizers.custom_adamw.RemoteSensingAdamW
lr: 1e-3
weight_decay: 1e-2
gradient_clip: 1.0
adaptive_wd: true
```

### 3. 注册到模块

```python
# src/optimizers/__init__.py
from .custom_adamw import RemoteSensingAdamW

__all__ = ['RemoteSensingAdamW']
```

## 📚 参考资源

### PyTorch 优化器文档
- [torch.optim](https://pytorch.org/docs/stable/optim.html)
- [自定义优化器教程](https://pytorch.org/tutorials/beginner/examples_autograd/polynomial_custom_function.html)

### 第三方优化器库
- [timm.optim](https://github.com/rwightman/pytorch-image-models/tree/master/timm/optim)
- [torch-optimizer](https://github.com/jettify/pytorch-optimizer)

### 遥感分割相关优化策略
- 多尺度训练的学习率策略
- 类别不平衡的权重调整
- 大图像分割的内存优化

## 🎯 最佳实践

1. **继承基类**: 继承 `torch.optim.Optimizer` 确保兼容性
2. **参数验证**: 在 `__init__` 中验证参数有效性
3. **文档完整**: 提供详细的文档字符串
4. **测试覆盖**: 编写单元测试验证优化器行为
5. **性能考虑**: 避免在 `step()` 方法中进行重复计算

## 📞 技术支持

如需实现自定义优化器，请参考：
- 项目技术文档: `analysis_workspace/OPTIMIZER_SCHEDULER_ANALYSIS.md`
- 示例实现: `src/optimizers/examples/` (如果存在)
- Lightning 文档: [configure_optimizers](https://lightning.ai/docs/pytorch/stable/common/lightning_module.html#configure-optimizers)
