#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
SAM优化器实现
基于论文: Sharpness-Aware Minimization for Efficiently Improving Generalization
论文: https://arxiv.org/abs/2010.01412
"""

import torch
from torch.optim.optimizer import Optimizer
from typing import Any, Dict, Optional, Union, Iterable, Callable


class SAM(Optimizer):
    """
    SAM优化器 - Sharpness-Aware Minimization
    
    SAM是一种寻找平坦最小值的优化器，主要特点：
    - 同时最小化损失值和损失锐度
    - 提高模型的泛化能力
    - 需要两次前向传播和一次反向传播
    - 适合各种基础优化器（AdamW, SGD等）
    
    Args:
        params: 模型参数
        base_optimizer: 基础优化器类
        rho: 扰动半径 (默认: 0.05)
        adaptive: 是否使用自适应扰动 (默认: False)
        **kwargs: 传递给基础优化器的参数
    """
    
    def __init__(
        self,
        params: Iterable[torch.Tensor],
        base_optimizer: Optimizer,
        rho: float = 0.05,
        adaptive: bool = False,
        **kwargs
    ):
        if rho < 0.0:
            raise ValueError(f"Invalid rho value: {rho}")
        
        defaults = dict(rho=rho, adaptive=adaptive, **kwargs)
        super().__init__(params, defaults)
        
        # 创建基础优化器
        self.base_optimizer = base_optimizer(self.param_groups, **kwargs)
        self.param_groups = self.base_optimizer.param_groups
        self.defaults.update(self.base_optimizer.defaults)
    
    @torch.no_grad()
    def first_step(self, zero_grad: bool = False):
        """
        第一步：计算并应用扰动
        
        Args:
            zero_grad: 是否清零梯度
        """
        grad_norm = self._grad_norm()
        for group in self.param_groups:
            scale = group["rho"] / (grad_norm + 1e-12)
            
            for p in group["params"]:
                if p.grad is None:
                    continue
                self.state[p]["old_p"] = p.data.clone()
                e_w = (torch.pow(p, 2) if group["adaptive"] else 1.0) * p.grad * scale.to(p)
                p.add_(e_w)  # 爬到锐度高的点
        
        if zero_grad:
            self.zero_grad()
    
    @torch.no_grad()
    def second_step(self, zero_grad: bool = False):
        """
        第二步：恢复参数并使用基础优化器更新
        
        Args:
            zero_grad: 是否清零梯度
        """
        for group in self.param_groups:
            for p in group["params"]:
                if p.grad is None:
                    continue
                p.data = self.state[p]["old_p"]  # 回到原来的点
        
        self.base_optimizer.step()  # 使用基础优化器更新
        
        if zero_grad:
            self.zero_grad()
    
    @torch.no_grad()
    def step(self, closure: Optional[Callable] = None):
        """
        完整的SAM步骤
        注意：这需要特殊的训练循环来正确使用
        """
        if closure is None:
            raise ValueError("SAM requires a closure function for proper operation")
        
        # 第一步：计算扰动梯度
        closure()
        self.first_step(zero_grad=True)
        
        # 第二步：计算真实梯度并更新
        closure()
        self.second_step()
    
    def _grad_norm(self):
        """计算梯度范数"""
        shared_device = self.param_groups[0]["params"][0].device  # 假设所有参数在同一设备上
        norm = torch.norm(
            torch.stack([
                ((torch.abs(p) if group["adaptive"] else 1.0) * p.grad).norm(dtype=torch.float32)
                for group in self.param_groups for p in group["params"]
                if p.grad is not None
            ]),
            dtype=torch.float32
        )
        return norm.to(shared_device)
    
    def load_state_dict(self, state_dict):
        """加载状态字典"""
        super().load_state_dict(state_dict)
        self.base_optimizer.param_groups = self.param_groups
    
    def zero_grad(self, set_to_none: bool = False):
        """清零梯度"""
        self.base_optimizer.zero_grad(set_to_none)


# 便利函数：创建SAM包装的常用优化器
def SAM_AdamW(params, rho=0.05, adaptive=False, **kwargs):
    """SAM包装的AdamW优化器"""
    from ..standard.adamw import AdamW
    return SAM(params, AdamW, rho=rho, adaptive=adaptive, **kwargs)


def SAM_SGD(params, rho=0.05, adaptive=False, **kwargs):
    """SAM包装的SGD优化器"""
    from ..standard.sgd import SGD
    return SAM(params, SGD, rho=rho, adaptive=adaptive, **kwargs)
