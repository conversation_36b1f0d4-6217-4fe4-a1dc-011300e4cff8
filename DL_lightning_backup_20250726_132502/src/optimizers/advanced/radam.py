#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
RAdam优化器实现
基于论文: On the Variance of the Adaptive Learning Rate and Beyond
论文: https://arxiv.org/abs/1908.03265
"""

import torch
from torch.optim.optimizer import Optimizer
from typing import Any, Dict, Optional, Union, Iterable
import math


class RAdam(Optimizer):
    """
    RAdam优化器 - Rectified Adam
    
    RAdam是对Adam的改进，主要特点：
    - 修正了Adam在训练初期的方差问题
    - 在训练初期使用SGD，后期切换到Adam
    - 提供更稳定的训练过程
    - 减少了对学习率调度的依赖
    
    Args:
        params: 模型参数
        lr: 学习率 (默认: 1e-3)
        betas: RAdam的beta参数 (默认: (0.9, 0.999))
        eps: 数值稳定性参数 (默认: 1e-8)
        weight_decay: 权重衰减 (默认: 0)
        degenerated_to_sgd: 是否在初期退化为SGD (默认: True)
    """
    
    def __init__(
        self,
        params: Iterable[torch.Tensor],
        lr: float = 1e-3,
        betas: tuple = (0.9, 0.999),
        eps: float = 1e-8,
        weight_decay: float = 0,
        degenerated_to_sgd: bool = True,
        **kwargs
    ):
        if not 0.0 <= lr:
            raise ValueError(f"Invalid learning rate: {lr}")
        if not 0.0 <= eps:
            raise ValueError(f"Invalid epsilon value: {eps}")
        if not 0.0 <= betas[0] < 1.0:
            raise ValueError(f"Invalid beta parameter at index 0: {betas[0]}")
        if not 0.0 <= betas[1] < 1.0:
            raise ValueError(f"Invalid beta parameter at index 1: {betas[1]}")
        if not 0.0 <= weight_decay:
            raise ValueError(f"Invalid weight_decay value: {weight_decay}")
        
        defaults = dict(
            lr=lr,
            betas=betas,
            eps=eps,
            weight_decay=weight_decay,
            degenerated_to_sgd=degenerated_to_sgd
        )
        super().__init__(params, defaults)
    
    def step(self, closure: Optional[callable] = None) -> Optional[float]:
        """执行单步优化"""
        loss = None
        if closure is not None:
            with torch.enable_grad():
                loss = closure()
        
        for group in self.param_groups:
            for p in group['params']:
                if p.grad is None:
                    continue
                
                grad = p.grad.data
                if grad.dtype in {torch.float16, torch.bfloat16}:
                    grad = grad.float()
                
                state = self.state[p]
                
                # 状态初始化
                if len(state) == 0:
                    state['step'] = 0
                    state['exp_avg'] = torch.zeros_like(p.data).float()
                    state['exp_avg_sq'] = torch.zeros_like(p.data).float()
                
                exp_avg, exp_avg_sq = state['exp_avg'], state['exp_avg_sq']
                beta1, beta2 = group['betas']
                
                state['step'] += 1
                
                # 权重衰减
                if group['weight_decay'] != 0:
                    grad = grad.add(p.data, alpha=group['weight_decay'])
                
                # 指数移动平均
                exp_avg.mul_(beta1).add_(grad, alpha=1 - beta1)
                exp_avg_sq.mul_(beta2).addcmul_(grad, grad, value=1 - beta2)
                
                # 计算偏差修正
                bias_correction1 = 1 - beta1 ** state['step']
                bias_correction2 = 1 - beta2 ** state['step']
                
                # 计算方差修正的长度
                rho_inf = 2 / (1 - beta2) - 1
                rho_t = rho_inf - 2 * state['step'] * beta2 ** state['step'] / bias_correction2
                
                # 修正的Adam更新
                if rho_t >= 5:
                    # 使用修正的Adam
                    r_t = math.sqrt((rho_t - 4) * (rho_t - 2) * rho_inf / ((rho_inf - 4) * (rho_inf - 2) * rho_t))
                    denom = (exp_avg_sq.sqrt() / math.sqrt(bias_correction2)).add_(group['eps'])
                    step_size = group['lr'] * r_t / bias_correction1
                    p.data.addcdiv_(exp_avg, denom, value=-step_size)
                elif group['degenerated_to_sgd']:
                    # 退化为SGD
                    step_size = group['lr'] / bias_correction1
                    p.data.add_(exp_avg, alpha=-step_size)
        
        return loss


# 为了兼容性，也提供一个别名
RectifiedAdam = RAdam
