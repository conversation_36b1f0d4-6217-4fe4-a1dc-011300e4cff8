#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
LAMB优化器实现
基于论文: Large Batch Optimization for Deep Learning
论文: https://arxiv.org/abs/1904.00962
"""

import torch
from torch.optim.optimizer import Optimizer
from typing import Any, Dict, Optional, Union, Iterable
import math


class LAMB(Optimizer):
    """
    LAMB优化器 - Layer-wise Adaptive Moments optimizer for Batch training
    
    LAMB是专为大批量训练设计的优化器，主要特点：
    - 层级自适应学习率
    - 适合大批量训练
    - 在BERT等大模型训练中表现优异
    - 结合了Adam和LARS的优点
    
    Args:
        params: 模型参数
        lr: 学习率 (默认: 1e-3)
        betas: LAMB的beta参数 (默认: (0.9, 0.999))
        eps: 数值稳定性参数 (默认: 1e-6)
        weight_decay: 权重衰减 (默认: 0.01)
        clamp_value: 信任比率的裁剪值 (默认: 10.0)
        debias: 是否进行偏差修正 (默认: True)
    """
    
    def __init__(
        self,
        params: Iterable[torch.Tensor],
        lr: float = 1e-3,
        betas: tuple = (0.9, 0.999),
        eps: float = 1e-6,
        weight_decay: float = 0.01,
        clamp_value: float = 10.0,
        debias: bool = True,
        **kwargs
    ):
        if not 0.0 <= lr:
            raise ValueError(f"Invalid learning rate: {lr}")
        if not 0.0 <= eps:
            raise ValueError(f"Invalid epsilon value: {eps}")
        if not 0.0 <= betas[0] < 1.0:
            raise ValueError(f"Invalid beta parameter at index 0: {betas[0]}")
        if not 0.0 <= betas[1] < 1.0:
            raise ValueError(f"Invalid beta parameter at index 1: {betas[1]}")
        if not 0.0 <= weight_decay:
            raise ValueError(f"Invalid weight_decay value: {weight_decay}")
        
        defaults = dict(
            lr=lr,
            betas=betas,
            eps=eps,
            weight_decay=weight_decay,
            clamp_value=clamp_value,
            debias=debias
        )
        super().__init__(params, defaults)
    
    def step(self, closure: Optional[callable] = None) -> Optional[float]:
        """执行单步优化"""
        loss = None
        if closure is not None:
            with torch.enable_grad():
                loss = closure()
        
        for group in self.param_groups:
            for p in group['params']:
                if p.grad is None:
                    continue
                
                grad = p.grad.data
                if grad.dtype in {torch.float16, torch.bfloat16}:
                    grad = grad.float()
                
                state = self.state[p]
                
                # 状态初始化
                if len(state) == 0:
                    state['step'] = 0
                    state['exp_avg'] = torch.zeros_like(p.data).float()
                    state['exp_avg_sq'] = torch.zeros_like(p.data).float()
                
                exp_avg, exp_avg_sq = state['exp_avg'], state['exp_avg_sq']
                beta1, beta2 = group['betas']
                
                state['step'] += 1
                
                # 指数移动平均
                exp_avg.mul_(beta1).add_(grad, alpha=1 - beta1)
                exp_avg_sq.mul_(beta2).addcmul_(grad, grad, value=1 - beta2)
                
                # 偏差修正
                if group['debias']:
                    bias_correction1 = 1 - beta1 ** state['step']
                    bias_correction2 = 1 - beta2 ** state['step']
                    
                    exp_avg_corrected = exp_avg / bias_correction1
                    exp_avg_sq_corrected = exp_avg_sq / bias_correction2
                else:
                    exp_avg_corrected = exp_avg
                    exp_avg_sq_corrected = exp_avg_sq
                
                # 计算更新方向
                denom = exp_avg_sq_corrected.sqrt().add_(group['eps'])
                update = exp_avg_corrected / denom
                
                # 权重衰减
                if group['weight_decay'] != 0:
                    update = update.add(p.data, alpha=group['weight_decay'])
                
                # 计算信任比率（LARS风格的层级自适应）
                weight_norm = p.data.norm()
                update_norm = update.norm()
                
                if weight_norm > 0 and update_norm > 0:
                    trust_ratio = weight_norm / update_norm
                    trust_ratio = min(trust_ratio, group['clamp_value'])
                else:
                    trust_ratio = 1.0
                
                # 应用更新
                step_size = group['lr'] * trust_ratio
                p.data.add_(update, alpha=-step_size)
        
        return loss
