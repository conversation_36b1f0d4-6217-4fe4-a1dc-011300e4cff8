#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
AdaBelief优化器实现
基于论文: AdaBelief Optimizer: Adapting Stepsizes by the Belief in Observed Gradients
论文: https://arxiv.org/abs/2010.07468
"""

import torch
from torch.optim.optimizer import Optimizer
from typing import Any, Dict, Optional, Union, Iterable
import math


class AdaBelief(Optimizer):
    """
    AdaBelief优化器
    
    AdaBelief是对Adam的改进，主要特点：
    - 使用梯度的"信念"（belief）来调整步长
    - 在梯度方向一致时加速，不一致时减速
    - 在很多任务上比Adam表现更好
    - 特别适合深度学习和遥感任务
    
    Args:
        params: 模型参数
        lr: 学习率 (默认: 1e-3)
        betas: AdaBelief的beta参数 (默认: (0.9, 0.999))
        eps: 数值稳定性参数 (默认: 1e-16)
        weight_decay: 权重衰减 (默认: 0)
        amsgrad: 是否使用AMSGrad变体 (默认: False)
        weight_decouple: 是否使用解耦权重衰减 (默认: True)
        fixed_decay: 是否使用固定衰减 (默认: False)
        rectify: 是否使用RAdam风格的修正 (默认: True)
        maximize: 是否最大化目标函数 (默认: False)
    """
    
    def __init__(
        self,
        params: Iterable[torch.Tensor],
        lr: float = 1e-3,
        betas: tuple = (0.9, 0.999),
        eps: float = 1e-16,
        weight_decay: float = 0,
        amsgrad: bool = False,
        weight_decouple: bool = True,
        fixed_decay: bool = False,
        rectify: bool = True,
        maximize: bool = False,
        **kwargs
    ):
        if not 0.0 <= lr:
            raise ValueError(f"Invalid learning rate: {lr}")
        if not 0.0 <= eps:
            raise ValueError(f"Invalid epsilon value: {eps}")
        if not 0.0 <= betas[0] < 1.0:
            raise ValueError(f"Invalid beta parameter at index 0: {betas[0]}")
        if not 0.0 <= betas[1] < 1.0:
            raise ValueError(f"Invalid beta parameter at index 1: {betas[1]}")
        if not 0.0 <= weight_decay:
            raise ValueError(f"Invalid weight_decay value: {weight_decay}")
        
        defaults = dict(
            lr=lr,
            betas=betas,
            eps=eps,
            weight_decay=weight_decay,
            amsgrad=amsgrad,
            weight_decouple=weight_decouple,
            fixed_decay=fixed_decay,
            rectify=rectify,
            maximize=maximize
        )
        super().__init__(params, defaults)
    
    def __setstate__(self, state):
        super().__setstate__(state)
        for group in self.param_groups:
            group.setdefault('amsgrad', False)
            group.setdefault('weight_decouple', True)
            group.setdefault('fixed_decay', False)
            group.setdefault('rectify', True)
            group.setdefault('maximize', False)
    
    def step(self, closure: Optional[callable] = None) -> Optional[float]:
        """执行单步优化"""
        loss = None
        if closure is not None:
            with torch.enable_grad():
                loss = closure()
        
        for group in self.param_groups:
            for p in group['params']:
                if p.grad is None:
                    continue
                
                grad = p.grad.data if not group['maximize'] else -p.grad.data
                if grad.dtype in {torch.float16, torch.bfloat16}:
                    grad = grad.float()
                
                state = self.state[p]
                
                # 状态初始化
                if len(state) == 0:
                    state['step'] = 0
                    state['exp_avg'] = torch.zeros_like(p.data).float()
                    state['exp_avg_sq'] = torch.zeros_like(p.data).float()
                    if group['amsgrad']:
                        state['max_exp_avg_sq'] = torch.zeros_like(p.data).float()
                
                exp_avg, exp_avg_sq = state['exp_avg'], state['exp_avg_sq']
                if group['amsgrad']:
                    max_exp_avg_sq = state['max_exp_avg_sq']
                beta1, beta2 = group['betas']
                
                state['step'] += 1
                bias_correction1 = 1 - beta1 ** state['step']
                bias_correction2 = 1 - beta2 ** state['step']
                
                # 权重衰减
                if group['weight_decay'] != 0:
                    if group['weight_decouple']:
                        # 解耦权重衰减（AdamW风格）
                        p.data.mul_(1 - group['lr'] * group['weight_decay'])
                    else:
                        # L2正则化风格
                        grad = grad.add(p.data, alpha=group['weight_decay'])
                
                # 指数移动平均
                exp_avg.mul_(beta1).add_(grad, alpha=1 - beta1)
                
                # AdaBelief的核心：使用梯度与动量的差异
                grad_residual = grad - exp_avg
                exp_avg_sq.mul_(beta2).addcmul_(grad_residual, grad_residual, value=1 - beta2)
                
                if group['amsgrad']:
                    torch.max(max_exp_avg_sq, exp_avg_sq, out=max_exp_avg_sq)
                    denom = (max_exp_avg_sq.sqrt() / math.sqrt(bias_correction2)).add_(group['eps'])
                else:
                    denom = (exp_avg_sq.sqrt() / math.sqrt(bias_correction2)).add_(group['eps'])
                
                # 学习率修正（RAdam风格）
                if group['rectify']:
                    # 计算方差修正的长度
                    rho_inf = 2 / (1 - beta2) - 1
                    rho_t = rho_inf - 2 * state['step'] * beta2 ** state['step'] / bias_correction2
                    
                    if rho_t >= 5:
                        # 方差修正
                        r_t = math.sqrt((rho_t - 4) * (rho_t - 2) * rho_inf / ((rho_inf - 4) * (rho_inf - 2) * rho_t))
                        step_size = group['lr'] * r_t / bias_correction1
                    else:
                        # 只使用动量
                        step_size = group['lr'] / bias_correction1
                        denom = torch.ones_like(denom)
                else:
                    step_size = group['lr'] / bias_correction1
                
                # 参数更新
                p.data.addcdiv_(exp_avg, denom, value=-step_size)
        
        return loss
