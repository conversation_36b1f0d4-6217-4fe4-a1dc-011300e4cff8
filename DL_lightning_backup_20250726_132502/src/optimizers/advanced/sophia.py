#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Sophia优化器实现
基于论文: Sophia: A Scalable Stochastic Second-order Optimizer for Language Model Pre-training
论文: https://arxiv.org/abs/2305.14342
"""

import torch
from torch.optim.optimizer import Optimizer
from typing import Any, Dict, Optional, Union, Iterable
import math


class Sophia(Optimizer):
    """
    Sophia优化器 - Scalable Stochastic Second-order Optimizer
    
    Sophia是专为大语言模型预训练设计的二阶优化器，主要特点：
    - 使用Hessian对角线的轻量级估计
    - 比Adam更快的收敛速度
    - 内存开销与Adam相当
    - 特别适合Transformer架构
    
    Args:
        params: 模型参数
        lr: 学习率 (默认: 1e-4)
        betas: Sophia的beta参数 (默认: (0.965, 0.99))
        rho: 裁剪阈值 (默认: 0.04)
        weight_decay: 权重衰减 (默认: 1e-1)
        maximize: 是否最大化目标函数 (默认: False)
        capturable: 是否支持CUDA图捕获 (默认: False)
    """
    
    def __init__(
        self,
        params: Iterable[torch.Tensor],
        lr: float = 1e-4,
        betas: tuple = (0.965, 0.99),
        rho: float = 0.04,
        weight_decay: float = 1e-1,
        maximize: bool = False,
        capturable: bool = False,
        **kwargs
    ):
        if not 0.0 <= lr:
            raise ValueError(f"Invalid learning rate: {lr}")
        if not 0.0 <= betas[0] < 1.0:
            raise ValueError(f"Invalid beta parameter at index 0: {betas[0]}")
        if not 0.0 <= betas[1] < 1.0:
            raise ValueError(f"Invalid beta parameter at index 1: {betas[1]}")
        if not 0.0 <= weight_decay:
            raise ValueError(f"Invalid weight_decay value: {weight_decay}")
        if not 0.0 <= rho:
            raise ValueError(f"Invalid rho value: {rho}")
        
        defaults = dict(
            lr=lr,
            betas=betas,
            rho=rho,
            weight_decay=weight_decay,
            maximize=maximize,
            capturable=capturable
        )
        super().__init__(params, defaults)
    
    def __setstate__(self, state):
        super().__setstate__(state)
        for group in self.param_groups:
            group.setdefault('maximize', False)
            group.setdefault('capturable', False)
    
    def step(self, closure: Optional[callable] = None, bs: int = 5120) -> Optional[float]:
        """
        执行单步优化
        
        Args:
            closure: 闭包函数
            bs: 批量大小，用于Hessian估计
        """
        loss = None
        if closure is not None:
            with torch.enable_grad():
                loss = closure()
        
        for group in self.param_groups:
            params_with_grad = []
            grads = []
            exp_avgs = []
            exp_hessian_diag_sqs = []
            state_steps = []
            
            beta1, beta2 = group['betas']
            
            for p in group['params']:
                if p.grad is None:
                    continue
                params_with_grad.append(p)
                
                if p.grad.dtype in {torch.float16, torch.bfloat16}:
                    grads.append(p.grad)
                else:
                    grads.append(p.grad)
                
                state = self.state[p]
                
                # 延迟状态初始化
                if len(state) == 0:
                    state['step'] = torch.zeros((1,), dtype=torch.float, device=p.device) \
                        if group['capturable'] else torch.tensor(0.)
                    state['exp_avg'] = torch.zeros_like(p, memory_format=torch.preserve_format)
                    state['exp_hessian_diag_sq'] = torch.zeros_like(p, memory_format=torch.preserve_format)
                
                exp_avgs.append(state['exp_avg'])
                exp_hessian_diag_sqs.append(state['exp_hessian_diag_sq'])
                
                if group['capturable']:
                    state_steps.append(state['step'])
                else:
                    state_steps.append(state['step'])
            
            # 执行Sophia更新
            self._sophia_update(
                params_with_grad,
                grads,
                exp_avgs,
                exp_hessian_diag_sqs,
                state_steps,
                beta1=beta1,
                beta2=beta2,
                lr=group['lr'],
                weight_decay=group['weight_decay'],
                rho=group['rho'],
                maximize=group['maximize'],
                capturable=group['capturable'],
                bs=bs
            )
        
        return loss
    
    def _sophia_update(
        self,
        params,
        grads,
        exp_avgs,
        exp_hessian_diag_sqs,
        state_steps,
        *,
        beta1: float,
        beta2: float,
        lr: float,
        weight_decay: float,
        rho: float,
        maximize: bool,
        capturable: bool,
        bs: int
    ):
        """Sophia更新逻辑"""
        for i, param in enumerate(params):
            grad = grads[i] if not maximize else -grads[i]
            exp_avg = exp_avgs[i]
            exp_hessian_diag_sq = exp_hessian_diag_sqs[i]
            step_t = state_steps[i]
            
            if capturable:
                assert param.is_cuda and step_t.is_cuda, "If capturable=True, params and state_steps must be CUDA tensors."
            
            # 更新步数
            if capturable:
                step_t += 1
                step = step_t
            else:
                step_t += 1
                step = step_t.item()
            
            # 权重衰减
            if weight_decay != 0:
                param.mul_(1 - lr * weight_decay)
            
            # 更新梯度的指数移动平均
            exp_avg.mul_(beta1).add_(grad, alpha=1 - beta1)
            
            # 估计Hessian对角线
            if capturable:
                hessian_diag = grad * grad / bs
            else:
                hessian_diag = grad * grad / bs
            
            # 更新Hessian对角线的指数移动平均
            exp_hessian_diag_sq.mul_(beta2).add_(hessian_diag, alpha=1 - beta2)
            
            # 偏差修正
            if capturable:
                bias_correction1 = 1 - beta1 ** step
                bias_correction2 = 1 - beta2 ** step
            else:
                bias_correction1 = 1 - beta1 ** step
                bias_correction2 = 1 - beta2 ** step
            
            # 计算预条件梯度
            exp_avg_corrected = exp_avg / bias_correction1
            exp_hessian_diag_sq_corrected = exp_hessian_diag_sq / bias_correction2
            
            # Sophia的核心更新：使用Hessian对角线进行预条件
            k = exp_hessian_diag_sq_corrected.clamp_(min=1e-8)
            u = exp_avg_corrected / k.sqrt()
            
            # 裁剪更新
            u = u.clamp_(-rho, rho)
            
            # 参数更新
            param.add_(u, alpha=-lr)


# 为了兼容性，也提供一个别名
SophiaG = Sophia
