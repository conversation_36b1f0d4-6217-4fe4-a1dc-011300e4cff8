"""
优化器模块 - 集中式实现
所有优化器实现都在这里，通过Hydra的_target_直接访问

使用方式:
在配置文件中直接使用类路径，例如：
_target_: src.optimizers.standard.AdamW
_target_: src.optimizers.advanced.Lion
_target_: src.optimizers.remote_sensing.RemoteSensingAdamW
"""

# 标准优化器
from .standard.adamw import AdamW
from .standard.sgd import SGD
from .standard.adam import Adam
from .standard.rmsprop import RMSprop
from .standard.adagrad import Adagrad

# 先进优化器
from .advanced.lion import Lion
from .advanced.adabelief import AdaBelief
from .advanced.radam import RAdam
from .advanced.lamb import LAMB
from .advanced.sam import SAM
from .advanced.sophia import Sophia

# 遥感专用优化器
from .remote_sensing.multi_scale_optimizer import MultiScaleOptimizer
from .remote_sensing.class_balanced_optimizer import ClassBalancedOptimizer
from .examples.remote_sensing_adamw import RemoteSensingAdamW

__all__ = [
    # 标准优化器
    'AdamW', 'SGD', 'Adam', 'RMSprop', 'Adagrad',
    # 先进优化器
    'Lion', 'AdaBelief', 'RAdam', 'LAMB', 'SAM', 'Sophia',
    # 遥感专用
    'RemoteSensingAdamW', 'MultiScaleOptimizer', 'ClassBalancedOptimizer'
]