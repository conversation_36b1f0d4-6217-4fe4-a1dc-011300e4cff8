"""
交叉熵损失函数 - Lightning版本
支持动态类别权重更新的交叉熵损失
适配PyTorch Lightning 2.3+ 和 Hydra 1.3.2+
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Union, Tuple

from .base import BaseLoss


class CrossEntropyLoss(BaseLoss):
    """
    交叉熵损失函数 - Lightning版本
    
    支持动态类别权重更新的交叉熵损失，适用于多类别分割任务
    """
    
    def __init__(
        self, 
        num_classes: int = 14, 
        ignore_index: int = 255, 
        weight: Optional[torch.Tensor] = None, 
        reduction: str = 'mean', 
        use_dynamic_weights: bool = True, 
        weight_method: str = 'inverse', 
        label_smoothing: float = 0.0
    ):
        """
        初始化交叉熵损失
        
        Args:
            num_classes: 类别数量
            ignore_index: 忽略的标签值
            weight: 初始类别权重
            reduction: 损失聚合方式 ('mean', 'sum', 'none')
            use_dynamic_weights: 是否使用动态类别权重
            weight_method: 权重计算方法 ('inverse', 'sqrt_inverse', 'log_inverse')
            label_smoothing: 标签平滑系数
        """
        super().__init__()
        self.num_classes = num_classes
        self.ignore_index = ignore_index
        self.reduction = reduction
        self.supports_class_weights = use_dynamic_weights
        self.weights = weight
        self.weight_method = weight_method
        self.label_smoothing = label_smoothing
        
        self.loss_fn = nn.CrossEntropyLoss(
            weight=weight,
            ignore_index=ignore_index,
            reduction=reduction,
            label_smoothing=label_smoothing
        )
        
        # 记录初始化信息
        self.logger.info(f"交叉熵损失: 类别数={num_classes}, 忽略索引={ignore_index}")
        
        if weight is not None:
            self.logger.info(f"初始类别权重: {weight}")
        else:
            self.logger.info("初始化无类别权重")
            
        if use_dynamic_weights:
            self.logger.info(f"启用动态类别权重更新, 方法: {weight_method}")
    
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        计算交叉熵损失
        
        Args:
            inputs: 模型输出，形状为 (N, C, H, W)
            targets: 真实标签，形状为 (N, H, W)
            
        Returns:
            torch.Tensor: 交叉熵损失值
        """
        self.last_loss = self.loss_fn(inputs, targets)
        return self.last_loss
    
    def update_weights(
        self, 
        class_counts: torch.Tensor, 
        epoch: Optional[int] = None, 
        alpha: float = 0.8
    ) -> bool:
        """
        更新类别权重
        
        Args:
            class_counts: 类别统计数据，形状为 (num_classes,)
            epoch: 当前训练轮次
            alpha: 指数移动平均系数
            
        Returns:
            bool: 是否成功更新权重
        """
        if not self.supports_class_weights:
            return False
        
        # 获取当前设备
        device = next(self.parameters()).device if list(self.parameters()) else torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 确保类别计数在正确设备上
        if device.type != 'cpu' and class_counts.device.type == 'cpu':
            class_counts = class_counts.to(device)
        
        # 计算新的权重
        new_weights = self._calculate_class_weights(class_counts)
        new_weights = new_weights.to(device)
        
        # 记录更新前的权重状态
        old_weight_info = self.get_weight_info()
        
        # 如果已经有权重，使用指数移动平均更新
        if hasattr(self, 'weights') and self.weights is not None:
            self.weights = alpha * self.weights + (1 - alpha) * new_weights
        else:
            self.weights = new_weights
        
        # 更新损失函数
        self.loss_fn = nn.CrossEntropyLoss(
            weight=self.weights,
            ignore_index=self.ignore_index,
            reduction=self.reduction,
            label_smoothing=self.label_smoothing
        )
        
        # 记录更新后的权重状态
        new_weight_info = self.get_weight_info()
        
        # 记录日志
        self.logger.info(f"交叉熵损失类别权重更新: {old_weight_info} -> {new_weight_info}")
        if epoch is not None:
            self.logger.info(f"Epoch {epoch}: 交叉熵损失类别权重已更新")
        
        # 保存最后更新的轮次
        self.last_update_epoch = epoch
        return True
    
    def _calculate_class_weights(self, class_counts: torch.Tensor, max_ratio: float = 10.0) -> torch.Tensor:
        """
        计算类别权重
        
        Args:
            class_counts: 类别统计数据
            max_ratio: 最大权重比例，防止权重过大
            
        Returns:
            torch.Tensor: 计算得到的类别权重
        """
        # 避免除零
        class_counts = class_counts.float()
        class_counts = torch.clamp(class_counts, min=1.0)
        
        if self.weight_method == 'inverse':
            # 反比例权重
            total_samples = class_counts.sum()
            weights = total_samples / (self.num_classes * class_counts)
        elif self.weight_method == 'sqrt_inverse':
            # 平方根反比例权重
            total_samples = class_counts.sum()
            weights = torch.sqrt(total_samples / (self.num_classes * class_counts))
        elif self.weight_method == 'log_inverse':
            # 对数反比例权重
            weights = torch.log(class_counts.sum() / class_counts)
        else:
            # 默认使用反比例
            total_samples = class_counts.sum()
            weights = total_samples / (self.num_classes * class_counts)
        
        # 限制权重比例，防止权重过大
        min_weight = weights.min()
        max_weight = min_weight * max_ratio
        weights = torch.clamp(weights, max=max_weight)
        
        # 归一化权重
        weights = weights / weights.sum() * self.num_classes
        
        return weights
    
    def get_losses(self) -> Union[float, Tuple[float, ...], None]:
        """获取分项损失"""
        if hasattr(self, 'last_loss'):
            if self.last_loss.dim() == 0:  # scalar
                return self.last_loss.item()
            else:  # per-sample losses
                return tuple(self.last_loss.detach().cpu().tolist())
        return None
    
    def get_weight_info(self) -> str:
        """获取权重信息"""
        if not hasattr(self, 'weights') or self.weights is None:
            return "未初始化权重"
        
        weights = self.weights.detach().cpu()
        return f"权重范围: [{weights.min():.4f}, {weights.max():.4f}], 均值: {weights.mean():.4f}"
