"""
Dice损失函数 - Lightning版本
用于语义分割的基于区域重叠的损失函数，不直接支持类别权重
适配PyTorch Lightning 2.3+ 和 Hydra 1.3.2+
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Union, Tuple

from .base import BaseLoss


class DiceLoss(BaseLoss):
    """
    Dice损失函数 - Lightning版本

    基于Dice系数的损失函数，特别适用于语义分割任务中的不平衡数据
    不支持类别权重，但提供完整的数值一致性保证
    """

    def __init__(
        self,
        num_classes: int = 14,
        ignore_index: int = 255,
        smooth: float = 1.0,
        per_class: bool = False,
        square: bool = False,
        reduction: str = 'mean'
    ):
        """
        初始化Dice损失

        Args:
            num_classes: 类别数量
            ignore_index: 忽略的标签值
            smooth: 平滑参数，防止除零
            per_class: 是否返回每个类别的损失
            square: 是否对预测和目标进行平方
            reduction: 损失聚合方式 ('mean', 'sum', 'none')
        """
        super().__init__()
        self.num_classes = num_classes
        self.ignore_index = ignore_index
        self.smooth = smooth
        self.per_class = per_class
        self.square = square
        self.reduction = reduction
        self.supports_class_weights = False

        self.logger.info(f"Dice损失: 类别数={num_classes}, 平滑参数={smooth}, 忽略索引={ignore_index}")
        self.logger.info("Dice损失不支持类别权重")

    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        计算Dice损失

        Args:
            inputs: 模型输出，形状为 (N, C, H, W)
            targets: 真实标签，形状为 (N, H, W)

        Returns:
            torch.Tensor: Dice损失值
        """
        # 使用sigmoid激活函数（与原始成功版本保持一致）
        inputs = torch.sigmoid(inputs)

        # 存储每个类别的Dice系数
        dice_coeffs = []

        # 计算每个类别的Dice系数
        for cls in range(self.num_classes):
            # 获取当前类别的预测和真实标签
            target_cls = (targets == cls).float()
            input_cls = inputs[:, cls]

            # 忽略指定的索引
            if self.ignore_index is not None:
                mask = (targets != self.ignore_index).float()
                target_cls = target_cls * mask
                input_cls = input_cls * mask

            # 如果启用square选项
            if self.square:
                input_cls = input_cls ** 2
                target_cls = target_cls ** 2

            # 计算交集和并集
            intersection = (input_cls * target_cls).sum()
            union = input_cls.sum() + target_cls.sum()

            # 计算Dice系数
            dice_coeff = (2. * intersection + self.smooth) / (union + self.smooth)
            dice_coeffs.append(dice_coeff)

        # 计算平均Dice损失
        dice_coeffs = torch.stack(dice_coeffs)
        self.last_dice_per_class = 1.0 - dice_coeffs

        # 应用reduction
        if self.reduction == 'mean':
            self.last_loss = 1.0 - dice_coeffs.mean()
        elif self.reduction == 'sum':
            self.last_loss = (1.0 - dice_coeffs).sum()
        else:  # 'none'
            self.last_loss = 1.0 - dice_coeffs

        if self.per_class:
            return self.last_dice_per_class
        else:
            return self.last_loss

    def get_losses(self) -> Union[float, Tuple[float, ...], None]:
        """获取分项损失"""
        if hasattr(self, 'last_loss'):
            if self.last_loss.dim() == 0:  # scalar
                return self.last_loss.item()
            else:  # per-class losses
                return tuple(self.last_loss.detach().cpu().tolist())
        return None

    def get_per_class_loss(self) -> Optional[list]:
        """获取每个类别的损失"""
        if hasattr(self, 'last_dice_per_class'):
            return self.last_dice_per_class.detach().cpu().tolist()
        return None

    def get_weight_info(self) -> str:
        """获取权重信息"""
        return f"Dice损失 - 类别数: {self.num_classes}, 平滑参数: {self.smooth}, 不支持类别权重"


class CEDiceLoss(nn.Module):
    """
    交叉熵损失和Dice损失的组合 - Lightning版本
    这种组合通常比单独使用任何一种损失函数在分割任务中效果更好
    """
    def __init__(self, ce_weight: float = 0.5, dice_weight: float = 0.5, class_weights=None):
        super(CEDiceLoss, self).__init__()
        self.ce_weight = ce_weight
        self.dice_weight = dice_weight
        self.ce_loss = nn.CrossEntropyLoss(weight=class_weights)
        self.dice_loss = DiceLoss()

    def forward(self, logits: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        ce_loss_val = self.ce_loss(logits, targets)
        dice_loss_val = self.dice_loss(logits, targets)

        return self.ce_weight * ce_loss_val + self.dice_weight * dice_loss_val
