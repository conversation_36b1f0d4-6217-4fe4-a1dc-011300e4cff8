"""
Lovasz Loss实现 - Lightning版本
Lovasz loss是针对IoU优化的分割损失函数，
特别适合处理类别不平衡的语义分割问题。
适配PyTorch Lightning 2.3+ 和 Hydra 1.3.2+

参考文献:
'The Lovász-Softmax Loss: A Tractable Surrogate for the Optimization of the IoU Measure in Neural Networks'
https://arxiv.org/abs/1705.08790
"""

import logging
from typing import Dict, Optional, Tuple, Union

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F

from .base import BaseLoss


def lovasz_grad(gt_sorted: torch.Tensor) -> torch.Tensor:
    """
    计算Lovasz扩展的梯度
    
    Args:
        gt_sorted: 排序后的真实标签
        
    Returns:
        torch.Tensor: 计算得到的梯度
    """
    p = len(gt_sorted)
    gts = gt_sorted.sum()
    intersection = gts - gt_sorted.float().cumsum(0)
    union = gts + (1 - gt_sorted).float().cumsum(0)
    jaccard = 1. - intersection / union
    if p > 1:  # 如果存在p个prediction
        jaccard[0] = 1. - gt_sorted[0]  # 第一个值的准确率
        jaccard[1:p] = jaccard[1:p] - jaccard[0:p-1]  # 其余的延迟率
    return jaccard


def lovasz_softmax_flat(
    prob: torch.Tensor, 
    labels: torch.Tensor, 
    classes: str = 'present', 
    per_image: bool = False, 
    ignore: Optional[int] = None
) -> torch.Tensor:
    """
    计算Lovasz-Softmax损失的多类别版本
    
    Args:
        prob: [P, C] 概率变量, 其中P是像素数，C是类别数
        labels: [P] 标签
        classes: 'all' 所有类别, 'present' 只计算出现在图像/批次中的类别
        per_image: 对每个图像分别计算
        ignore: 忽略的标签索引
        
    Returns:
        torch.Tensor: 计算得到的损失值
    """
    if per_image:
        loss = 0
        for prob_i, label_i in zip(prob, labels):
            loss += lovasz_softmax_flat(prob_i.unsqueeze(0), label_i.unsqueeze(0), 
                                        classes, False, ignore)
        return loss / len(prob)
    
    # 最大化probabilities的维度为[P, C]
    if prob.dim() == 3:
        prob = prob.reshape(-1, prob.size(2))
    if labels.dim() == 3:
        labels = labels.reshape(-1)
    
    # 过滤掉需要忽略的像素
    if ignore is not None:
        valid = (labels != ignore)
        prob = prob[valid]
        labels = labels[valid]
    
    # 获取需要计算的类别
    if classes == 'present':
        classes = torch.unique(labels)
    
    loss = 0
    for c in classes:
        if c == ignore:
            continue
        # 计算真值前景/背景掩码
        fg = (labels == c).float()
        if fg.sum() == 0:
            continue
        
        # 获取当前类别的概率
        class_prob = prob[:, c]
        
        # 计算错误
        errors = (fg - class_prob).abs()
        
        # 根据错误排序和计算梯度
        errors_sorted, perm = torch.sort(errors, dim=0, descending=True)
        perm = perm.data
        fg_sorted = fg[perm]
        grad = lovasz_grad(fg_sorted)
        
        # 计算总损失
        loss += torch.dot(F.relu(errors_sorted), grad)
    
    return loss / classes.shape[0]


class LovaszLoss(BaseLoss):
    """
    Lovasz损失函数类 - Lightning版本
    用于语义分割的IoU优化
    
    特点：
    - 针对IoU度量进行优化
    - 支持多类别分割
    - 适合类别不平衡的数据集
    - 支持忽略特定类别
    - 不支持类别权重
    """
    
    def __init__(
        self, 
        num_classes: Optional[int] = None,
        ignore_index: int = 255,
        weight: Optional[torch.Tensor] = None,
        per_image: bool = True,
        reduction: str = 'mean'
    ):
        """
        初始化Lovasz损失
        
        Args:
            num_classes: 类别数量
            ignore_index: 忽略的标签索引
            weight: 类别权重（Lovasz loss不使用）
            per_image: 是否对每个图像单独计算损失
            reduction: 聚合方式，支持'mean'和'sum'
        """
        super().__init__()
        self.num_classes = num_classes
        self.ignore_index = ignore_index
        self.per_image = per_image
        self.reduction = reduction
        self.supports_class_weights = False
        
        # Lovasz loss不支持类别权重
        if weight is not None:
            self.logger.warning("Lovasz损失不支持类别权重，权重参数将被忽略")
        
        self.logger.info(f"Lovasz损失: 类别数={num_classes}, 每图像计算={per_image}, 忽略索引={ignore_index}")
    
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        计算Lovasz损失
        
        Args:
            inputs: 预测的logits张量，形状为[B, C, H, W]
            targets: 目标标签张量，形状为[B, H, W]
            
        Returns:
            torch.Tensor: 计算得到的损失值
        """
        # 确保输入格式正确
        assert inputs.dim() == 4, f"预期inputs维度为4，得到的是{inputs.dim()}"
        assert targets.dim() == 3, f"预期targets维度为3，得到的是{targets.dim()}"
        assert inputs.size(0) == targets.size(0), "batch size不匹配"
        
        # 使用softmax获取概率
        probs = F.softmax(inputs, dim=1)
        
        # 计算lovasz损失
        if self.per_image:
            # 变换为[B, H*W, C]和[B, H*W]
            B, C, H, W = inputs.shape
            probs = probs.permute(0, 2, 3, 1).contiguous().view(B, H*W, C)
            targets = targets.view(B, H*W)
            
            losses = []
            for prob, tgt in zip(probs, targets):
                losses.append(lovasz_softmax_flat(prob, tgt, 
                                               classes='present', 
                                               per_image=False, 
                                               ignore=self.ignore_index))
            loss = torch.stack(losses)
        else:
            # 直接处理整批次
            loss = lovasz_softmax_flat(probs.permute(0, 2, 3, 1).reshape(-1, inputs.size(1)), 
                                     targets.reshape(-1), 
                                     classes='present',
                                     per_image=False,
                                     ignore=self.ignore_index)
        
        # 应用reduction
        if self.reduction == 'mean':
            self.last_loss = loss.mean()
        elif self.reduction == 'sum':
            self.last_loss = loss.sum()
        else:
            self.last_loss = loss
            
        return self.last_loss
    
    def get_losses(self) -> Union[float, Tuple[float, ...], None]:
        """获取分项损失"""
        if hasattr(self, 'last_loss'):
            if self.last_loss.dim() == 0:  # scalar
                return self.last_loss.item()
            else:  # per-image losses
                return tuple(self.last_loss.detach().cpu().tolist())
        return None
    
    def get_weight_info(self) -> str:
        """获取权重信息"""
        return "Lovasz损失不支持类别权重"
    
    def update_weights(
        self, 
        class_counts: torch.Tensor, 
        epoch: Optional[int] = None, 
        alpha: float = 0.8
    ) -> bool:
        """
        更新权重（空实现，因为不支持类别权重）
        
        Args:
            class_counts: 类别计数
            epoch: 当前训练轮次
            alpha: 指数移动平均系数
            
        Returns:
            bool: 始终返回False，因为不支持类别权重
        """
        return False
