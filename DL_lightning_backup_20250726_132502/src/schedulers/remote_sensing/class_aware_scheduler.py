#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
类别感知调度器实现
专为遥感图像分割中的类别不平衡问题设计的学习率调度器
"""

import torch
from torch.optim.lr_scheduler import _LRScheduler
from typing import List, Optional, Dict
import warnings
import math


class ClassAwareScheduler(_LRScheduler):
    """
    类别感知调度器
    
    专为遥感图像分割中的类别不平衡问题设计，主要特点：
    - 根据类别性能动态调整学习率
    - 对难分类别给予更多关注
    - 基于类别损失的自适应调整
    - 支持多种基础调度策略
    
    Args:
        optimizer: 优化器
        base_scheduler: 基础调度器类型 ('cosine', 'step', 'poly') (默认: 'cosine')
        T_max: 最大迭代次数 (用于cosine调度器)
        step_size: 步长 (用于step调度器)
        total_iters: 总迭代次数 (用于poly调度器)
        class_weights: 类别权重 (默认: None, 自动计算)
        adaptation_factor: 自适应因子 (默认: 0.1)
        min_lr_factor: 最小学习率因子 (默认: 0.1)
        max_lr_factor: 最大学习率因子 (默认: 2.0)
        last_epoch: 上一个epoch (默认: -1)
        verbose: 是否打印学习率变化 (默认: False)
    """
    
    def __init__(
        self,
        optimizer: torch.optim.Optimizer,
        base_scheduler: str = 'cosine',
        T_max: Optional[int] = None,
        step_size: Optional[int] = None,
        total_iters: Optional[int] = None,
        class_weights: Optional[List[float]] = None,
        adaptation_factor: float = 0.1,
        min_lr_factor: float = 0.1,
        max_lr_factor: float = 2.0,
        last_epoch: int = -1,
        verbose: bool = False
    ):
        self.base_scheduler = base_scheduler
        self.T_max = T_max or 100
        self.step_size = step_size or 30
        self.total_iters = total_iters or 100
        self.class_weights = class_weights
        self.adaptation_factor = adaptation_factor
        self.min_lr_factor = min_lr_factor
        self.max_lr_factor = max_lr_factor
        
        # 类别性能统计
        self.class_performance = {}
        self.global_step = 0
        self.adaptation_history = []
        
        super().__init__(optimizer, last_epoch, verbose)
    
    def get_lr(self) -> List[float]:
        """计算类别感知学习率"""
        if not self._get_lr_called_within_step:
            warnings.warn("To get the last learning rate computed by the scheduler, "
                         "please use `get_last_lr()`.", UserWarning)
        
        # 计算基础学习率
        base_lrs = self._get_base_lr()
        
        # 计算类别自适应因子
        adaptation_factor = self._compute_adaptation_factor()
        
        # 应用自适应因子
        adapted_lrs = []
        for base_lr in base_lrs:
            adapted_lr = base_lr * adaptation_factor
            # 限制学习率范围
            adapted_lr = max(base_lr * self.min_lr_factor, 
                           min(base_lr * self.max_lr_factor, adapted_lr))
            adapted_lrs.append(adapted_lr)
        
        return adapted_lrs
    
    def _get_base_lr(self) -> List[float]:
        """根据基础调度器计算学习率"""
        if self.base_scheduler == 'cosine':
            return self._cosine_lr()
        elif self.base_scheduler == 'step':
            return self._step_lr()
        elif self.base_scheduler == 'poly':
            return self._poly_lr()
        else:
            return [group['lr'] for group in self.optimizer.param_groups]
    
    def _cosine_lr(self) -> List[float]:
        """余弦退火学习率"""
        if self.last_epoch == 0:
            return self.base_lrs
        
        return [base_lr * (1 + math.cos(math.pi * self.last_epoch / self.T_max)) / 2
                for base_lr in self.base_lrs]
    
    def _step_lr(self) -> List[float]:
        """阶梯学习率"""
        if (self.last_epoch == 0) or (self.last_epoch % self.step_size != 0):
            return [group['lr'] for group in self.optimizer.param_groups]
        return [group['lr'] * 0.1 for group in self.optimizer.param_groups]
    
    def _poly_lr(self) -> List[float]:
        """多项式学习率"""
        if self.last_epoch == 0 or self.last_epoch > self.total_iters:
            return [group['lr'] for group in self.optimizer.param_groups]
        
        decay_factor = (1 - self.last_epoch / self.total_iters) ** 1.0
        return [base_lr * decay_factor for base_lr in self.base_lrs]
    
    def _compute_adaptation_factor(self) -> float:
        """计算类别自适应因子"""
        if not self.class_performance:
            return 1.0
        
        # 计算类别性能的方差
        performances = list(self.class_performance.values())
        if len(performances) < 2:
            return 1.0
        
        mean_performance = sum(performances) / len(performances)
        variance = sum((p - mean_performance) ** 2 for p in performances) / len(performances)
        
        # 基于方差计算自适应因子
        # 方差大说明类别不平衡严重，需要更大的学习率调整
        adaptation = 1.0 + self.adaptation_factor * math.sqrt(variance)
        
        # 记录自适应历史
        self.adaptation_history.append(adaptation)
        if len(self.adaptation_history) > 100:  # 保持最近100个记录
            self.adaptation_history.pop(0)
        
        return adaptation
    
    def update_class_performance(self, class_metrics: Dict[int, float]):
        """
        更新类别性能统计
        
        Args:
            class_metrics: 类别性能指标 {class_id: performance_score}
                          performance_score可以是IoU、F1-score等，值越高表示性能越好
        """
        self.global_step += 1
        
        for class_id, performance in class_metrics.items():
            if class_id not in self.class_performance:
                self.class_performance[class_id] = []
            
            self.class_performance[class_id].append(performance)
            
            # 保持最近的性能记录（滑动窗口）
            if len(self.class_performance[class_id]) > 50:
                self.class_performance[class_id].pop(0)
        
        # 计算平均性能
        for class_id in self.class_performance:
            performances = self.class_performance[class_id]
            self.class_performance[class_id] = sum(performances) / len(performances)
    
    def get_class_statistics(self) -> Dict:
        """获取类别统计信息"""
        return {
            'class_performance': self.class_performance,
            'global_step': self.global_step,
            'adaptation_history': self.adaptation_history[-10:],  # 最近10个自适应因子
            'current_adaptation_factor': self._compute_adaptation_factor()
        }
    
    def reset_class_statistics(self):
        """重置类别统计信息"""
        self.class_performance = {}
        self.global_step = 0
        self.adaptation_history = []
    
    def _get_closed_form_lr(self) -> List[float]:
        """获取闭式解的学习率"""
        return self.get_lr()
