"""
学习率调度器模块 - 集中式实现
所有调度器实现都在这里，通过Hydra的_target_直接访问

使用方式:
在配置文件中直接使用类路径，例如：
_target_: src.schedulers.standard.CosineAnnealingLR
_target_: src.schedulers.advanced.OneCycleLR
_target_: src.schedulers.remote_sensing.MultiScaleScheduler
"""

# 标准调度器
from .standard.cosine_annealing import CosineAnnealingLR
from .standard.step_lr import StepLR
from .standard.polynomial_lr import PolynomialLR
from .standard.exponential_lr import ExponentialLR
from .standard.reduce_on_plateau import ReduceLROnPlateau

# 先进调度器
from .advanced.cosine_warm_restarts import CosineAnnealingWarmRestarts
from .advanced.one_cycle_lr import OneCycleLR
from .advanced.cyclic_lr import CyclicLR
from .advanced.warmup_schedulers import WarmupSchedulers, WarmupCosineAnnealingLR, WarmupPolynomialLR, WarmupLinearLR

# 遥感专用调度器
from .remote_sensing.class_aware_scheduler import ClassAwareScheduler
from .examples.multiscale_scheduler import MultiScaleScheduler, AdaptiveCosineScheduler

__all__ = [
    # 标准调度器
    'CosineAnnealingLR', 'StepLR', 'PolynomialLR', 'ExponentialLR', 'ReduceLROnPlateau',
    # 先进调度器
    'CosineAnnealingWarmRestarts', 'OneCycleLR', 'CyclicLR',
    'WarmupSchedulers', 'WarmupCosineAnnealingLR', 'WarmupPolynomialLR', 'WarmupLinearLR',
    # 遥感专用
    'MultiScaleScheduler', 'AdaptiveCosineScheduler', 'ClassAwareScheduler'
]