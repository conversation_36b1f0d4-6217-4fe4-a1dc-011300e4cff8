#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OneCycleLR调度器实现
基于PyTorch标准OneCycleLR，针对遥感任务进行优化
"""

import torch
from torch.optim.lr_scheduler import _LRScheduler
from typing import List, Optional, Union
import warnings


class OneCycleLR(_LRScheduler):
    """
    OneCycleLR调度器
    
    实现一个周期的学习率调度，包含预热、主要训练和退火三个阶段
    特别适合遥感图像的训练任务
    
    Args:
        optimizer: 优化器
        max_lr: 最大学习率
        total_steps: 总步数 (默认: None)
        epochs: 总epoch数 (默认: None)
        steps_per_epoch: 每个epoch的步数 (默认: None)
        pct_start: 预热阶段占比 (默认: 0.3)
        anneal_strategy: 退火策略 ('cos' 或 'linear') (默认: 'cos')
        cycle_momentum: 是否循环动量 (默认: True)
        base_momentum: 基础动量 (默认: 0.85)
        max_momentum: 最大动量 (默认: 0.95)
        div_factor: 初始学习率除数 (默认: 25.0)
        final_div_factor: 最终学习率除数 (默认: 1e4)
        three_phase: 是否使用三阶段 (默认: False)
        last_epoch: 上一个epoch (默认: -1)
        verbose: 是否打印学习率变化 (默认: False)
    """
    
    def __init__(
        self,
        optimizer: torch.optim.Optimizer,
        max_lr: Union[float, List[float]],
        total_steps: Optional[int] = None,
        epochs: Optional[int] = None,
        steps_per_epoch: Optional[int] = None,
        pct_start: float = 0.3,
        anneal_strategy: str = 'cos',
        cycle_momentum: bool = True,
        base_momentum: Union[float, List[float]] = 0.85,
        max_momentum: Union[float, List[float]] = 0.95,
        div_factor: float = 25.0,
        final_div_factor: float = 1e4,
        three_phase: bool = False,
        last_epoch: int = -1,
        verbose: bool = False
    ):
        # 验证参数
        if total_steps is None and epochs is None and steps_per_epoch is None:
            raise ValueError("You must define either total_steps OR (epochs AND steps_per_epoch)")
        elif total_steps is not None:
            if total_steps <= 0:
                raise ValueError("Expected positive total_steps, but got {}".format(total_steps))
            self.total_steps = total_steps
        else:
            if epochs <= 0:
                raise ValueError("Expected positive epochs, but got {}".format(epochs))
            if steps_per_epoch <= 0:
                raise ValueError("Expected positive steps_per_epoch, but got {}".format(steps_per_epoch))
            self.total_steps = epochs * steps_per_epoch
        
        self.max_lrs = self._format_param('max_lr', optimizer, max_lr)
        self.pct_start = pct_start
        self.anneal_strategy = anneal_strategy
        self.cycle_momentum = cycle_momentum
        self.div_factor = div_factor
        self.final_div_factor = final_div_factor
        self.three_phase = three_phase
        
        if self.cycle_momentum:
            self.base_momentums = self._format_param('base_momentum', optimizer, base_momentum)
            self.max_momentums = self._format_param('max_momentum', optimizer, max_momentum)
        
        super().__init__(optimizer, last_epoch, verbose)
        
        # 计算初始学习率
        self.initial_lrs = [max_lr / self.div_factor for max_lr in self.max_lrs]
        self.min_lrs = [max_lr / self.final_div_factor for max_lr in self.max_lrs]
    
    def _format_param(self, name: str, optimizer: torch.optim.Optimizer, param: Union[float, List[float]]) -> List[float]:
        """格式化参数"""
        if isinstance(param, (list, tuple)):
            if len(param) != len(optimizer.param_groups):
                raise ValueError("expected {} values for {}, got {}".format(
                    len(optimizer.param_groups), name, len(param)))
            return list(param)
        else:
            return [param] * len(optimizer.param_groups)
    
    def get_lr(self) -> List[float]:
        """计算OneCycleLR学习率"""
        if not self._get_lr_called_within_step:
            warnings.warn("To get the last learning rate computed by the scheduler, "
                         "please use `get_last_lr()`.", UserWarning)
        
        lrs = []
        step_num = self.last_epoch
        
        if step_num > self.total_steps:
            return [group['lr'] for group in self.optimizer.param_groups]
        
        for initial_lr, max_lr, min_lr in zip(self.initial_lrs, self.max_lrs, self.min_lrs):
            # 计算当前阶段
            if step_num <= self.pct_start * self.total_steps:
                # 预热阶段
                pct = step_num / (self.pct_start * self.total_steps)
                lr = initial_lr + pct * (max_lr - initial_lr)
            else:
                # 退火阶段
                pct = (step_num - self.pct_start * self.total_steps) / (self.total_steps - self.pct_start * self.total_steps)
                if self.anneal_strategy == 'cos':
                    lr = min_lr + (max_lr - min_lr) * (1 + math.cos(math.pi * pct)) / 2
                else:  # linear
                    lr = max_lr - pct * (max_lr - min_lr)
            
            lrs.append(lr)
        
        return lrs
    
    def _get_closed_form_lr(self) -> List[float]:
        """获取闭式解的学习率"""
        return self.get_lr()


# 为了兼容性，导入math模块
import math
