"""
模型模块初始化文件 - 重构版本
专注于网络架构，训练逻辑已移至src/modules/

重构说明：
- SegmentationModule移动到src/modules/
- 保留纯网络架构在src/models/
- 按任务类型组织（segmentation/, detection/, classification/等）
"""

# 导入分割网络架构
from .segmentation import (
    DeepLabV3Plus,
    UNet,
    UNetPlusPlus,
    SwinUnet,
    ModularAdaptiveDeepLabV3Plus,
    AVAILABLE_ARCHITECTURES
)

__all__ = [
    # 分割网络架构
    'DeepLabV3Plus',
    'UNet',
    'UNetPlusPlus',
    'SwinUnet',
    'ModularAdaptiveDeepLabV3Plus',
    'AVAILABLE_ARCHITECTURES'
]