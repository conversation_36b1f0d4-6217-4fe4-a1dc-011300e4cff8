"""
分割网络架构模块 - 重构版本
导出所有可用的分割网络架构
职责：纯网络架构，不包含训练逻辑

重构说明：
- 从src/models/architectures/移动到src/models/segmentation/
- 专注于网络架构定义，训练逻辑移至src/modules/
"""

from .deeplabv3plus import DeepLabV3Plus
from .unet import UNet, UNetPlusPlus
from .swin_unet import SwinUnet
from .modular_adaptive_deeplabv3plus import ModularAdaptiveDeepLabV3Plus

# 注册可用的模型架构
AVAILABLE_ARCHITECTURES = {
    'deeplabv3plus': DeepLabV3Plus,
    'unet': UNet,
    'unetpp': UNetPlusPlus,
    'unet++': UNetPlusPlus,  # 别名
    'swin_unet': SwinUnet,
    'swin': SwinUnet,  # 别名
    'modular_deeplabv3plus': ModularAdaptiveDeepLabV3Plus,
    'modular_adaptive_deeplabv3plus': ModularAdaptiveDeepLabV3Plus,  # 别名
}

# 导出所有架构
__all__ = [
    'DeepLabV3Plus',
    'UNet',
    'UNetPlusPlus',
    'SwinUnet',
    'ModularAdaptiveDeepLabV3Plus',
    'AVAILABLE_ARCHITECTURES'
]
