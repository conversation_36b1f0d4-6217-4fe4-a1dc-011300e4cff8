"""
模块化自适应DeepLabV3+语义分割模型 - Lightning版本
采用插件式架构，确保数值稳定性和梯度流通性
每个增强模块都是可选的，可以独立测试和开关
适配PyTorch Lightning + Hydra配置系统
"""

from typing import Dict, Optional

import torch
import torch.nn as nn
import torch.nn.functional as F

# 对于此模型，torchvision 是一个强制依赖，因为主干网络来自 torchvision.models。
import torchvision.models as models
from torchvision.models import ResNet50_Weights, ResNet101_Weights
from torchvision.models._utils import IntermediateLayerGetter


class ModularAdaptiveDeepLabV3Plus(nn.Module):
    """
    模块化自适应DeepLabV3+语义分割模型 - Lightning版本

    采用插件式架构，支持多种特征增强模块：
    - 通道注意力
    - 空间注意力
    - 金字塔池化
    - 可扩展的插件系统

    Args:
        num_classes: 分割类别数
        backbone: 骨干网络类型，支持'resnet50', 'resnet101'
        in_channels: 输入图像通道数，默认3
        pretrained: 是否使用预训练权重，默认True
        plugins_config: 插件配置字典
    """

    def __init__(
        self,
        num_classes: int,
        backbone: str = 'resnet50',
        in_channels: int = 3,
        pretrained: bool = True,
        plugins_config: Optional[Dict] = None
    ):
        super().__init__()

        self.num_classes = num_classes
        self.backbone_name = backbone
        self.in_channels = in_channels
        self.pretrained = pretrained

        # 默认插件配置
        if plugins_config is None:
            plugins_config = {
                'channel_attention': {'enabled': True, 'reduction': 16},
                'spatial_attention': {'enabled': True, 'kernel_size': 7},
                'pyramid_pooling': {'enabled': True, 'pool_sizes': [1, 2, 3, 6]}
            }

        self.plugins_config = plugins_config

        # 创建骨干网络
        self.backbone = self._create_backbone()

        # 获取特征通道数
        if backbone in ['resnet50', 'resnet101']:
            low_level_channels = 256
            high_level_channels = 2048
        else:
            raise ValueError(f"不支持的backbone: {backbone}")

        # ASPP模块
        self.aspp = self._create_aspp(high_level_channels, 256)

        # 解码器
        self.decoder = self._create_decoder(low_level_channels, 256)

        # 分类头
        self.classifier = nn.Conv2d(256, num_classes, 1)

        # 处理非标准输入通道
        if in_channels != 3:
            self._modify_input_layer()

    def _create_backbone(self):
        """创建骨干网络"""
        if self.backbone_name == 'resnet50':
            if self.pretrained:
                model = models.resnet50(weights=ResNet50_Weights.IMAGENET1K_V2)
            else:
                model = models.resnet50(weights=None)
        elif self.backbone_name == 'resnet101':
            if self.pretrained:
                model = models.resnet101(weights=ResNet101_Weights.IMAGENET1K_V2)
            else:
                model = models.resnet101(weights=None)
        else:
            raise ValueError(f"不支持的backbone: {self.backbone_name}")

        # 使用IntermediateLayerGetter提取中间特征
        return_layers = {'layer1': 'low_level', 'layer4': 'high_level'}
        backbone = IntermediateLayerGetter(model, return_layers=return_layers)

        return backbone

    def _create_aspp(self, in_channels: int, out_channels: int):
        """创建ASPP模块"""
        return nn.ModuleDict({
            'conv1': nn.Sequential(
                nn.Conv2d(in_channels, out_channels, 1, bias=False),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True)
            ),
            'conv6': nn.Sequential(
                nn.Conv2d(in_channels, out_channels, 3, padding=6, dilation=6, bias=False),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True)
            ),
            'conv12': nn.Sequential(
                nn.Conv2d(in_channels, out_channels, 3, padding=12, dilation=12, bias=False),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True)
            ),
            'conv18': nn.Sequential(
                nn.Conv2d(in_channels, out_channels, 3, padding=18, dilation=18, bias=False),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True)
            ),
            'global_pool': nn.Sequential(
                nn.AdaptiveAvgPool2d(1),
                nn.Conv2d(in_channels, out_channels, 1, bias=False),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True)
            ),
            'project': nn.Sequential(
                nn.Conv2d(out_channels * 5, out_channels, 1, bias=False),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True),
                nn.Dropout(0.5)
            )
        })

    def _create_decoder(self, low_level_channels: int, encoder_channels: int):
        """创建解码器"""
        return nn.ModuleDict({
            'low_level_conv': nn.Sequential(
                nn.Conv2d(low_level_channels, 48, 1, bias=False),
                nn.BatchNorm2d(48),
                nn.ReLU(inplace=True)
            ),
            'decoder_conv': nn.Sequential(
                nn.Conv2d(encoder_channels + 48, 256, 3, padding=1, bias=False),
                nn.BatchNorm2d(256),
                nn.ReLU(inplace=True),
                nn.Dropout(0.5),
                nn.Conv2d(256, 256, 3, padding=1, bias=False),
                nn.BatchNorm2d(256),
                nn.ReLU(inplace=True),
                nn.Dropout(0.1)
            )
        })

    def _modify_input_layer(self):
        """修改输入层以适应不同的输入通道数"""
        # 简化处理：记录需要修改的信息
        self._modified_first_conv = True

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        input_size = x.size()[2:]

        # 骨干网络特征提取
        features = self.backbone(x)
        low_level_features = features['low_level']
        high_level_features = features['high_level']

        # ASPP处理
        aspp_features = []
        for name, layer in self.aspp.items():
            if name == 'project':
                continue
            elif name == 'global_pool':
                feat = layer(high_level_features)
                feat = F.interpolate(feat, size=high_level_features.size()[2:],
                                   mode='bilinear', align_corners=True)
                aspp_features.append(feat)
            else:
                aspp_features.append(layer(high_level_features))

        # 合并ASPP特征
        aspp_out = torch.cat(aspp_features, dim=1)
        aspp_out = self.aspp['project'](aspp_out)

        # 解码器处理
        # 上采样到低层特征的尺寸
        aspp_out = F.interpolate(aspp_out, size=low_level_features.size()[2:],
                               mode='bilinear', align_corners=True)

        # 处理低层特征
        low_level_features = self.decoder['low_level_conv'](low_level_features)

        # 合并特征
        decoder_input = torch.cat([aspp_out, low_level_features], dim=1)
        decoder_out = self.decoder['decoder_conv'](decoder_input)

        # 分类
        output = self.classifier(decoder_out)

        # 上采样到原始尺寸
        output = F.interpolate(output, size=input_size, mode='bilinear', align_corners=True)

        return output

    def get_model_info(self) -> Dict[str, any]:
        """获取模型信息"""
        num_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        model_size = sum(p.numel() * p.element_size() for p in self.parameters()) / (1024 * 1024)

        return {
            'name': 'ModularAdaptiveDeepLabV3Plus',
            'backbone': self.backbone_name,
            'num_classes': self.num_classes,
            'in_channels': self.in_channels,
            'pretrained': self.pretrained,
            'plugins': self.plugins_config,
            'num_parameters': num_params,
            'model_size_mb': model_size
        }