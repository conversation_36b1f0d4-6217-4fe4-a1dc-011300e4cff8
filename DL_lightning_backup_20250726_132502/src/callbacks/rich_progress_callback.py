#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Rich进度条回调

功能特性:
- 替代Lightning默认进度条
- 集成系统资源监控
- 美观的训练进度显示
- 实时指标更新
- 支持训练和验证阶段
"""

import time
from typing import Any, Dict, Optional

import lightning.pytorch as pl
from lightning.pytorch.callbacks import Callback

# 尝试导入Rich组件
try:
    from rich.console import Console
    from rich.progress import (
        Progress, SpinnerColumn, TextColumn, BarColumn, 
        MofNCompleteColumn, TimeElapsedColumn, TimeRemainingColumn,
        TaskID
    )
    from rich.table import Table
    from rich.panel import Panel
    from rich.live import Live
    from rich.layout import Layout
    from rich.text import Text
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

from ..utils.system_monitor import get_system_monitor, get_system_stats_string
from ..utils.console_manager import get_console_manager

# 导入统一主题配置
try:
    from ..utils.rich_theme import get_theme, get_progress_style, get_color
    THEME_AVAILABLE = True
except ImportError:
    THEME_AVAILABLE = False


class RichProgressCallback(Callback):
    """Rich风格的训练进度回调"""
    
    def __init__(
        self, 
        refresh_rate: int = 10,
        enable_system_monitor: bool = True,
        show_system_stats: bool = True,
        leave_progress: bool = True
    ):
        """
        初始化Rich进度回调
        
        Args:
            refresh_rate: 进度条刷新频率（每N个batch更新一次）
            enable_system_monitor: 是否启用系统监控
            show_system_stats: 是否显示系统统计信息
            leave_progress: 训练完成后是否保留进度条
        """
        super().__init__()
        
        self.refresh_rate = refresh_rate
        self.enable_system_monitor = enable_system_monitor
        self.show_system_stats = show_system_stats
        self.leave_progress = leave_progress
        
        # Rich组件
        self.console = Console() if RICH_AVAILABLE else None
        self.progress: Optional[Progress] = None
        self.live_display: Optional[Live] = None
        
        # 任务ID跟踪
        self.train_task_id: Optional[TaskID] = None
        self.val_task_id: Optional[TaskID] = None
        
        # 系统监控
        self.system_monitor = get_system_monitor() if enable_system_monitor else None
        
        # 状态跟踪
        self.current_epoch = 0
        self.total_epochs = 0
        self.best_metrics = {}
        self.start_time = None
        
        # 检查是否应该启用
        self.enabled = RICH_AVAILABLE and self._should_enable()
    
    def _should_enable(self) -> bool:
        """检查是否应该启用Rich进度条"""
        console_manager = get_console_manager()

        # 如果控制台管理器被禁用，也禁用进度条
        if console_manager.quiet_mode:
            return False

        # 在Ray Tune worker中禁用
        if console_manager.is_ray_tune_worker:
            return False

        return True
    
    def _create_progress(self) -> Progress:
        """创建Rich进度条"""
        if not self.enabled:
            return None

        return Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(bar_width=40),
            MofNCompleteColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            "•",
            TimeElapsedColumn(),
            "<",
            TimeRemainingColumn(),
            "•",
            TextColumn("[yellow]Loss: {task.fields[loss]:.4f}[/yellow]"),
            TextColumn("[green]LR: {task.fields[lr]:.2e}[/green]"),
            console=self.console,
            transient=not self.leave_progress
        )
    
    def _create_layout(self) -> Layout:
        """创建显示布局"""
        if not self.enabled:
            return None
        
        layout = Layout()
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="progress", ratio=1),
            Layout(name="footer", size=2)
        )
        return layout
    
    def _update_header(self, layout: Layout, trainer: Optional[pl.Trainer] = None):
        """更新头部信息"""
        if not self.enabled or not layout:
            return

        # 创建训练信息表格
        header_table = Table(show_header=False, box=None, padding=(0, 1))
        header_table.add_column("Info", style="bold blue", width=30)
        header_table.add_column("Metrics", style="green", width=50)

        # 基础信息
        epoch_info = f"🏋️ Epoch {self.current_epoch}/{self.total_epochs}"

        # 最佳指标
        metrics_info = ""
        if self.best_metrics:
            metrics_parts = []
            for key, value in list(self.best_metrics.items())[:3]:  # 只显示前3个指标
                metrics_parts.append(f"🏆 {key}: {value:.4f}")
            metrics_info = " | ".join(metrics_parts)

        # 当前指标（如果有trainer）
        current_metrics = ""
        if trainer and hasattr(trainer, 'logged_metrics'):
            current_parts = []
            for key, value in trainer.logged_metrics.items():
                if 'val/' in key and isinstance(value, (int, float)):
                    metric_name = key.replace('val/', '')
                    current_parts.append(f"📊 {metric_name}: {value:.4f}")
            if current_parts:
                current_metrics = " | ".join(current_parts[:2])  # 只显示前2个

        header_table.add_row(epoch_info, metrics_info or current_metrics or "等待指标...")

        layout["header"].update(Panel(header_table, border_style="blue"))
    
    def _update_footer(self, layout: Layout):
        """更新底部系统信息"""
        if not self.enabled or not layout or not self.show_system_stats:
            return
        
        if self.system_monitor:
            stats_text = get_system_stats_string(include_details=True)
            layout["footer"].update(Panel(stats_text, border_style="dim"))
    
    def on_train_start(self, trainer: pl.Trainer, pl_module: pl.LightningModule) -> None:
        """训练开始时的处理"""
        if not self.enabled:
            return
        
        self.total_epochs = trainer.max_epochs
        self.start_time = time.time()
        
        # 创建进度条
        self.progress = self._create_progress()
        
        if self.progress:
            # 创建布局
            layout = self._create_layout()
            
            # 启动实时显示
            if layout:
                self.live_display = Live(layout, console=self.console, refresh_per_second=2)
                self.live_display.start()
            else:
                self.progress.start()
    
    def on_train_epoch_start(self, trainer: pl.Trainer, pl_module: pl.LightningModule) -> None:
        """训练epoch开始时的处理"""
        if not self.enabled or not self.progress:
            return
        
        self.current_epoch = trainer.current_epoch + 1
        
        # 移除之前的任务
        if self.train_task_id is not None:
            try:
                self.progress.remove_task(self.train_task_id)
            except:
                pass
        
        # 创建新的训练任务
        total_batches = trainer.num_training_batches
        if total_batches != float('inf'):
            self.train_task_id = self.progress.add_task(
                f"[bold magenta]Training Epoch {self.current_epoch}",
                total=total_batches,
                loss=0.0,
                lr=0.0
            )
        
        # 更新布局
        if self.live_display:
            layout = self.live_display.renderable
            self._update_header(layout, trainer)
            layout["progress"].update(self.progress)
    
    def on_train_batch_end(
        self,
        trainer: pl.Trainer,
        pl_module: pl.LightningModule,
        outputs: Any,
        batch: Any,
        batch_idx: int
    ) -> None:
        """训练batch结束时的处理"""
        if not self.enabled or not self.progress or self.train_task_id is None:
            return

        # 只在指定频率更新
        if batch_idx % self.refresh_rate == 0:
            # 获取当前指标
            loss = 0.0
            lr = 0.0

            # 从outputs中获取损失
            if outputs and isinstance(outputs, dict):
                if 'loss' in outputs:
                    loss = float(outputs['loss'])
            elif outputs and hasattr(outputs, 'item'):
                loss = float(outputs.item())

            # 从trainer中获取学习率
            if trainer.optimizers:
                optimizer = trainer.optimizers[0]
                if optimizer.param_groups:
                    lr = optimizer.param_groups[0]['lr']

            # 更新进度条
            self.progress.update(
                self.train_task_id,
                advance=self.refresh_rate,
                loss=loss,
                lr=lr
            )

            # 更新系统统计
            if self.live_display and self.show_system_stats:
                layout = self.live_display.renderable
                self._update_footer(layout)
    
    def on_validation_epoch_start(self, trainer: pl.Trainer, pl_module: pl.LightningModule) -> None:
        """验证epoch开始时的处理"""
        if not self.enabled or not self.progress:
            return
        
        # 移除之前的验证任务
        if self.val_task_id is not None:
            try:
                self.progress.remove_task(self.val_task_id)
            except:
                pass
        
        # 创建新的验证任务
        total_batches = trainer.num_val_batches[0] if trainer.num_val_batches else 0
        if total_batches > 0:
            self.val_task_id = self.progress.add_task(
                f"[bold cyan]Validation Epoch {self.current_epoch}",
                total=total_batches,
                loss=0.0,
                lr=0.0
            )
        
        # 更新布局
        if self.live_display:
            layout = self.live_display.renderable
            layout["progress"].update(self.progress)
    
    def on_validation_batch_end(
        self,
        trainer: pl.Trainer,
        pl_module: pl.LightningModule,
        outputs: Any,
        batch: Any,
        batch_idx: int,
        dataloader_idx: int = 0
    ) -> None:
        """验证batch结束时的处理"""
        if not self.enabled or not self.progress or self.val_task_id is None:
            return
        
        self.progress.update(self.val_task_id, advance=1)
    
    def on_validation_epoch_end(self, trainer: pl.Trainer, pl_module: pl.LightningModule) -> None:
        """验证epoch结束时的处理"""
        if not self.enabled:
            return
        
        # 更新最佳指标
        if hasattr(trainer, 'logged_metrics'):
            metrics = trainer.logged_metrics
            for key, value in metrics.items():
                if 'val/' in key and isinstance(value, (int, float)):
                    metric_name = key.replace('val/', '')
                    if metric_name not in self.best_metrics or value > self.best_metrics[metric_name]:
                        self.best_metrics[metric_name] = float(value)
        
        # 移除验证任务
        if self.val_task_id is not None and self.progress:
            try:
                self.progress.remove_task(self.val_task_id)
                self.val_task_id = None
            except:
                pass
        
        # 更新布局
        if self.live_display:
            layout = self.live_display.renderable
            self._update_header(layout, trainer)
    
    def on_train_epoch_end(self, trainer: pl.Trainer, pl_module: pl.LightningModule) -> None:
        """训练epoch结束时的处理"""
        if not self.enabled:
            return
        
        # 移除训练任务
        if self.train_task_id is not None and self.progress:
            try:
                self.progress.remove_task(self.train_task_id)
                self.train_task_id = None
            except:
                pass
    
    def on_train_end(self, trainer: pl.Trainer, pl_module: pl.LightningModule) -> None:
        """训练结束时的处理"""
        if not self.enabled:
            return
        
        # 停止显示
        if self.live_display:
            self.live_display.stop()
            self.live_display = None
        elif self.progress:
            self.progress.stop()
        
        # 显示训练总结
        if self.start_time:
            duration = time.time() - self.start_time
            console_manager = get_console_manager()

            # 准备最终指标
            final_metrics = {}
            if hasattr(trainer, 'logged_metrics'):
                for key, value in trainer.logged_metrics.items():
                    if isinstance(value, (int, float)):
                        final_metrics[key] = float(value)

            # 显示训练总结
            console_manager.print_training_summary(final_metrics, duration)
        
        # 清理
        self.progress = None
        self.train_task_id = None
        self.val_task_id = None
    
    def on_exception(self, trainer: pl.Trainer, pl_module: pl.LightningModule, exception: BaseException) -> None:
        """异常处理"""
        if not self.enabled:
            return
        
        # 停止显示
        if self.live_display:
            self.live_display.stop()
            self.live_display = None
        elif self.progress:
            self.progress.stop()
        
        # 显示错误信息
        beautifier = get_console_beautifier()
        beautifier.print_error(exception, "训练过程中发生错误")
