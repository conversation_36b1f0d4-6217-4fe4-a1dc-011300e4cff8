#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Ray Tune回调

功能特性:
- 与RayTuneConsoleManager集成
- 试验状态同步
- WandB信息提取
- 多进程协调
"""

import os
from typing import Dict, Any, Optional

# 尝试导入Ray Tune
try:
    import ray
    from ray import tune
    from ray.tune import Callback
    RAY_AVAILABLE = True
except ImportError:
    RAY_AVAILABLE = False
    # 创建一个虚拟的Callback类以避免导入错误
    class Callback:
        pass

from ..utils.console_manager import get_console_manager


class RayTuneConsoleCallback(Callback):
    """Ray Tune控制台回调，用于与控制台管理器通信"""
    
    def __init__(self, console_manager=None):
        """
        初始化Ray Tune回调
        
        Args:
            console_manager: RayTuneConsoleManager实例，如果为None则使用全局实例
        """
        if not RAY_AVAILABLE:
            return
        
        self.console_manager = console_manager or get_console_manager()
    
    def on_trial_start(self, iteration, trials, trial, **info):
        """试验开始时的回调"""
        if not RAY_AVAILABLE or not self.console_manager:
            return
        
        wandb_info = self._extract_wandb_info(trial)
        self.console_manager.update_trial_status(
            trial_id=trial.trial_id,
            status="RUNNING",
            config=trial.config,
            metrics={},
            wandb_info=wandb_info
        )
    
    def on_trial_result(self, iteration, trials, trial, result, **info):
        """试验结果更新时的回调"""
        if not RAY_AVAILABLE or not self.console_manager:
            return
        
        wandb_info = self._extract_wandb_info(trial)
        
        # 提取指标
        metrics = {}
        for key, value in result.items():
            if isinstance(value, (int, float)) and not key.startswith('_'):
                metrics[key] = float(value)
        
        self.console_manager.update_trial_status(
            trial_id=trial.trial_id,
            status="RUNNING",
            config=trial.config,
            metrics=metrics,
            wandb_info=wandb_info
        )
    
    def on_trial_complete(self, iteration, trials, trial, **info):
        """试验完成时的回调"""
        if not RAY_AVAILABLE or not self.console_manager:
            return
        
        wandb_info = self._extract_wandb_info(trial)
        
        # 获取最终结果
        final_metrics = {}
        if trial.last_result:
            for key, value in trial.last_result.items():
                if isinstance(value, (int, float)) and not key.startswith('_'):
                    final_metrics[key] = float(value)
        
        self.console_manager.update_trial_status(
            trial_id=trial.trial_id,
            status="TERMINATED",
            config=trial.config,
            metrics=final_metrics,
            wandb_info=wandb_info
        )
    
    def on_trial_error(self, iteration, trials, trial, **info):
        """试验错误时的回调"""
        if not RAY_AVAILABLE or not self.console_manager:
            return
        
        wandb_info = self._extract_wandb_info(trial)
        self.console_manager.update_trial_status(
            trial_id=trial.trial_id,
            status="ERROR",
            config=trial.config,
            metrics={},
            wandb_info=wandb_info
        )
    
    def _extract_wandb_info(self, trial) -> Dict[str, str]:
        """从试验中提取WandB信息"""
        wandb_info = {
            'mode': 'unknown',
            'status': 'unknown'
        }
        
        # 尝试从试验配置中获取WandB信息
        if hasattr(trial, 'config') and trial.config:
            if 'wandb' in trial.config:
                wandb_config = trial.config['wandb']
                if isinstance(wandb_config, dict):
                    wandb_info['mode'] = wandb_config.get('mode', 'unknown')
            
            # 也可以从顶层配置获取
            wandb_info['mode'] = trial.config.get('wandb_mode', wandb_info['mode'])
        
        # 尝试从环境变量获取
        if wandb_info['mode'] == 'unknown':
            wandb_info['mode'] = os.getenv('WANDB_MODE', 'auto')
        
        return wandb_info


def create_raytune_callback(console_manager=None) -> Optional[RayTuneConsoleCallback]:
    """
    创建Ray Tune回调
    
    Args:
        console_manager: RayTuneConsoleManager实例
        
    Returns:
        RayTuneConsoleCallback实例，如果Ray Tune不可用则返回None
    """
    if not RAY_AVAILABLE:
        return None
    
    return RayTuneConsoleCallback(console_manager)
