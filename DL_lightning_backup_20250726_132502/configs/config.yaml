# =============================================================================
# DL_lightning 主配置文件
# =============================================================================
# 这是整个项目的核心配置文件，定义了训练的默认参数和组件选择
#
# 配置组织结构：
# - defaults: 指定默认使用的配置文件组合
# - 全局参数: 项目名称、实验名称等
# - WandB配置: 实验跟踪和日志记录设置
#
# 使用方式：
#   python scripts/train.py                    # 使用默认配置
#   python scripts/train.py model=unet         # 覆盖模型配置
#   python scripts/train.py optimizer.lr=0.01  # 覆盖具体参数
# =============================================================================

defaults:
  - _self_                    # 当前文件的配置优先级最高
  - trainer: default          # 训练器配置：GPU设置、训练轮数、精度等
  - model: deeplabv3          # 模型配置：默认使用DeepLabV3+语义分割模型
  - data: suide               # 数据集配置：默认使用SuiDe遥感数据集
  - loss: combined            # 损失函数配置：默认使用组合损失函数(CE+Dice+Focal)
  - optimizer: adamw          # 优化器配置：默认使用AdamW优化器
  - scheduler: cosine         # 学习率调度器：默认使用余弦退火调度器
  - logger: wandb             # 日志记录器：默认使用Weights & Biases
  - callbacks: default        # 回调函数：默认使用标准回调(检查点、早停等)
  - experiment: null          # 实验配置：默认不使用预定义实验配置

# =============================================================================
# 全局项目设定
# =============================================================================
project_name: "SuiDe_RemoteSensing"                                    # 项目名称，用于WandB项目分组
experiment_name: "baseline"                                            # 实验名称，用于区分不同的训练运行
run_name: "${experiment_name}_${now:%Y-%m-%d_%H-%M-%S}"               # 运行名称，自动添加时间戳

# =============================================================================
# Weights & Biases (WandB) 实验跟踪配置
# =============================================================================
# WandB是一个强大的机器学习实验跟踪平台，支持：
# - 实时监控训练指标和损失曲线
# - 超参数记录和对比
# - 模型版本管理
# - 团队协作和实验分享
wandb:
  # -----------------------------------------------------------------------------
  # 基本配置
  # -----------------------------------------------------------------------------
  project: ${project_name}                    # WandB项目名称，引用上面定义的project_name
  name: ${run_name}                          # 运行名称，引用上面定义的run_name
  tags: ["baseline", "${model.name}"]        # 标签列表，用于实验分类和筛选
  notes: "A baseline run for segmentation."  # 实验描述，记录实验目的和特点

  # -----------------------------------------------------------------------------
  # 运行模式配置
  # -----------------------------------------------------------------------------
  # 四种模式说明：
  # - auto: 自动检测网络状况，有网络时在线，无网络时离线
  # - online: 强制在线模式，实时同步数据到WandB云端
  # - offline: 强制离线模式，数据保存到本地，可后续手动同步
  # - disabled: 完全禁用WandB，不记录任何实验数据
  mode: auto

  # -----------------------------------------------------------------------------
  # 存储配置
  # -----------------------------------------------------------------------------
  local_dir: null                            # 本地存储目录，null时使用Hydra输出目录下的wandb_local子目录

  # -----------------------------------------------------------------------------
  # 高级功能配置
  # -----------------------------------------------------------------------------
  log_model: false                           # 是否上传模型文件到WandB（大文件会影响同步速度）
  silent: true                               # 是否静默运行，减少WandB的控制台输出
  api_key: null                              # WandB API密钥，建议通过环境变量WANDB_API_KEY设置

  # -----------------------------------------------------------------------------
  # 网络连接配置
  # -----------------------------------------------------------------------------
  timeout: 10                                # 网络连接超时时间(秒)，适用于网络不稳定的环境
  retry_attempts: 3                          # 连接失败时的重试次数
