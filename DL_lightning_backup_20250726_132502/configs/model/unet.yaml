# =============================================================================
# U-Net 模型配置
# =============================================================================
# U-Net是经典的语义分割模型，具有以下特点：
# - 对称的编码器-解码器结构(U型结构)
# - 跳跃连接保持细节信息
# - 参数量适中，训练速度快
# - 在医学图像分割领域表现优异
# - 适合小数据集训练
#
# 适用场景：
# - 医学图像分割
# - 快速原型开发
# - 计算资源受限的环境
# - 小数据集训练
# =============================================================================

_target_: src.modules.segmentation_module.SegmentationModule

# =============================================================================
# 基础模型配置
# =============================================================================
model_name: unet                             # 模型架构名称，对应AVAILABLE_ARCHITECTURES中的键
name: UNet                                   # 模型显示名称，用于日志和WandB标记
num_classes: ${data.dataset_config.num_classes}  # 分割类别数，自动从数据配置中获取
ignore_index: 255                           # 忽略的标签索引，通常用于未标注区域

# =============================================================================
# 模型架构参数
# =============================================================================
model_params:
  in_channels: 3                             # 输入图像通道数
                                             # 3: RGB图像
                                             # 4: RGBA图像或多光谱图像
                                             # 1: 灰度图像

  bilinear: true                             # 上采样方式选择
                                             # true: 使用双线性插值上采样(参数少，速度快)
                                             # false: 使用转置卷积上采样(参数多，效果可能更好)

  features: [64, 128, 256, 512]              # 各层特征图通道数
                                             # 定义U-Net各个层级的特征维度
                                             # 更大的值提供更强的表达能力但增加计算量

  pretrained: false                          # 是否使用预训练权重
                                             # U-Net通常不使用预训练权重
                                             # 因为其结构与分类网络差异较大

# =============================================================================
# 损失函数配置引用
# =============================================================================
loss_cfg: ${loss}                            # 引用全局损失函数配置
                                             # 支持: cross_entropy, dice, focal, combined等

# =============================================================================
# 优化器配置 (内嵌配置，会被全局optimizer配置覆盖)
# =============================================================================
optimizer_cfg:
  _target_: torch.optim.Adam                 # 优化器类型：Adam
  _partial_: true                            # 使用部分实例化，参数会在运行时传入

  lr: 0.001                                  # 学习率
                                             # U-Net推荐范围: 1e-4 到 1e-3

  weight_decay: 1e-4                         # 权重衰减(L2正则化)
                                             # 较小的权重衰减，防止过度正则化

  betas: [0.9, 0.999]                        # Adam动量参数
                                             # beta1: 一阶矩估计的衰减率
                                             # beta2: 二阶矩估计的衰减率

# =============================================================================
# 学习率调度器配置 (内嵌配置，会被全局scheduler配置覆盖)
# =============================================================================
scheduler_cfg:
  _target_: torch.optim.lr_scheduler.StepLR  # 阶梯式学习率调度器

  step_size: 30                              # 学习率衰减的步长(epoch)
                                             # 每30个epoch降低一次学习率

  gamma: 0.1                                 # 学习率衰减因子
                                             # 每次衰减时学习率乘以此值
