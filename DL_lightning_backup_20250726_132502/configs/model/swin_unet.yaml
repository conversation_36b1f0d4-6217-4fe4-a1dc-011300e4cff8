# =============================================================================
# Swin-UNet 模型配置
# =============================================================================
# Swin-UNet是基于Swin Transformer的U型分割网络，具有以下特点：
# - 结合Transformer的全局建模能力和U-Net的分层结构
# - 使用Shifted Window机制提高计算效率
# - 多尺度特征提取和融合
# - 在医学图像和自然图像分割中表现优异
# - 支持大分辨率图像处理
#
# 技术特点：
# - Hierarchical Transformer架构
# - Shifted Window Self-Attention
# - Patch Merging和Patch Expanding
# - Skip Connections保持细节信息
#
# 适用场景：
# - 高分辨率图像分割
# - 需要全局上下文的分割任务
# - 医学图像分析
# - 遥感图像分割
# =============================================================================

_target_: src.modules.segmentation_module.SegmentationModule

# =============================================================================
# 基础模型配置
# =============================================================================
model_name: swin_unet                        # 模型架构名称，对应AVAILABLE_ARCHITECTURES中的键
name: Swin-UNet                              # 模型显示名称，用于日志和WandB标记
num_classes: ${data.dataset_config.num_classes}  # 分割类别数，自动从数据配置中获取
ignore_index: 255                           # 忽略的标签索引，通常用于未标注区域

# =============================================================================
# Swin-UNet 架构参数
# =============================================================================
model_params:
  img_size: 512                              # 输入图像尺寸
                                             # Swin Transformer对输入尺寸敏感
                                             # 必须是patch_size的倍数

  in_channels: 3                             # 输入图像通道数
                                             # 3: RGB图像
                                             # 4: RGBA图像或多光谱图像
                                             # 1: 灰度图像

  depths: [2, 2, 6, 2]                       # 各阶段的Transformer块数量
                                             # 对应4个分辨率层级的深度
                                             # [stage1, stage2, stage3, stage4]
                                             # 更深的网络提供更强的表达能力

  pretrained: true                           # 是否使用预训练权重
                                             # true: 使用ImageNet预训练的Swin Transformer权重
                                             # false: 随机初始化，适合特殊领域数据

# =============================================================================
# 损失函数配置引用
# =============================================================================
loss_cfg: ${loss}                            # 引用全局损失函数配置
                                             # 支持: cross_entropy, dice, focal, combined等

# =============================================================================
# 优化器配置 (内嵌配置，会被全局optimizer配置覆盖)
# =============================================================================
optimizer_cfg:
  _target_: torch.optim.AdamW                # 优化器类型：AdamW
  _partial_: true                            # 使用部分实例化，参数会在运行时传入

  lr: 0.0001                                 # 学习率
                                             # Transformer模型通常使用较小的学习率
                                             # 推荐范围: 1e-5 到 1e-4

  weight_decay: 0.05                         # 权重衰减(L2正则化)
                                             # Transformer模型通常使用较大的权重衰减
                                             # 防止过拟合，推荐范围: 0.01-0.1

  betas: [0.9, 0.999]                        # Adam动量参数
                                             # beta1: 一阶矩估计的衰减率
                                             # beta2: 二阶矩估计的衰减率

# =============================================================================
# 学习率调度器配置 (内嵌配置，会被全局scheduler配置覆盖)
# =============================================================================
scheduler_cfg:
  _target_: torch.optim.lr_scheduler.CosineAnnealingLR  # 余弦退火调度器

  T_max: 150                                 # 余弦退火的半周期长度
                                             # Transformer模型通常需要更长的训练
                                             # 建议设置为实际训练epoch数

  eta_min: 1e-7                              # 最小学习率
                                             # 比其他模型更小，适合Transformer的精细调优
