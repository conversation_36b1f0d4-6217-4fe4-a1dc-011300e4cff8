# =============================================================================
# Weights & Biases (WandB) 日志记录器配置
# =============================================================================
# WandB是一个强大的机器学习实验跟踪平台，提供以下功能：
# - 实时监控训练指标和损失曲线
# - 超参数记录和可视化
# - 模型版本管理和比较
# - 团队协作和实验分享
# - 自动模式切换 (在线/离线/禁用)
#
# 特色功能：
# - 智能网络检测，自动选择最佳模式
# - 离线数据缓存，支持后续同步
# - 丰富的可视化图表和报告
# - 超参数搜索和优化建议
#
# 使用方式：
#   python scripts/train.py logger=wandb
#   python scripts/train.py logger=wandb_offline  # 强制离线模式
#   python scripts/train.py logger=wandb_disabled # 禁用WandB
# =============================================================================

_target_: src.utils.wandb_utils.create_wandb_logger
_partial_: true                              # 使用部分实例化，参数在运行时传入

# =============================================================================
# 配置说明
# =============================================================================
# 实际的WandB logger创建由 src.utils.wandb_utils.create_wandb_logger 函数处理
#
# 该函数提供以下智能功能：
# 1. 自动网络检测：检测网络连接状态，选择最佳模式
# 2. 灵活模式切换：支持在线、离线、禁用三种模式
# 3. 错误处理：网络异常时自动降级到离线模式
# 4. 配置验证：验证WandB配置的有效性
# 5. 本地化支持：离线模式下的本地数据存储
#
# 具体的WandB参数配置在主配置文件 (config.yaml) 的 wandb 部分定义：
# - project: 项目名称
# - name: 运行名称
# - tags: 实验标签
# - mode: 运行模式 (auto/online/offline/disabled)
# - local_dir: 本地存储目录
# - 其他高级配置选项
# =============================================================================
