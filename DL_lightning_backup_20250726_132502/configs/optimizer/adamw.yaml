# =============================================================================
# AdamW 优化器配置
# =============================================================================
# AdamW是Adam优化器的改进版本，具有以下特点：
# - 修正了Adam中权重衰减的实现问题
# - 更好的泛化性能，特别是在Transformer模型中
# - 对学习率和权重衰减的解耦处理
# - 在深度学习任务中表现优异
#
# 适用场景：
# - 大多数深度学习任务的首选优化器
# - Transformer架构模型
# - 需要良好泛化性能的任务
# - 大规模数据集训练
#
# 使用方式：
#   python scripts/train.py optimizer=adamw
#   python scripts/train.py optimizer=adamw optimizer.lr=0.001
# =============================================================================

_target_: torch.optim.AdamW                  # PyTorch AdamW优化器
_partial_: true                              # 使用部分实例化，模型参数在运行时传入

# =============================================================================
# 核心超参数
# =============================================================================
lr: 1e-4                                     # 学习率 (Learning Rate)
                                             # 控制参数更新的步长大小
                                             # 推荐范围: 1e-5 到 1e-3
                                             # - 过大: 训练不稳定，可能发散
                                             # - 过小: 收敛速度慢

weight_decay: 1e-2                           # 权重衰减系数 (L2正则化)
                                             # 防止过拟合，促进模型泛化
                                             # 推荐范围: 1e-4 到 1e-1
                                             # - AdamW中权重衰减与梯度更新解耦
                                             # - 比Adam中的权重衰减更有效
