# =============================================================================
# DL_lightning项目统一Rich UI配置
# =============================================================================
# 此配置文件定义了项目中所有Rich组件的统一样式和行为
# 确保整个项目的视觉一致性和用户体验统一性
#
# 使用方式：
#   python scripts/train.py ui=rich_config
#   python scripts/train.py ui.theme.primary_color="#FF6B6B"  # 覆盖主色调
# =============================================================================

# =============================================================================
# 主题配置
# =============================================================================
theme:
  # 主色调配置
  primary_color: "#00D4AA"           # 主色调 - 青绿色
  secondary_color: "#FF6B6B"         # 次要色 - 珊瑚红
  accent_color: "#4ECDC4"            # 强调色 - 浅青色
  
  # 状态颜色
  success_color: "#51CF66"           # 成功色 - 绿色
  warning_color: "#FFD43B"           # 警告色 - 黄色
  error_color: "#FF6B6B"             # 错误色 - 红色
  info_color: "#74C0FC"              # 信息色 - 蓝色
  
  # 文本颜色
  text_primary: "#FFFFFF"            # 主要文本
  text_secondary: "#A0A0A0"          # 次要文本
  text_muted: "#666666"              # 静音文本
  
  # 背景和边框
  background_color: "#1A1A1A"        # 背景色
  border_color: "#333333"            # 边框色
  highlight_color: "#FFE066"         # 高亮色

# =============================================================================
# 控制台配置
# =============================================================================
console:
  # 基础设置
  width: 100                         # 控制台宽度
  force_terminal: true               # 强制终端模式
  color_system: "auto"               # 颜色系统 (auto/standard/256/truecolor)
  
  # 输出控制
  quiet: false                       # 是否静默模式
  stderr: false                      # 是否输出到stderr
  
  # 环境检测
  auto_disable_in_ci: true           # 在CI环境中自动禁用Rich
  auto_disable_in_ray: true          # 在Ray Tune中自动禁用Rich

# =============================================================================
# 进度条配置
# =============================================================================
progress:
  # 基础设置
  refresh_rate: 10                   # 刷新频率 (步骤)
  bar_width: 40                      # 进度条宽度
  
  # 样式设置
  complete_style: "${theme.primary_color}"     # 完成部分样式
  finished_style: "${theme.success_color}"     # 完成状态样式
  pulse_style: "${theme.accent_color}"         # 脉冲样式
  
  # 显示内容
  show_speed: true                   # 显示速度
  show_percentage: true              # 显示百分比
  show_time_remaining: true          # 显示剩余时间
  show_time_elapsed: true            # 显示已用时间
  
  # 训练特定设置
  show_loss: true                    # 显示损失值
  show_learning_rate: true           # 显示学习率
  show_metrics: true                 # 显示指标
  
  # 系统监控
  enable_system_monitor: true        # 启用系统监控
  show_gpu_stats: true               # 显示GPU统计
  show_memory_stats: true            # 显示内存统计
  show_cpu_stats: true               # 显示CPU统计

# =============================================================================
# 面板和横幅配置
# =============================================================================
panels:
  # 启动横幅
  startup_banner:
    title: "🔥 训练启动"
    border_style: "${theme.primary_color}"
    padding: [1, 2]                  # [垂直, 水平] 内边距
    expand: false                    # 是否扩展到全宽
    
  # 状态面板
  status_panel:
    title: "📈 WandB 状态"
    border_style: "${theme.accent_color}"
    padding: [0, 1]
    expand: false
    
  # 完成面板
  completion_panel:
    title: "🎉 训练完成"
    border_style: "${theme.success_color}"
    padding: [1, 2]
    expand: false

# =============================================================================
# 表格配置
# =============================================================================
tables:
  # 模型摘要表格
  model_summary:
    header_style: "${theme.primary_color}"
    border_style: "${theme.border_color}"
    row_styles: ["${theme.text_primary}", "${theme.text_secondary}"]
    show_header: true
    show_lines: true
    
  # 指标表格
  metrics_table:
    header_style: "${theme.info_color}"
    border_style: "${theme.border_color}"
    row_styles: ["${theme.text_primary}"]
    show_header: true
    show_lines: false

# =============================================================================
# 日志配置
# =============================================================================
logging:
  # 日志级别样式
  level_styles:
    DEBUG: "${theme.text_muted}"
    INFO: "${theme.info_color}"
    WARNING: "${theme.warning_color}"
    ERROR: "${theme.error_color}"
    CRITICAL: "bold ${theme.error_color}"
  
  # 消息样式
  message_styles:
    success: "bold ${theme.success_color}"
    info: "${theme.info_color}"
    warning: "bold ${theme.warning_color}"
    error: "bold ${theme.error_color}"
    
  # 表情符号映射
  emoji_map:
    success: "✅"
    error: "❌"
    warning: "⚠️"
    info: "ℹ️"
    loading: "⏳"
    completed: "🎉"
    training: "🏋️"
    validation: "🔍"
    testing: "🧪"
    model: "🤖"
    data: "📊"
    config: "⚙️"
    logger: "📝"
    callback: "🔧"
    checkpoint: "💾"
    gpu: "🎮"
    cpu: "💻"
    memory: "🧠"
    network: "🌐"
    offline: "💾"
    online: "🌐"
    disabled: "🚫"
    auto: "🔄"

# =============================================================================
# 回调配置
# =============================================================================
callbacks:
  # Rich模型摘要
  rich_model_summary:
    max_depth: 1                     # 最大显示深度
    
  # Rich进度条
  rich_progress:
    refresh_rate: "${progress.refresh_rate}"
    enable_system_monitor: "${progress.enable_system_monitor}"
    show_system_stats: true
    leave_progress: true             # 训练完成后保留进度条

# =============================================================================
# 响应式配置
# =============================================================================
responsive:
  # 终端宽度阈值
  narrow_threshold: 80               # 窄屏阈值
  wide_threshold: 120                # 宽屏阈值
  
  # 自适应行为
  auto_adjust_width: true            # 自动调整宽度
  auto_hide_details: true            # 窄屏时自动隐藏详细信息
  
  # 移动设备优化
  mobile_friendly: true              # 移动设备友好模式
  reduce_animations: false           # 减少动画效果

# =============================================================================
# 性能配置
# =============================================================================
performance:
  # 渲染优化
  lazy_rendering: true               # 延迟渲染
  buffer_size: 1000                  # 缓冲区大小
  
  # 更新频率控制
  min_update_interval: 0.1           # 最小更新间隔 (秒)
  max_update_interval: 1.0           # 最大更新间隔 (秒)
  
  # 内存管理
  auto_cleanup: true                 # 自动清理
  max_history_size: 100              # 最大历史记录大小

# =============================================================================
# 调试配置
# =============================================================================
debug:
  # 调试模式
  enabled: false                     # 是否启用调试模式
  verbose: false                     # 详细输出
  
  # 性能监控
  profile_rendering: false           # 性能分析渲染
  show_render_time: false            # 显示渲染时间
  
  # 错误处理
  strict_mode: false                 # 严格模式
  fallback_to_plain: true            # 出错时回退到普通输出
