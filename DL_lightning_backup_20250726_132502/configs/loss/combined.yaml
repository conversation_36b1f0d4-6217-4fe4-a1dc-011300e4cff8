# =============================================================================
# 组合损失函数配置
# =============================================================================
# 组合损失函数将多种损失函数加权组合，具有以下优势：
# - 结合不同损失函数的优点
# - 交叉熵损失：优化整体分类准确性
# - Dice损失：优化分割区域的重叠度
# - Focal损失：处理类别不平衡问题
# - 通过权重调节各损失函数的重要性
#
# 适用场景：
# - 类别不平衡的分割任务
# - 需要精确边界的分割任务
# - 小目标分割任务
# - 多类别语义分割
#
# 使用方式：
#   python scripts/train.py loss=combined
#   python scripts/train.py loss=combined loss.loss_weights.ce=0.5
# =============================================================================

_target_: src.losses.combined_loss.CombinedLoss

# =============================================================================
# 基础配置
# =============================================================================
num_classes: ${data.dataset_config.num_classes}  # 分割类别数，自动从数据配置获取
ignore_index: 255                           # 忽略的标签索引
                                             # 通常用于未标注区域或边界像素
                                             # 这些像素不参与损失计算

use_dynamic_weights: true                    # 是否使用动态类别权重
                                             # true: 根据类别频率自动计算权重
                                             # false: 使用均匀权重

# =============================================================================
# 损失函数权重配置
# =============================================================================
loss_weights:
  ce: 0.4                                    # 交叉熵损失权重
                                             # 优化整体分类准确性
                                             # 推荐范围: 0.3-0.6

  dice: 0.4                                  # Dice损失权重
                                             # 优化分割区域重叠度
                                             # 对小目标分割特别有效
                                             # 推荐范围: 0.3-0.6

  focal: 0.2                                 # Focal损失权重
                                             # 处理类别不平衡和困难样本
                                             # 推荐范围: 0.1-0.3

# =============================================================================
# 交叉熵损失特定参数
# =============================================================================
ce_params:
  weight_method: 'inverse'                   # 类别权重计算方法
                                             # - 'inverse': 1/频率
                                             # - 'sqrt_inverse': 1/sqrt(频率)
                                             # - 'log_inverse': 1/log(频率+1)

  label_smoothing: 0.1                       # 标签平滑系数
                                             # 防止过拟合，提高泛化能力
                                             # 推荐范围: 0.0-0.2

# =============================================================================
# Dice损失特定参数
# =============================================================================
dice_params:
  smooth: 1.0                                # 平滑参数
                                             # 防止除零错误，稳定训练
                                             # 推荐范围: 0.1-2.0

  per_class: false                           # 是否返回每个类别的损失
                                             # true: 分别计算每类的Dice损失
                                             # false: 计算平均Dice损失

  square: false                              # 是否对预测和目标进行平方
                                             # true: 使用平方Dice损失
                                             # false: 使用标准Dice损失

# =============================================================================
# Focal损失特定参数
# =============================================================================
focal_params:
  alpha: 0.25                                # 平衡因子
                                             # 调节正负样本的权重
                                             # 推荐范围: 0.1-0.5

  gamma: 2.0                                 # 聚焦参数
                                             # 控制对困难样本的关注度
                                             # 推荐范围: 1.0-3.0

  weight_method: 'inverse'                   # 权重计算方法
                                             # 与交叉熵损失保持一致
