# =============================================================================
# 交叉熵损失函数配置
# =============================================================================
# 交叉熵损失是分类任务中最常用的损失函数，具有以下特点：
# - 衡量预测概率分布与真实标签的差异
# - 对于多类别分类问题表现优异
# - 支持类别权重平衡
# - 可以结合标签平滑技术
# - 计算效率高，收敛稳定
#
# 适用场景：
# - 标准的语义分割任务
# - 类别相对平衡的数据集
# - 需要概率输出的任务
# - 作为组合损失的基础组件
#
# 使用方式：
#   python scripts/train.py loss=cross_entropy
#   python scripts/train.py loss=cross_entropy loss.label_smoothing=0.1
# =============================================================================

_target_: src.losses.cross_entropy_loss.CrossEntropyLoss

# =============================================================================
# 基础配置
# =============================================================================
num_classes: ${data.dataset_config.num_classes}  # 分割类别数，自动从数据配置获取
ignore_index: 255                           # 忽略的标签索引
                                             # 通常用于未标注区域或边界像素
                                             # 这些像素不参与损失计算

# =============================================================================
# 类别权重配置
# =============================================================================
weight: null                                 # 初始类别权重 (可选)
                                             # null: 不使用预设权重
                                             # [w1, w2, ...]: 手动指定各类别权重
                                             # 通常配合use_dynamic_weights使用

use_dynamic_weights: true                    # 是否使用动态类别权重
                                             # true: 根据训练数据自动计算权重
                                             # false: 使用均匀权重或预设权重

weight_method: 'inverse'                     # 动态权重计算方法
                                             # 'inverse': 1/频率 (强调少数类)
                                             # 'sqrt_inverse': 1/sqrt(频率) (温和平衡)
                                             # 'log_inverse': 1/log(频率+1) (轻微平衡)

# =============================================================================
# 损失计算配置
# =============================================================================
reduction: 'mean'                            # 损失聚合方式
                                             # 'mean': 计算平均损失 (推荐)
                                             # 'sum': 计算总损失
                                             # 'none': 返回每个样本的损失

label_smoothing: 0.0                         # 标签平滑系数
                                             # 0.0: 不使用标签平滑
                                             # 0.1: 轻微平滑，提高泛化能力
                                             # 0.2: 较强平滑，防止过拟合
                                             # 推荐范围: 0.0-0.2
