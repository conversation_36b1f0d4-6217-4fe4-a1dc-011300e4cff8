# Focal损失函数配置 - Lightning版本
# 适配PyTorch Lightning 2.3+ 和 Hydra 1.3.2+

_target_: src.losses.focal_loss.FocalLoss

# 类别数量
num_classes: ${data.dataset_config.num_classes}

# 平衡因子，可以是标量或向量
alpha: 0.25

# 聚焦参数，控制难易样本的权重
gamma: 2.0

# 忽略的标签索引
ignore_index: 255

# 初始类别权重（可选）
weight: null

# 损失聚合方式 ('mean', 'sum', 'none')
reduction: 'mean'

# 是否使用动态类别权重
use_dynamic_weights: true

# 权重计算方法 ('inverse', 'sqrt_inverse', 'log_inverse')
weight_method: 'inverse'