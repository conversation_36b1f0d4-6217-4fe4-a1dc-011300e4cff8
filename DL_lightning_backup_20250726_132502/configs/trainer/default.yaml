# =============================================================================
# PyTorch Lightning Trainer 默认配置
# =============================================================================
# Trainer是PyTorch Lightning的核心组件，负责管理整个训练流程
# 包括：硬件配置、训练循环、验证、日志记录、检查点保存等
#
# 使用方式：
#   python scripts/train.py                           # 使用默认配置
#   python scripts/train.py trainer.max_epochs=100    # 覆盖训练轮数
#   python scripts/train.py trainer.devices=2         # 使用2个GPU
# =============================================================================

_target_: lightning.pytorch.Trainer

# =============================================================================
# 硬件和计算配置
# =============================================================================
accelerator: "auto"                          # 计算加速器类型
                                             # - "auto": 自动检测可用的最佳加速器(GPU/TPU/CPU)
                                             # - "gpu": 强制使用GPU
                                             # - "cpu": 强制使用CPU
                                             # - "tpu": 使用TPU(需要特殊环境)

devices: "auto"                              # 设备数量配置
                                             # - "auto": 自动使用所有可用设备
                                             # - 1: 使用1个设备
                                             # - [0,1]: 使用指定的设备ID
                                             # - -1: 使用所有可用设备

precision: "16-mixed"                        # 数值精度配置
                                             # - "32": 标准32位浮点精度(默认)
                                             # - "16-mixed": 混合精度训练，节省显存并加速
                                             # - "bf16-mixed": BFloat16混合精度(需要支持的硬件)
                                             # - "64": 64位双精度(很少使用)

# =============================================================================
# 训练流程控制
# =============================================================================
max_epochs: 50                               # 最大训练轮数
                                             # 训练将在达到此轮数后停止
                                             # 可以通过早停回调提前结束

check_val_every_n_epoch: 1                   # 验证频率
                                             # 每N个训练epoch后进行一次验证
                                             # 设置为更大值可以加速训练但减少验证频率

log_every_n_steps: 10                        # 日志记录频率
                                             # 每N个训练步骤记录一次指标
                                             # 较小值提供更详细的训练曲线

# =============================================================================
# 用户界面配置
# =============================================================================
enable_progress_bar: false                   # 是否启用默认进度条
                                             # 设为false以使用自定义的Rich进度条
                                             # 自定义进度条提供更美观的显示效果

enable_model_summary: false                  # 是否启用默认模型摘要
                                             # false: 禁用默认ModelSummary，使用RichModelSummary
                                             # true: 启用默认ModelSummary
                                             # 设为false避免与RichModelSummary冲突

# =============================================================================
# 开发和调试选项
# =============================================================================
fast_dev_run: false                          # 快速开发运行模式
                                             # true: 只运行1个训练和验证批次，用于快速测试
                                             # false: 正常训练模式
                                             # 数字: 运行指定数量的批次

limit_train_batches: null                    # 限制训练批次数量
                                             # null: 使用所有训练数据
                                             # 0.0-1.0: 使用训练数据的百分比
                                             # 整数: 使用指定数量的批次

limit_val_batches: null                      # 限制验证批次数量
                                             # null: 使用所有验证数据
                                             # 0.0-1.0: 使用验证数据的百分比
                                             # 整数: 使用指定数量的批次
