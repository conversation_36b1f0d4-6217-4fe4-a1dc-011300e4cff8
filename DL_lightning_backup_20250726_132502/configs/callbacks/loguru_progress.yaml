# =============================================================================
# Loguru统一输出回调函数配置
# =============================================================================
# 基于Loguru的统一输出架构，替换Rich依赖的回调函数
# 提供以下功能：
# - 模型检查点保存和管理
# - 学习率监控和记录
# - 统一的训练进度显示和系统监控
# - 模型结构摘要显示
# - 三种训练模式的自动适配
#
# 使用方式：
#   python scripts/train.py callbacks=loguru_progress
# =============================================================================

# =============================================================================
# 模型检查点回调
# =============================================================================
model_checkpoint:
  _target_: lightning.pytorch.callbacks.ModelCheckpoint

  # ---------------------------------------------------------------------------
  # 保存路径配置
  # ---------------------------------------------------------------------------
  dirpath: ${path_join:${get_run_dir:}, "checkpoints"}
                                             # 检查点保存目录
                                             # 动态计算：运行目录/checkpoints
                                             # 避免硬编码路径，支持Hydra输出管理

  # ---------------------------------------------------------------------------
  # 文件命名和监控配置
  # ---------------------------------------------------------------------------
  filename: "epoch_{epoch:03d}-iou_{val/iou:.4f}"
                                             # 检查点文件命名格式
                                             # 包含epoch和验证IoU信息
                                             # 便于识别最佳模型

  monitor: "val/iou"                         # 监控的指标名称
                                             # 语义分割任务通常监控IoU
                                             # 也可以选择 "val/loss" 等其他指标

  mode: "max"                                # 监控模式
                                             # "max": 指标越大越好 (IoU, Accuracy)
                                             # "min": 指标越小越好 (Loss)

  save_top_k: 1                              # 保存最佳模型的数量
                                             # 1: 只保存最佳模型
                                             # 3: 保存前3个最佳模型
                                             # -1: 保存所有检查点

  auto_insert_metric_name: false             # 是否自动插入指标名到文件名
                                             # false: 使用自定义文件名格式

  save_last: true                            # 是否保存最后一个epoch的模型
                                             # 用于恢复训练或调试

# =============================================================================
# 学习率监控回调
# =============================================================================
learning_rate_monitor:
  _target_: lightning.pytorch.callbacks.LearningRateMonitor

  logging_interval: "step"                   # 学习率记录间隔
                                             # "step": 每个训练步骤记录
                                             # "epoch": 每个epoch记录

# =============================================================================
# Loguru模型结构摘要回调
# =============================================================================
loguru_model_summary:
  _target_: src.callbacks.loguru_progress_callback.LoguruModelSummaryCallback
                                             # 使用Loguru统一输出的模型结构显示
                                             # 提供简洁、一致的模型信息格式
                                             # 包括参数数量、层级结构等

# =============================================================================
# Loguru统一进度条回调
# =============================================================================
loguru_progress:
  _target_: src.callbacks.loguru_progress_callback.LoguruProgressCallback

  # ---------------------------------------------------------------------------
  # 进度显示配置
  # ---------------------------------------------------------------------------
  refresh_rate: 10                           # 进度条刷新频率 (步骤)
                                             # 控制进度条更新的频率
                                             # 较小值提供更流畅的显示

  leave_progress: true                       # 训练完成后是否保留进度条
                                             # true: 保留最终状态显示
                                             # false: 清除进度条

  # ---------------------------------------------------------------------------
  # 系统监控配置
  # ---------------------------------------------------------------------------
  enable_system_monitor: true                # 是否启用系统监控
                                             # 显示GPU使用率、内存占用等

  show_system_stats: true                    # 是否显示系统统计信息
                                             # 包括CPU、GPU、内存使用情况

  system_monitor_interval: 5.0               # 系统监控更新间隔(秒)
                                             # 控制系统资源监控的频率

  # ---------------------------------------------------------------------------
  # 输出格式配置
  # ---------------------------------------------------------------------------
  use_simple_format: true                    # 使用简洁的文本格式
                                             # true: [INFO] 格式
                                             # false: 详细格式

  show_eta: true                             # 是否显示预计完成时间
                                             # 帮助估算训练剩余时间

  show_metrics_summary: true                 # 是否显示指标摘要
                                             # 在epoch结束时显示关键指标

# =============================================================================
# 扩展回调配置 (可选)
# =============================================================================
# 🎯 未来可以添加的自定义回调：
#
# log_segmentation_masks:                   # 分割结果可视化回调
#   _target_: src.callbacks.wandb_callbacks.LogSegmentationMasksCallback
#   num_samples: 8                          # 记录的样本数量
#   log_frequency: 5                        # 记录频率 (epoch)
#
# early_stopping:                           # 早停回调
#   _target_: lightning.pytorch.callbacks.EarlyStopping
#   monitor: "val/loss"                     # 监控指标
#   patience: 10                            # 耐心等待epoch数
#   mode: "min"                             # 监控模式
#
# gradient_clip:                             # 梯度裁剪回调
#   _target_: lightning.pytorch.callbacks.GradientClipCallback
#   gradient_clip_val: 1.0                  # 梯度裁剪阈值
