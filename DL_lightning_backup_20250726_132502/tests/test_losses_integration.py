#!/usr/bin/env python3
"""
损失函数与SegmentationModule集成测试
验证损失函数在实际训练环境中的工作情况

测试范围：
1. 损失函数与SegmentationModule集成
2. 通过Hydra配置系统使用损失函数
3. 训练流程中的损失函数工作验证
4. 不同损失函数的性能对比测试
"""

import sys
from pathlib import Path
import pytest
import warnings

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import torch
from omegaconf import DictConfig
import hydra

# 导入模块
from src.modules.segmentation_module import SegmentationModule
from src.losses import AVAILABLE_LOSSES


class TestLossIntegrationWithSegmentationModule:
    """损失函数与SegmentationModule集成测试"""
    
    @pytest.mark.parametrize("loss_name", ['cross_entropy', 'dice', 'focal', 'combined'])
    def test_loss_integration_with_segmentation_module(self, loss_name):
        """测试不同损失函数与SegmentationModule的集成"""
        
        # 抑制Lightning的trainer警告（测试环境下正常）
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", message=".*self.trainer.*")
            
            # 创建损失函数配置
            if loss_name == 'cross_entropy':
                loss_cfg = DictConfig({
                    '_target_': 'src.losses.cross_entropy_loss.CrossEntropyLoss',
                    'ignore_index': 255
                })
            elif loss_name == 'dice':
                loss_cfg = DictConfig({
                    '_target_': 'src.losses.dice_loss.DiceLoss',
                    'num_classes': 7,
                    'smooth': 1e-5
                })
            elif loss_name == 'focal':
                loss_cfg = DictConfig({
                    '_target_': 'src.losses.focal_loss.FocalLoss',
                    'alpha': 1.0,
                    'gamma': 2.0,
                    'num_classes': 7
                })
            elif loss_name == 'combined':
                loss_cfg = DictConfig({
                    '_target_': 'src.losses.combined_loss.CombinedLoss',
                    'loss_weights': {'ce': 0.5, 'dice': 0.5},
                    'ignore_index': 255,
                    'num_classes': 7
                })
            
            # 创建SegmentationModule
            model = SegmentationModule(
                model_name='deeplabv3plus',
                model_params=DictConfig({
                    'backbone': 'resnet50',
                    'in_channels': 3,
                    'pretrained': False
                }),
                optimizer_cfg=DictConfig({
                    '_target_': 'torch.optim.AdamW',
                    'lr': 0.001
                }),
                scheduler_cfg=DictConfig({
                    '_target_': 'torch.optim.lr_scheduler.StepLR',
                    'step_size': 10
                }),
                loss_cfg=loss_cfg,
                num_classes=7,
                ignore_index=255
            )
            
            # 创建测试数据
            test_batch = {
                'image': torch.randn(2, 3, 128, 128),
                'mask': torch.randint(0, 7, (2, 128, 128))
            }
            
            # 测试训练步骤
            model.train()
            train_loss = model.training_step(test_batch, 0)
            
            assert isinstance(train_loss, torch.Tensor), f"{loss_name}: 训练步骤未返回损失张量"
            assert train_loss.dim() == 0, f"{loss_name}: 损失张量维度错误"
            assert not torch.isnan(train_loss), f"{loss_name}: 损失为NaN"
            assert not torch.isinf(train_loss), f"{loss_name}: 损失为无穷大"
            assert train_loss.item() >= 0, f"{loss_name}: 损失值应该非负"
            
            # 测试验证步骤
            model.eval()
            with torch.no_grad():
                val_result = model.validation_step(test_batch, 0)
            
            print(f"✅ {loss_name} 损失函数集成测试通过")
    
    def test_loss_function_consistency(self):
        """测试损失函数的数值一致性"""
        
        # 创建固定的测试数据
        torch.manual_seed(42)
        logits = torch.randn(2, 7, 64, 64)
        targets = torch.randint(0, 7, (2, 64, 64))
        
        # 测试CrossEntropyLoss的一致性
        from src.losses import CrossEntropyLoss
        
        ce_loss1 = CrossEntropyLoss(ignore_index=255)
        ce_loss2 = CrossEntropyLoss(ignore_index=255)
        
        loss1 = ce_loss1(logits, targets)
        loss2 = ce_loss2(logits, targets)
        
        # 两次计算应该得到相同的结果
        assert torch.allclose(loss1, loss2, atol=1e-6), "CrossEntropyLoss数值不一致"
        
        print(f"✅ 损失函数数值一致性测试通过: {loss1.item():.6f}")
    
    def test_loss_function_gradients(self):
        """测试损失函数的梯度计算"""
        
        from src.losses import CrossEntropyLoss, DiceLoss
        
        # 创建需要梯度的测试数据
        logits = torch.randn(2, 7, 32, 32, requires_grad=True)
        targets = torch.randint(0, 7, (2, 32, 32))
        
        # 测试CrossEntropyLoss梯度
        ce_loss = CrossEntropyLoss()
        ce_result = ce_loss(logits, targets)
        ce_result.backward()
        
        assert logits.grad is not None, "CrossEntropyLoss未产生梯度"
        assert not torch.isnan(logits.grad).any(), "CrossEntropyLoss梯度包含NaN"
        assert not torch.isinf(logits.grad).any(), "CrossEntropyLoss梯度包含无穷大"
        
        # 清除梯度
        logits.grad.zero_()
        
        # 测试DiceLoss梯度
        dice_loss = DiceLoss(num_classes=7)
        dice_result = dice_loss(logits, targets)
        dice_result.backward()
        
        assert logits.grad is not None, "DiceLoss未产生梯度"
        assert not torch.isnan(logits.grad).any(), "DiceLoss梯度包含NaN"
        assert not torch.isinf(logits.grad).any(), "DiceLoss梯度包含无穷大"
        
        print("✅ 损失函数梯度计算测试通过")
    
    def test_loss_function_performance_comparison(self):
        """测试不同损失函数的性能对比"""
        
        import time
        from src.losses import CrossEntropyLoss, DiceLoss, FocalLoss, CombinedLoss
        
        # 创建较大的测试数据
        logits = torch.randn(4, 7, 256, 256)
        targets = torch.randint(0, 7, (4, 256, 256))
        
        # 测试不同损失函数的计算时间
        loss_functions = {
            'CrossEntropy': CrossEntropyLoss(),
            'Dice': DiceLoss(num_classes=7),
            'Focal': FocalLoss(num_classes=7),
            'Combined': CombinedLoss(loss_weights={'ce': 0.5, 'dice': 0.5}, num_classes=7)
        }
        
        performance_results = {}
        
        for name, loss_fn in loss_functions.items():
            # 预热
            for _ in range(3):
                _ = loss_fn(logits, targets)
            
            # 计时
            start_time = time.time()
            for _ in range(10):
                loss = loss_fn(logits, targets)
            end_time = time.time()
            
            avg_time = (end_time - start_time) / 10
            performance_results[name] = avg_time
            
            print(f"  {name}: {avg_time:.4f}s, 损失值: {loss.item():.4f}")
        
        # 验证所有损失函数都能正常计算
        for name, time_cost in performance_results.items():
            assert time_cost > 0, f"{name} 损失函数计算时间异常"
            assert time_cost < 1.0, f"{name} 损失函数计算时间过长: {time_cost:.4f}s"
        
        print("✅ 损失函数性能对比测试通过")
    
    def test_loss_function_memory_usage(self):
        """测试损失函数的内存使用"""
        
        import gc
        import torch
        from src.losses import CrossEntropyLoss, DiceLoss
        
        # 清理内存
        gc.collect()
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
        
        # 记录初始内存
        initial_memory = torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
        
        # 创建测试数据
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        logits = torch.randn(8, 7, 512, 512, device=device)
        targets = torch.randint(0, 7, (8, 512, 512), device=device)
        
        # 测试CrossEntropyLoss内存使用
        ce_loss = CrossEntropyLoss().to(device)
        ce_result = ce_loss(logits, targets)
        
        if torch.cuda.is_available():
            ce_memory = torch.cuda.memory_allocated() - initial_memory
            print(f"  CrossEntropyLoss 内存使用: {ce_memory / 1024 / 1024:.2f} MB")
        
        # 清理
        del ce_result, ce_loss
        gc.collect()
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
        
        # 测试DiceLoss内存使用
        dice_loss = DiceLoss(num_classes=7).to(device)
        dice_result = dice_loss(logits, targets)
        
        if torch.cuda.is_available():
            dice_memory = torch.cuda.memory_allocated() - initial_memory
            print(f"  DiceLoss 内存使用: {dice_memory / 1024 / 1024:.2f} MB")
        
        # 验证内存使用合理
        assert dice_result is not None, "DiceLoss计算失败"
        
        print("✅ 损失函数内存使用测试通过")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "--tb=short", "-s"])
