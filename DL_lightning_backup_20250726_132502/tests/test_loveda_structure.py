#!/usr/bin/env python3
"""
测试LoveDA数据模块结构
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from omegaconf import OmegaConf

def test_loveda_structure():
    """测试LoveDA数据模块结构"""
    
    print("🔍 测试LoveDA数据模块结构...")
    
    try:
        # 测试导入
        from src.data.datasets.loveda_dataset import LoveDADataset, LoveDAClassMapper
        from src.data.loveda_datamodule import LoveDADataModule
        
        print("✅ 所有模块导入成功")
        
        # 测试配置
        config = OmegaConf.create({
            'dataset_config': {
                'data_dir': "data/LoveDA",  # 假设的LoveDA数据路径
                'num_classes': 7
            },
            'transform_config': None,
            'dataloader_config': {'batch_size': 4, 'num_workers': 0}
        })
        
        # 测试DataModule创建
        dm = LoveDADataModule(
            dataset_config=config.dataset_config,
            transform_config=config.transform_config,
            dataloader_config=config.dataloader_config
        )
        
        print("✅ LoveDA DataModule创建成功")
        
        # 测试类别映射器
        class_mapper = LoveDAClassMapper()
        print(f"✅ 类别映射器创建成功，类别数: {class_mapper.num_classes}")
        print(f"  类别名称: {class_mapper.class_names}")
        
        # 测试数据集创建（不需要实际数据）
        try:
            dataset = LoveDADataset(
                data_dir=Path("data/LoveDA"),
                split='train',
                class_mapper=class_mapper,
                transform=None
            )
            print("✅ LoveDA Dataset创建成功")
        except FileNotFoundError as e:
            print(f"⚠️ 数据目录不存在（预期）: {e}")
            print("✅ Dataset类结构正确")
        
        # 测试setup（会因为数据不存在而失败，但可以测试结构）
        try:
            dm.setup('fit')
            print("✅ DataModule setup成功")
            print(f"  训练集样本数: {len(dm.train_dataset)}")
            print(f"  验证集样本数: {len(dm.val_dataset)}")
            print(f"  类别数量: {dm.num_classes}")

            # 测试数据加载
            train_loader = dm.train_dataloader()
            batch = next(iter(train_loader))

            print("✅ 数据加载成功")
            print(f"  Batch keys: {list(batch.keys())}")
            print(f"  Image shape: {batch['image'].shape}")
            print(f"  Mask shape: {batch['mask'].shape}")

            # 验证数据格式
            if len(batch['image'].shape) == 4 and batch['image'].shape[1] == 3:
                print("✅ 图像格式正确: [B, C, H, W]")
            else:
                print(f"❌ 图像格式错误: {batch['image'].shape}")

            if len(batch['mask'].shape) == 3:
                print("✅ 掩码格式正确: [B, H, W]")
            else:
                print(f"❌ 掩码格式错误: {batch['mask'].shape}")

        except FileNotFoundError as e:
            print(f"⚠️ 数据目录不存在（预期）: {e}")
            print("✅ DataModule结构正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_loveda_vs_suide_structure():
    """对比LoveDA和SuiDe的结构一致性"""
    
    print("\n🔍 测试LoveDA vs SuiDe结构一致性...")
    
    try:
        from src.data.datasets.loveda_dataset import LoveDADataset, LoveDAClassMapper
        from src.data.datasets.suide_dataset import SuiDeDataset, SuiDeClassMapper
        from src.data.loveda_datamodule import LoveDADataModule
        from src.data.suide_datamodule import SuiDeDataModule
        
        # 检查DataModule接口一致性
        loveda_methods = set(dir(LoveDADataModule))
        suide_methods = set(dir(SuiDeDataModule))
        
        common_methods = loveda_methods & suide_methods
        print(f"✅ 共同方法数: {len(common_methods)}")
        
        # 检查关键方法
        required_methods = {
            'setup', 'train_dataloader', 'val_dataloader', 
            'test_dataloader', 'num_classes'
        }
        
        for method in required_methods:
            if method in loveda_methods and method in suide_methods:
                print(f"  ✅ {method}: 两个DataModule都有")
            else:
                print(f"  ❌ {method}: 缺失")
        
        # 检查Dataset接口一致性
        loveda_dataset_methods = set(dir(LoveDADataset))
        suide_dataset_methods = set(dir(SuiDeDataset))
        
        dataset_common = loveda_dataset_methods & suide_dataset_methods
        print(f"✅ Dataset共同方法数: {len(dataset_common)}")
        
        # 检查ClassMapper接口
        loveda_mapper_methods = set(dir(LoveDAClassMapper))
        suide_mapper_methods = set(dir(SuiDeClassMapper))
        
        mapper_common = loveda_mapper_methods & suide_mapper_methods
        print(f"✅ ClassMapper共同方法数: {len(mapper_common)}")
        
        required_mapper_methods = {'num_classes', 'map_mask', 'apply_mapping'}
        for method in required_mapper_methods:
            if method in loveda_mapper_methods and method in suide_mapper_methods:
                print(f"  ✅ {method}: 两个ClassMapper都有")
            else:
                print(f"  ❌ {method}: 缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ 结构对比失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success1 = test_loveda_structure()
    success2 = test_loveda_vs_suide_structure()
    
    if success1 and success2:
        print("\n🎉 LoveDA数据模块结构测试成功！")
        print("📋 结构总结:")
        print("  ✅ LoveDA和SuiDe采用相同的模块化结构")
        print("  ✅ 每个数据集管理自己的类别映射")
        print("  ✅ Dataset和DataModule完全分离")
        print("  ✅ 接口标准化，易于扩展")
    else:
        print("\n💥 LoveDA数据模块结构测试失败！")
