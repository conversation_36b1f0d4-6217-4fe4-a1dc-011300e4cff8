#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化器Hydra实例化单元测试
测试SegmentationModule中优化器创建的正确性（注册表已移除）
"""

import sys
import unittest
import torch
import torch.nn as nn
from pathlib import Path
from omegaconf import DictConfig, OmegaConf

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.segmentation_module import SegmentationModule
from src.models.segmentation.unet import UNet

class TestOptimizerHydraInstantiation(unittest.TestCase):
    """优化器Hydra实例化测试类"""

    def setUp(self):
        """测试前准备"""
        # 创建一个简单的模型
        self.model = UNet(in_channels=3, num_classes=5)

        # 创建SegmentationModule实例的基本配置
        self.model_params = OmegaConf.create({
            "in_channels": 3,
            "num_classes": 5
        })

        self.loss_cfg = {"_target_": "torch.nn.CrossEntropyLoss"}
    
    def test_available_optimizers(self):
        """测试可用优化器注册"""
        # 检查是否有足够的优化器
        self.assertGreaterEqual(len(AVAILABLE_OPTIMIZERS), 5, "可用优化器数量不足")
        
        # 检查必要的优化器是否存在
        required_optimizers = ['adamw', 'sgd', 'adam']
        for opt_name in required_optimizers:
            self.assertIn(opt_name, AVAILABLE_OPTIMIZERS, f"缺少必要的优化器: {opt_name}")
        
        # 检查自定义优化器是否存在
        self.assertIn('remote_sensing_adamw', AVAILABLE_OPTIMIZERS, "缺少自定义优化器: remote_sensing_adamw")
    
    def test_create_optimizer_by_hydra_config(self):
        """测试通过Hydra配置创建优化器"""
        # 测试标准优化器 - AdamW
        optimizer_cfg = OmegaConf.create({
            '_target_': 'src.optimizers.standard.AdamW',
            'lr': 1e-3,
            'weight_decay': 1e-2
        })

        module = SegmentationModule(
            model_name="unet",
            model_params=self.model_params,
            num_classes=5,
            optimizer_cfg=optimizer_cfg,
            scheduler_cfg=None,
            loss_cfg=self.loss_cfg
        )
        module.architecture = self.model

        optimizer = module._create_optimizer()
        self.assertEqual(optimizer.__class__.__name__, 'AdamW', "创建AdamW优化器失败")

        # 测试先进优化器 - Lion
        optimizer_cfg = OmegaConf.create({
            '_target_': 'src.optimizers.advanced.Lion',
            'lr': 1e-4,
            'weight_decay': 1e-2
        })
        module.optimizer_cfg = optimizer_cfg
        optimizer = module._create_optimizer()
        self.assertEqual(optimizer.__class__.__name__, 'Lion', "创建Lion优化器失败")

        # 测试遥感专用优化器
        optimizer_cfg = OmegaConf.create({
            '_target_': 'src.optimizers.examples.RemoteSensingAdamW',
            'lr': 1e-3,
            'weight_decay': 1e-2
        })
        module.optimizer_cfg = optimizer_cfg
        optimizer = module._create_optimizer()
        self.assertEqual(optimizer.__class__.__name__, 'RemoteSensingAdamW', "创建遥感专用优化器失败")
    
    def test_create_optimizer_by_config(self):
        """测试通过Hydra配置创建优化器"""
        # 测试Hydra配置
        optimizer_cfg = OmegaConf.create({
            '_target_': 'torch.optim.Adam',
            'lr': 1e-3,
            'weight_decay': 1e-4
        })

        module = SegmentationModule(
            model_name="unet",
            model_params=self.model_params,
            num_classes=5,
            optimizer_cfg=optimizer_cfg,
            scheduler_cfg=None,
            loss_cfg=self.loss_cfg
        )
        module.architecture = self.model

        optimizer = module._create_optimizer()
        self.assertIsInstance(optimizer, torch.optim.Adam, "通过配置创建Adam优化器失败")
        self.assertEqual(optimizer.param_groups[0]['lr'], 1e-3, "学习率设置错误")
        self.assertEqual(optimizer.param_groups[0]['weight_decay'], 1e-4, "权重衰减设置错误")
    
    def test_optimizer_imports(self):
        """测试优化器导入"""
        # 测试标准优化器导入
        try:
            from src.optimizers.standard.adamw import AdamW
            from src.optimizers.standard.sgd import SGD
            from src.optimizers.standard.adam import Adam
            self.assertTrue(True, "标准优化器导入成功")
        except ImportError as e:
            self.fail(f"标准优化器导入失败: {e}")

        # 测试先进优化器导入
        try:
            from src.optimizers.advanced.lion import Lion
            from src.optimizers.advanced.adabelief import AdaBelief
            self.assertTrue(True, "先进优化器导入成功")
        except ImportError as e:
            self.fail(f"先进优化器导入失败: {e}")

        # 测试遥感专用优化器导入
        try:
            from src.optimizers.examples.remote_sensing_adamw import RemoteSensingAdamW
            from src.optimizers.remote_sensing.multi_scale_optimizer import MultiScaleOptimizer
            self.assertTrue(True, "遥感专用优化器导入成功")
        except ImportError as e:
            self.fail(f"遥感专用优化器导入失败: {e}")
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效配置 - 应该回退到默认优化器
        invalid_cfg = OmegaConf.create({'_target_': 'invalid.path'})

        module = SegmentationModule(
            model_name="unet",
            model_params=self.model_params,
            num_classes=5,
            optimizer_cfg=invalid_cfg,
            scheduler_cfg=None,
            loss_cfg=self.loss_cfg
        )
        module.architecture = self.model

        optimizer = module._create_optimizer()
        self.assertEqual(optimizer.__class__.__name__, 'AdamW', "无效配置应该回退到默认优化器")

if __name__ == '__main__':
    unittest.main()
