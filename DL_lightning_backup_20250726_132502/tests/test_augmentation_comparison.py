#!/usr/bin/env python3
"""
详细对比分析LoveDA和SuiDe两个数据集的数据增强策略
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

import yaml
from omegaconf import OmegaConf
import torch

def load_augmentation_configs():
    """加载两个数据集的增强配置"""
    
    # 加载SuiDe配置
    suide_path = Path(__file__).parent.parent / "configs" / "data" / "suide.yaml"
    with open(suide_path, 'r') as f:
        suide_config = yaml.safe_load(f)
    
    # 加载LoveDA配置
    loveda_path = Path(__file__).parent.parent / "configs" / "data" / "loveda.yaml"
    with open(loveda_path, 'r') as f:
        loveda_config = yaml.safe_load(f)
    
    return suide_config, loveda_config

def compare_augmentation_parameters():
    """详细对比数据增强参数"""
    
    print("🔍 详细对比LoveDA和SuiDe数据增强策略")
    print("=" * 80)
    
    suide_config, loveda_config = load_augmentation_configs()
    
    # 提取增强配置
    suide_aug = suide_config['transform_config']['augmentation']
    loveda_aug = loveda_config['transform_config']['augmentation']
    
    print("\n📋 **1. 数据增强参数对比表**")
    print("-" * 80)
    
    # 定义所有可能的增强参数
    all_params = set(suide_aug.keys()) | set(loveda_aug.keys())
    
    print(f"{'参数名称':<25} {'SuiDe':<15} {'LoveDA':<15} {'一致性':<10} {'差异分析'}")
    print("-" * 80)
    
    differences = []
    
    for param in sorted(all_params):
        suide_val = suide_aug.get(param, "未设置")
        loveda_val = loveda_aug.get(param, "未设置")
        
        # 判断一致性
        if suide_val == loveda_val:
            consistency = "✅ 一致"
            analysis = "参数值相同"
        elif suide_val == "未设置" or loveda_val == "未设置":
            consistency = "⚠️ 缺失"
            analysis = "一个数据集未配置此参数"
            differences.append((param, suide_val, loveda_val, "配置缺失"))
        else:
            consistency = "❌ 不同"
            analysis = f"数值差异: {abs(float(suide_val) - float(loveda_val)) if isinstance(suide_val, (int, float)) and isinstance(loveda_val, (int, float)) else '类型不同'}"
            differences.append((param, suide_val, loveda_val, "数值不同"))
        
        print(f"{param:<25} {str(suide_val):<15} {str(loveda_val):<15} {consistency:<10} {analysis}")
    
    return differences

def analyze_augmentation_categories():
    """按类别分析增强策略"""
    
    print("\n📊 **2. 按类别分析增强策略**")
    print("-" * 80)
    
    suide_config, loveda_config = load_augmentation_configs()
    suide_aug = suide_config['transform_config']['augmentation']
    loveda_aug = loveda_config['transform_config']['augmentation']
    
    # 定义增强类别
    categories = {
        "几何变换": ["random_crop", "random_rotate90", "flip", "transpose"],
        "颜色变换": ["random_brightness_contrast", "brightness_limit", "contrast_limit"],
        "特殊效果": ["cutout", "cutout_max_size"]
    }
    
    for category, params in categories.items():
        print(f"\n🎯 **{category}**:")
        
        for param in params:
            suide_val = suide_aug.get(param, "未设置")
            loveda_val = loveda_aug.get(param, "未设置")
            
            print(f"  {param}:")
            print(f"    SuiDe:  {suide_val}")
            print(f"    LoveDA: {loveda_val}")
            
            # 分析差异原因
            if param == "transpose":
                if suide_val != loveda_val:
                    print(f"    💡 分析: LoveDA启用transpose可能因为其图像方向更多样化")
            elif param in ["brightness_limit", "contrast_limit"]:
                if isinstance(suide_val, (int, float)) and isinstance(loveda_val, (int, float)):
                    if suide_val > loveda_val:
                        print(f"    💡 分析: SuiDe使用更强的颜色增强({suide_val} vs {loveda_val})")
                    elif loveda_val > suide_val:
                        print(f"    💡 分析: LoveDA使用更强的颜色增强({loveda_val} vs {suide_val})")
            elif param == "cutout":
                if suide_val != loveda_val:
                    if loveda_val == True and suide_val == False:
                        print(f"    💡 分析: LoveDA启用cutout，可能因为其特征更丰富，需要遮挡增强")
            elif param == "cutout_max_size":
                if isinstance(suide_val, (int, float)) and isinstance(loveda_val, (int, float)):
                    if suide_val > loveda_val:
                        print(f"    💡 分析: SuiDe使用更大的cutout区域({suide_val} vs {loveda_val})")

def evaluate_strategy_adaptability():
    """评估增强策略适配性"""
    
    print("\n🎯 **3. 增强策略适配性评估**")
    print("-" * 80)
    
    suide_config, loveda_config = load_augmentation_configs()
    
    # 数据集特征分析
    print("\n📈 **数据集特征对比**:")
    print(f"  SuiDe:")
    print(f"    - 图像尺寸: {suide_config['transform_config']['image_size']}×{suide_config['transform_config']['image_size']}")
    print(f"    - 类别数: {suide_config['dataset_config']['num_classes']}")
    print(f"    - 特点: 多尺度遥感图像，复杂地物分类")
    
    print(f"  LoveDA:")
    print(f"    - 图像尺寸: {loveda_config['transform_config']['image_size']}×{loveda_config['transform_config']['image_size']}")
    print(f"    - 类别数: {loveda_config['dataset_config']['num_classes']}")
    print(f"    - 特点: 标准遥感图像，土地覆盖分类")
    
    # 策略适配性分析
    print("\n🔍 **策略适配性分析**:")
    
    suide_aug = suide_config['transform_config']['augmentation']
    loveda_aug = loveda_config['transform_config']['augmentation']
    
    # 分析关键差异
    print("\n  关键差异及其合理性:")
    
    # Transpose差异
    if suide_aug.get('transpose') != loveda_aug.get('transpose'):
        print(f"  1. Transpose: SuiDe={suide_aug.get('transpose')}, LoveDA={loveda_aug.get('transpose')}")
        print(f"     ✅ 合理: LoveDA图像方向更多样，transpose有助于增强泛化能力")
    
    # 颜色增强强度差异
    suide_brightness = suide_aug.get('brightness_limit', 0)
    loveda_brightness = loveda_aug.get('brightness_limit', 0)
    if suide_brightness != loveda_brightness:
        print(f"  2. 亮度增强: SuiDe={suide_brightness}, LoveDA={loveda_brightness}")
        if suide_brightness > loveda_brightness:
            print(f"     ✅ 合理: SuiDe多尺度数据需要更强的颜色增强来适应不同尺度")
        else:
            print(f"     ⚠️ 需评估: LoveDA使用更强颜色增强的必要性")
    
    # Cutout差异
    if suide_aug.get('cutout') != loveda_aug.get('cutout'):
        print(f"  3. Cutout: SuiDe={suide_aug.get('cutout')}, LoveDA={loveda_aug.get('cutout')}")
        if loveda_aug.get('cutout') and not suide_aug.get('cutout'):
            print(f"     ✅ 合理: LoveDA特征更丰富，cutout有助于提高鲁棒性")

def verify_augmentation_usage():
    """验证增强配置是否真正生效"""
    
    print("\n🔧 **4. 实际验证增强参数是否生效**")
    print("-" * 80)
    
    try:
        from src.data.components.transforms import create_transforms_from_hydra_config
        
        suide_config, loveda_config = load_augmentation_configs()
        
        # 测试SuiDe transform创建
        print("\n📋 **SuiDe Transform验证**:")
        try:
            suide_transform = create_transforms_from_hydra_config(
                suide_config['transform_config'], split='train'
            )
            print("  ✅ SuiDe训练transform创建成功")
            print(f"  Transform类型: {type(suide_transform)}")
        except Exception as e:
            print(f"  ❌ SuiDe transform创建失败: {e}")
        
        # 测试LoveDA transform创建
        print("\n📋 **LoveDA Transform验证**:")
        try:
            loveda_transform = create_transforms_from_hydra_config(
                loveda_config['transform_config'], split='train'
            )
            print("  ✅ LoveDA训练transform创建成功")
            print(f"  Transform类型: {type(loveda_transform)}")
        except Exception as e:
            print(f"  ❌ LoveDA transform创建失败: {e}")
            
        return True
        
    except ImportError as e:
        print(f"  ⚠️ 无法导入transform模块: {e}")
        print("  💡 建议检查transform_config是否被正确使用")
        return False

def recommend_strategy_unification():
    """建议增强策略统一化方案"""
    
    print("\n💡 **5. 增强策略统一化建议**")
    print("-" * 80)
    
    suide_config, loveda_config = load_augmentation_configs()
    suide_aug = suide_config['transform_config']['augmentation']
    loveda_aug = loveda_config['transform_config']['augmentation']
    
    print("\n🎯 **统一化方案建议**:")
    
    print("\n  方案A: 保持差异化 (推荐)")
    print("  ✅ 优点: 针对各数据集特点优化")
    print("  ✅ 适用: 当前配置基本合理")
    print("  📋 建议微调:")
    print("    - SuiDe: 考虑启用cutout增强鲁棒性")
    print("    - LoveDA: 当前配置已较优")
    
    print("\n  方案B: 完全统一")
    print("  ⚠️ 风险: 可能不适合某个数据集特点")
    print("  📋 统一参数建议:")
    print("    - transpose: true (LoveDA经验)")
    print("    - brightness_limit: 0.175 (两者平均)")
    print("    - contrast_limit: 0.175 (两者平均)")
    print("    - cutout: true (增强鲁棒性)")
    print("    - cutout_max_size: 0.075 (两者平均)")

if __name__ == "__main__":
    print("🚀 开始数据增强策略详细对比分析...")
    
    # 1. 参数对比
    differences = compare_augmentation_parameters()
    
    # 2. 类别分析
    analyze_augmentation_categories()
    
    # 3. 适配性评估
    evaluate_strategy_adaptability()
    
    # 4. 实际验证
    usage_verified = verify_augmentation_usage()
    
    # 5. 统一化建议
    recommend_strategy_unification()
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 **分析总结**")
    print("=" * 80)
    print(f"✅ 发现 {len(differences)} 个参数差异")
    print("✅ 差异化策略基本合理")
    print("✅ 配置文件结构完整" if usage_verified else "⚠️ 需要验证transform使用")
    print("💡 建议保持差异化，微调个别参数")
