#!/usr/bin/env python3
"""
损失函数模块Hydra标准测试 - 简化版
专注于测试损失函数与Hydra的集成，避免复杂的配置依赖

测试重点：
1. 使用DictConfig而非OmegaConf.create()
2. 使用hydra.utils.instantiate()标准方式
3. 验证损失函数在Hydra环境中的正确工作
"""

import sys
from pathlib import Path
import pytest
import warnings

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import torch
import hydra
from hydra.core.global_hydra import GlobalHydra
from omegaconf import DictConfig

# 导入损失函数模块
from src.losses import AVAILABLE_LOSSES


class TestLossesHydraStandard:
    """损失函数Hydra标准测试"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        GlobalHydra.instance().clear()
    
    def teardown_method(self):
        """每个测试方法后的清理"""
        GlobalHydra.instance().clear()
    
    def test_hydra_instantiate_vs_omegaconf_create(self):
        """对比Hydra标准方式与OmegaConf.create方式"""
        
        # ✅ 正确方式：使用DictConfig
        correct_config = DictConfig({
            '_target_': 'src.losses.cross_entropy_loss.CrossEntropyLoss',
            'ignore_index': 255,
            'num_classes': 7
        })
        
        # 通过Hydra实例化
        loss_fn_correct = hydra.utils.instantiate(correct_config)
        
        assert loss_fn_correct is not None, "Hydra标准方式实例化失败"
        assert isinstance(loss_fn_correct, torch.nn.Module), "不是有效的PyTorch模块"
        
        # 测试功能
        logits = torch.randn(2, 7, 32, 32)
        targets = torch.randint(0, 7, (2, 32, 32))
        
        loss = loss_fn_correct(logits, targets)
        
        assert isinstance(loss, torch.Tensor), "未返回张量"
        assert not torch.isnan(loss), "损失为NaN"
        
        print(f"✅ Hydra标准方式测试通过: 损失值 = {loss.item():.4f}")
    
    def test_all_loss_functions_with_hydra(self):
        """使用Hydra标准方式测试所有损失函数"""
        
        # 定义所有损失函数的Hydra配置
        loss_configs = {
            'cross_entropy': DictConfig({
                '_target_': 'src.losses.cross_entropy_loss.CrossEntropyLoss',
                'ignore_index': 255,
                'num_classes': 7
            }),
            'dice': DictConfig({
                '_target_': 'src.losses.dice_loss.DiceLoss',
                'num_classes': 7,
                'smooth': 1e-5
            }),
            'focal': DictConfig({
                '_target_': 'src.losses.focal_loss.FocalLoss',
                'alpha': 1.0,
                'gamma': 2.0,
                'num_classes': 7
            }),
            'lovasz': DictConfig({
                '_target_': 'src.losses.lovasz_loss.LovaszLoss',
                'ignore_index': 255
            }),
            'combined': DictConfig({
                '_target_': 'src.losses.combined_loss.CombinedLoss',
                'loss_weights': {'ce': 0.5, 'dice': 0.5},
                'ignore_index': 255,
                'num_classes': 7
            }),
            'ce_dice': DictConfig({
                '_target_': 'src.losses.dice_loss.CEDiceLoss',
                'ce_weight': 0.6,
                'dice_weight': 0.4
            })
        }
        
        results = {}
        
        for loss_name, loss_cfg in loss_configs.items():
            try:
                # 使用Hydra标准方式实例化
                loss_fn = hydra.utils.instantiate(loss_cfg)
                
                assert loss_fn is not None, f"实例化失败: {loss_name}"
                assert isinstance(loss_fn, torch.nn.Module), f"不是PyTorch模块: {loss_name}"
                
                # 测试前向传播
                if loss_name == 'ce_dice':
                    # CEDiceLoss使用默认的14个类别
                    logits = torch.randn(2, 14, 64, 64)
                    targets = torch.randint(0, 14, (2, 64, 64))
                else:
                    logits = torch.randn(2, 7, 64, 64)
                    targets = torch.randint(0, 7, (2, 64, 64))
                
                loss = loss_fn(logits, targets)
                
                assert isinstance(loss, torch.Tensor), f"未返回张量: {loss_name}"
                assert not torch.isnan(loss), f"损失为NaN: {loss_name}"
                assert not torch.isinf(loss), f"损失为无穷大: {loss_name}"
                assert loss.item() >= 0, f"损失值为负: {loss_name}"
                
                results[loss_name] = loss.item()
                print(f"✅ {loss_name}: 损失值 = {loss.item():.4f}")
                
            except Exception as e:
                pytest.fail(f"损失函数 {loss_name} 测试失败: {e}")
        
        # 验证所有损失函数都成功测试
        assert len(results) == len(loss_configs), "部分损失函数测试失败"
        
        print(f"✅ 所有 {len(results)} 个损失函数Hydra标准测试通过")
    
    def test_hydra_recursive_instantiation(self):
        """测试Hydra递归实例化功能"""
        
        # 测试嵌套配置的递归实例化
        nested_config = DictConfig({
            'model': {
                '_target_': 'src.modules.segmentation_module.SegmentationModule',
                'model_name': 'deeplabv3plus',
                'model_params': {
                    'backbone': 'resnet50',
                    'in_channels': 3,
                    'pretrained': False
                },
                'optimizer_cfg': {
                    '_target_': 'torch.optim.AdamW',
                    'lr': 0.001
                },
                'scheduler_cfg': {
                    '_target_': 'torch.optim.lr_scheduler.StepLR',
                    'step_size': 10
                },
                'loss_cfg': {
                    '_target_': 'src.losses.cross_entropy_loss.CrossEntropyLoss',
                    'ignore_index': 255,
                    'num_classes': 7
                },
                'num_classes': 7,
                'ignore_index': 255
            }
        })
        
        try:
            with warnings.catch_warnings():
                warnings.filterwarnings("ignore", message=".*self.trainer.*")
                
                # 使用递归实例化
                model = hydra.utils.instantiate(nested_config.model, _recursive_=False)
                
                assert model is not None, "递归实例化失败"
                
                # 验证损失函数正确实例化
                assert hasattr(model, 'loss_fn'), "模型缺少损失函数"
                assert isinstance(model.loss_fn, torch.nn.Module), "损失函数不是PyTorch模块"
                
                # 测试训练步骤
                test_batch = {
                    'image': torch.randn(2, 3, 128, 128),
                    'mask': torch.randint(0, 7, (2, 128, 128))
                }
                
                model.train()
                train_loss = model.training_step(test_batch, 0)
                
                assert isinstance(train_loss, torch.Tensor), "训练步骤未返回损失张量"
                assert not torch.isnan(train_loss), "训练损失为NaN"
                
                print(f"✅ Hydra递归实例化测试通过: 训练损失 = {train_loss.item():.4f}")
                
        except Exception as e:
            pytest.fail(f"Hydra递归实例化测试失败: {e}")
    
    def test_hydra_config_validation(self):
        """测试Hydra配置验证"""
        
        # 测试有效配置
        valid_config = DictConfig({
            '_target_': 'src.losses.dice_loss.DiceLoss',
            'num_classes': 7,
            'smooth': 1e-5
        })
        
        # 验证配置结构
        assert '_target_' in valid_config, "配置缺少_target_"
        assert valid_config._target_.startswith('src.losses.'), "_target_路径不正确"
        
        # 验证实例化
        loss_fn = hydra.utils.instantiate(valid_config)
        assert loss_fn is not None, "有效配置实例化失败"
        
        # 测试无效配置
        invalid_configs = [
            # 错误的_target_
            DictConfig({'_target_': 'invalid.module.Class'}),
            # 错误的参数
            DictConfig({
                '_target_': 'src.losses.dice_loss.DiceLoss',
                'invalid_param': 'value'
            })
        ]

        for i, invalid_config in enumerate(invalid_configs):
            try:
                hydra.utils.instantiate(invalid_config)
                pytest.fail(f"无效配置 {i} 应该失败但成功了")
            except Exception:
                # 预期的异常
                pass
        
        print("✅ Hydra配置验证测试通过")
    
    def test_hydra_performance_comparison(self):
        """测试Hydra实例化性能"""
        
        import time
        
        config = DictConfig({
            '_target_': 'src.losses.cross_entropy_loss.CrossEntropyLoss',
            'ignore_index': 255,
            'num_classes': 7
        })
        
        # 测试实例化性能
        start_time = time.time()
        for _ in range(100):
            loss_fn = hydra.utils.instantiate(config)
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 100
        
        assert avg_time < 0.01, f"Hydra实例化太慢: {avg_time:.4f}s"
        
        print(f"✅ Hydra实例化性能测试通过: 平均时间 = {avg_time:.4f}s")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "--tb=short", "-s"])
