#!/usr/bin/env python3
"""
模型模块全面系统性测试
遵循项目现有测试结构和PyTorch Lightning + Hydra框架标准

测试范围：
1. 架构完整性测试
2. 网络架构测试  
3. 配置集成测试
4. 训练流程测试
5. 错误处理和边界情况测试
"""

import sys
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any, Optional
import logging
import pytest

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import hydra
from omegaconf import DictConfig, OmegaConf
import torch
import lightning.pytorch as pl

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestModelArchitectures:
    """模型架构测试类"""
    
    def test_segmentation_module_inheritance(self):
        """测试SegmentationModule正确继承LightningModule"""
        from src.modules.segmentation_module import SegmentationModule
        
        assert issubclass(SegmentationModule, pl.LightningModule), \
            "SegmentationModule未正确继承LightningModule"
    
    def test_required_methods_exist(self):
        """测试必需方法存在"""
        from src.modules.segmentation_module import SegmentationModule
        
        required_methods = [
            'training_step', 'validation_step', 'test_step',
            'configure_optimizers', 'forward'
        ]
        
        for method in required_methods:
            assert hasattr(SegmentationModule, method), f"缺少必需方法: {method}"
            assert callable(getattr(SegmentationModule, method)), f"方法不可调用: {method}"
    
    def test_available_architectures_registry(self):
        """测试架构注册表"""
        from src.models.segmentation import AVAILABLE_ARCHITECTURES
        
        assert AVAILABLE_ARCHITECTURES, "AVAILABLE_ARCHITECTURES为空"
        assert len(AVAILABLE_ARCHITECTURES) > 0, "没有注册任何架构"
        
        # 验证预期的架构存在
        expected_archs = ['deeplabv3plus', 'unet', 'unetpp']
        for arch in expected_archs:
            assert arch in AVAILABLE_ARCHITECTURES, f"缺少预期架构: {arch}"


class TestNetworkArchitectures:
    """网络架构测试类"""
    
    @pytest.mark.parametrize("arch_name", [
        'deeplabv3plus', 'unet', 'unetpp', 'swin_unet'
    ])
    def test_architecture_instantiation(self, arch_name):
        """测试各个架构的实例化"""
        from src.models.segmentation import AVAILABLE_ARCHITECTURES
        
        if arch_name not in AVAILABLE_ARCHITECTURES:
            pytest.skip(f"架构 {arch_name} 不可用")
        
        arch_class = AVAILABLE_ARCHITECTURES[arch_name]
        
        # 根据架构类型创建实例
        if arch_name == 'deeplabv3plus':
            model = arch_class(num_classes=7, backbone='resnet50', in_channels=3)
        elif arch_name in ['unet', 'unetpp']:
            model = arch_class(num_classes=7, in_channels=3)
        elif arch_name in ['swin_unet', 'swin']:
            model = arch_class(num_classes=7)
        else:
            model = arch_class(num_classes=7)
        
        assert model is not None, f"架构 {arch_name} 实例化失败"
        assert isinstance(model, torch.nn.Module), f"架构 {arch_name} 不是有效的PyTorch模块"
    
    @pytest.mark.parametrize("batch_size,channels,height,width", [
        (2, 3, 256, 256),
        (4, 3, 512, 512),
        (2, 4, 224, 224),  # 多通道测试
    ])
    def test_forward_propagation(self, batch_size, channels, height, width):
        """测试前向传播功能"""
        from src.models.segmentation import AVAILABLE_ARCHITECTURES
        
        # 测试DeepLabV3+（最常用的架构）
        if 'deeplabv3plus' not in AVAILABLE_ARCHITECTURES:
            pytest.skip("DeepLabV3+架构不可用")
        
        arch_class = AVAILABLE_ARCHITECTURES['deeplabv3plus']
        model = arch_class(num_classes=7, backbone='resnet50', in_channels=channels)
        
        # 设置为eval模式避免BatchNorm问题
        model.eval()
        
        input_tensor = torch.randn(batch_size, channels, height, width)
        
        with torch.no_grad():
            output = model(input_tensor)
        
        # 验证输出形状
        expected_shape = (batch_size, 7, height, width)
        assert output.shape == expected_shape, \
            f"输出形状不匹配: 期望{expected_shape}, 实际{output.shape}"


class TestConfigIntegration:
    """配置集成测试类"""
    
    def test_hydra_model_instantiation(self):
        """测试通过Hydra实例化模型"""
        # 创建测试配置
        test_config = OmegaConf.create({
            'model': {
                '_target_': 'src.modules.segmentation_module.SegmentationModule',
                'model_name': 'deeplabv3plus',
                'model_params': {
                    'backbone': 'resnet50',
                    'in_channels': 3,
                    'pretrained': False  # 避免下载预训练权重
                },
                'num_classes': 7,
                'ignore_index': 255
            },
            'optimizer': {
                '_target_': 'torch.optim.AdamW',
                'lr': 0.001,
                'weight_decay': 0.01
            },
            'scheduler': {
                '_target_': 'torch.optim.lr_scheduler.CosineAnnealingLR',
                'T_max': 100,
                'eta_min': 1e-6
            },
            'loss': {
                '_target_': 'torch.nn.CrossEntropyLoss',
                'ignore_index': 255
            }
        })
        
        # 测试通过Hydra实例化模型
        model = hydra.utils.instantiate(
            test_config.model,
            optimizer_cfg=test_config.optimizer,
            scheduler_cfg=test_config.scheduler,
            loss_cfg=test_config.loss,
            _recursive_=False
        )
        
        assert isinstance(model, pl.LightningModule), "模型实例化失败"
        
        # 测试优化器配置
        optimizer_config = model.configure_optimizers()
        assert isinstance(optimizer_config, dict), "优化器配置返回格式错误"
        assert 'optimizer' in optimizer_config, "缺少优化器配置"
        assert 'lr_scheduler' in optimizer_config, "缺少调度器配置"


class TestTrainingWorkflow:
    """训练流程测试类"""
    
    def test_training_step(self):
        """测试训练步骤"""
        from src.modules.segmentation_module import SegmentationModule
        import warnings

        # 抑制Lightning的trainer警告（测试环境下正常）
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", message=".*self.trainer.*")

            model = SegmentationModule(
                model_name='deeplabv3plus',
                model_params=OmegaConf.create({
                    'backbone': 'resnet50',
                    'in_channels': 3,
                    'pretrained': False
                }),
                optimizer_cfg=OmegaConf.create({
                    '_target_': 'torch.optim.AdamW',
                    'lr': 0.001,
                    'weight_decay': 0.01
                }),
                scheduler_cfg=OmegaConf.create({
                    '_target_': 'torch.optim.lr_scheduler.CosineAnnealingLR',
                    'T_max': 10,
                    'eta_min': 1e-6
                }),
                loss_cfg=OmegaConf.create({
                    '_target_': 'torch.nn.CrossEntropyLoss',
                    'ignore_index': 255
                }),
                num_classes=7,
                ignore_index=255
            )

            # 创建测试数据
            batch_size = 2
            test_batch = {
                'image': torch.randn(batch_size, 3, 256, 256),
                'mask': torch.randint(0, 7, (batch_size, 256, 256))
            }

            # 测试训练步骤
            model.train()
            train_loss = model.training_step(test_batch, 0)

            assert isinstance(train_loss, torch.Tensor), "训练步骤未返回损失张量"
            assert train_loss.dim() == 0, "损失张量维度错误"
            assert not torch.isnan(train_loss), "损失为NaN"
            assert not torch.isinf(train_loss), "损失为无穷大"
    
    def test_validation_step(self):
        """测试验证步骤"""
        from src.modules.segmentation_module import SegmentationModule
        import warnings

        # 抑制Lightning的trainer警告（测试环境下正常）
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", message=".*self.trainer.*")

            model = SegmentationModule(
                model_name='deeplabv3plus',
                model_params=OmegaConf.create({
                    'backbone': 'resnet50',
                    'in_channels': 3,
                    'pretrained': False
                }),
                optimizer_cfg=OmegaConf.create({
                    '_target_': 'torch.optim.AdamW',
                    'lr': 0.001
                }),
                scheduler_cfg=OmegaConf.create({
                    '_target_': 'torch.optim.lr_scheduler.StepLR',
                    'step_size': 10
                }),
                loss_cfg=OmegaConf.create({
                    '_target_': 'torch.nn.CrossEntropyLoss'
                }),
                num_classes=7
            )

            # 创建测试数据
            test_batch = {
                'image': torch.randn(2, 3, 256, 256),
                'mask': torch.randint(0, 7, (2, 256, 256))
            }

            # 测试验证步骤
            model.eval()
            with torch.no_grad():
                val_result = model.validation_step(test_batch, 0)

            # 验证步骤应该正常完成（可能返回None或损失值）
            if val_result is not None:
                assert isinstance(val_result, torch.Tensor), "验证步骤返回值类型错误"


class TestErrorHandling:
    """错误处理测试类"""
    
    def test_unsupported_architecture_error(self):
        """测试不支持的架构错误处理"""
        from src.modules.segmentation_module import SegmentationModule
        
        with pytest.raises(ValueError, match="不支持的模型架构"):
            SegmentationModule(
                model_name='unsupported_model',
                model_params=OmegaConf.create({}),
                optimizer_cfg=OmegaConf.create({'_target_': 'torch.optim.Adam', 'lr': 0.001}),
                scheduler_cfg=OmegaConf.create({'_target_': 'torch.optim.lr_scheduler.StepLR', 'step_size': 10}),
                loss_cfg=OmegaConf.create({'_target_': 'torch.nn.CrossEntropyLoss'}),
                num_classes=7
            )
    
    def test_model_parameter_count(self):
        """测试模型参数数量合理性"""
        from src.modules.segmentation_module import SegmentationModule
        
        model = SegmentationModule(
            model_name='deeplabv3plus',
            model_params=OmegaConf.create({
                'backbone': 'resnet50',
                'in_channels': 3,
                'pretrained': False
            }),
            optimizer_cfg=OmegaConf.create({'_target_': 'torch.optim.Adam', 'lr': 0.001}),
            scheduler_cfg=OmegaConf.create({'_target_': 'torch.optim.lr_scheduler.StepLR', 'step_size': 10}),
            loss_cfg=OmegaConf.create({'_target_': 'torch.nn.CrossEntropyLoss'}),
            num_classes=7
        )
        
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        # DeepLabV3+大约有20-40M参数
        assert 1e6 < total_params < 1e8, f"参数数量异常: {total_params:,}"
        assert trainable_params > 0, "没有可训练参数"
        assert trainable_params <= total_params, "可训练参数数量异常"


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "--tb=short"])
