#!/usr/bin/env python3
"""
验证LoveDA数据集样本一致性

对比新旧版本加载的样本是否一致
"""

import sys
from pathlib import Path

# 确保可以导入src模块
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))


def load_samples_from_list_file(data_dir: Path, split: str):
    """使用旧版本方式从列表文件加载样本"""
    list_file = data_dir / f"{split}_list.txt"
    samples = []
    
    with open(list_file, 'r') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
                
            parts = line.split()
            if len(parts) >= 2:
                img_path, mask_path = parts[:2]
                # 去掉开头的 ./
                img_path = img_path.lstrip('./')
                mask_path = mask_path.lstrip('./')
                
                samples.append({
                    'image': data_dir / img_path,
                    'mask': data_dir / mask_path
                })
    
    return samples


def load_samples_from_directory_scan(data_dir: Path, split: str):
    """使用新版本方式从目录扫描加载样本"""
    samples = []
    
    # LoveDA目录结构: Train/Val/Test (首字母大写)
    split_name = split.capitalize()
    split_dir = data_dir / split_name
    
    if not split_dir.exists():
        return samples
    
    # LoveDA有Rural和Urban两个子目录
    for domain in ['Rural', 'Urban']:
        domain_dir = split_dir / domain
        if not domain_dir.exists():
            continue
            
        image_dir = domain_dir / 'images_png'
        mask_dir = domain_dir / 'masks_png'
        
        if not image_dir.exists() or not mask_dir.exists():
            continue
        
        # 加载图像和对应的标签
        for image_path in sorted(image_dir.glob('*.png')):  # 排序确保一致性
            mask_path = mask_dir / image_path.name
            if mask_path.exists():
                samples.append({
                    'image': image_path,
                    'mask': mask_path,
                    'domain': domain
                })
    
    return samples


def compare_sample_lists(list_samples, scan_samples, split):
    """对比两种方式加载的样本列表"""
    print(f"\n🔍 对比{split}集样本:")
    print(f"列表文件方式: {len(list_samples)} 个样本")
    print(f"目录扫描方式: {len(scan_samples)} 个样本")
    
    if len(list_samples) != len(scan_samples):
        print(f"❌ 样本数量不一致!")
        return False
    
    # 创建样本路径集合用于对比
    list_paths = set()
    for sample in list_samples:
        # 获取相对路径用于对比
        rel_img = sample['image'].relative_to(sample['image'].parents[3])
        rel_mask = sample['mask'].relative_to(sample['mask'].parents[3])
        list_paths.add((str(rel_img), str(rel_mask)))
    
    scan_paths = set()
    for sample in scan_samples:
        # 获取相对路径用于对比
        rel_img = sample['image'].relative_to(sample['image'].parents[3])
        rel_mask = sample['mask'].relative_to(sample['mask'].parents[3])
        scan_paths.add((str(rel_img), str(rel_mask)))
    
    # 检查差异
    only_in_list = list_paths - scan_paths
    only_in_scan = scan_paths - list_paths
    
    if only_in_list:
        print(f"❌ 只在列表文件中的样本: {len(only_in_list)}")
        for img, mask in list(only_in_list)[:5]:  # 只显示前5个
            print(f"   {img}")
    
    if only_in_scan:
        print(f"❌ 只在目录扫描中的样本: {len(only_in_scan)}")
        for img, mask in list(only_in_scan)[:5]:  # 只显示前5个
            print(f"   {img}")
    
    if not only_in_list and not only_in_scan:
        print("✅ 样本完全一致!")
        return True
    else:
        print("❌ 样本不一致!")
        return False


def main():
    """主函数"""
    print("🔍 验证LoveDA数据集样本一致性")
    print("=" * 50)
    
    # 数据目录
    data_dir = Path("/home/<USER>/DeepLearing/SuiDe_Project/data/LoveDA")
    
    if not data_dir.exists():
        print(f"❌ 数据目录不存在: {data_dir}")
        return False
    
    all_consistent = True
    
    # 检查训练集和验证集
    for split in ['train', 'val']:
        try:
            # 使用列表文件加载
            list_samples = load_samples_from_list_file(data_dir, split)
            
            # 使用目录扫描加载
            scan_samples = load_samples_from_directory_scan(data_dir, split)
            
            # 对比结果
            consistent = compare_sample_lists(list_samples, scan_samples, split)
            all_consistent = all_consistent and consistent
            
        except Exception as e:
            print(f"❌ {split}集验证失败: {e}")
            all_consistent = False
    
    print(f"\n📊 总体结果:")
    if all_consistent:
        print("✅ 新旧版本加载的样本完全一致!")
        print("✅ 目录扫描方式可以安全使用")
    else:
        print("❌ 新旧版本加载的样本存在差异")
        print("⚠️ 建议修改为使用列表文件方式")
    
    return all_consistent


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
