#!/usr/bin/env python3
"""
验证配置文件中的参数是否真正被使用
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

import yaml
from omegaconf import OmegaConf
import torch

def test_suide_config_usage():
    """验证SuiDe配置文件的参数是否真正被使用"""
    
    print("🔍 验证SuiDe配置文件参数使用情况...")
    
    try:
        from src.data.suide_datamodule import SuiDeDataModule
        
        # 1. 加载配置文件
        config_path = Path(__file__).parent.parent / "configs" / "data" / "suide.yaml"
        with open(config_path, 'r') as f:
            config_data = yaml.safe_load(f)
        
        # 2. 提取配置参数
        dataset_config = config_data['dataset_config']
        dataloader_config = config_data['dataloader_config']
        transform_config = config_data['transform_config']
        
        print("📋 配置文件中的参数:")
        print(f"  类别数: {dataset_config['num_classes']}")
        print(f"  训练级别: {dataset_config['training_level']}")
        print(f"  批次大小: {dataloader_config['batch_size']}")
        print(f"  工作进程: {dataloader_config['num_workers']}")
        print(f"  图像尺寸: {transform_config['image_size']}")
        print(f"  多尺度权重: {dataset_config['scale_weights']}")
        
        # 3. 创建DataModule（使用小批次测试）
        test_config = OmegaConf.create({
            'data_dir': "data/Data_SRC/Dataset_v2.2",
            'class_info_path': "data/Data_SRC/Dataset_v2.2/metadata/class_info.json",
            'training_level': dataset_config['training_level'],
            'num_classes': dataset_config['num_classes'],
            'ignore_index': dataset_config['ignore_index'],
            'scale_weights': dataset_config['scale_weights']
        })
        
        test_dataloader_config = OmegaConf.create({
            'batch_size': 2,  # 测试用小批次
            'num_workers': 0,
            'pin_memory': dataloader_config['pin_memory']
        })
        
        dm = SuiDeDataModule(
            dataset_config=test_config,
            transform_config=None,
            dataloader_config=test_dataloader_config
        )
        
        # 4. Setup并验证参数
        dm.setup('fit')
        
        print("\n✅ 验证DataModule中的实际参数:")
        print(f"  实际类别数: {dm.num_classes}")
        print(f"  实际训练级别: {dm.class_mapper.training_level}")
        print(f"  实际忽略索引: {test_config.ignore_index}")
        
        # 5. 验证数据加载器参数
        train_loader = dm.train_dataloader()
        print(f"  实际批次大小: {train_loader.batch_size}")
        print(f"  实际工作进程: {train_loader.num_workers}")
        print(f"  实际pin_memory: {train_loader.pin_memory}")
        
        # 6. 验证多尺度权重是否生效
        print(f"  训练集样本数: {len(dm.train_dataset)}")
        print(f"  验证集样本数: {len(dm.val_dataset)}")
        
        # 7. 验证数据格式
        batch = next(iter(train_loader))
        print(f"  图像shape: {batch['image'].shape}")
        print(f"  掩码shape: {batch['mask'].shape}")
        
        # 8. 对比配置文件vs实际使用
        print("\n📊 配置文件 vs 实际使用对比:")
        config_vs_actual = [
            ("类别数", dataset_config['num_classes'], dm.num_classes),
            ("训练级别", dataset_config['training_level'], dm.class_mapper.training_level),
            ("pin_memory", dataloader_config['pin_memory'], train_loader.pin_memory),
        ]
        
        all_match = True
        for name, config_val, actual_val in config_vs_actual:
            match = "✅" if config_val == actual_val else "❌"
            print(f"  {name}: 配置{config_val} vs 实际{actual_val} {match}")
            if config_val != actual_val:
                all_match = False
        
        return all_match
        
    except Exception as e:
        print(f"❌ SuiDe配置验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_loveda_config_usage():
    """验证LoveDA配置文件的参数是否真正被使用"""
    
    print("\n🔍 验证LoveDA配置文件参数使用情况...")
    
    try:
        from src.data.loveda_datamodule import LoveDADataModule
        
        # 1. 加载配置文件
        config_path = Path(__file__).parent.parent / "configs" / "data" / "loveda.yaml"
        with open(config_path, 'r') as f:
            config_data = yaml.safe_load(f)
        
        # 2. 提取配置参数
        dataset_config = config_data['dataset_config']
        dataloader_config = config_data['dataloader_config']
        transform_config = config_data['transform_config']
        
        print("📋 配置文件中的参数:")
        print(f"  类别数: {dataset_config['num_classes']}")
        print(f"  忽略索引: {dataset_config['ignore_index']}")
        print(f"  包含背景: {dataset_config['include_background']}")
        print(f"  忽略nodata: {dataset_config['ignore_nodata']}")
        print(f"  批次大小: {dataloader_config['batch_size']}")
        print(f"  图像尺寸: {transform_config['image_size']}")
        
        # 3. 创建DataModule（使用小批次测试）
        test_config = OmegaConf.create({
            'data_dir': "data/LoveDA",
            'num_classes': dataset_config['num_classes'],
            'ignore_index': dataset_config['ignore_index']
        })
        
        test_dataloader_config = OmegaConf.create({
            'batch_size': 2,  # 测试用小批次
            'num_workers': 0,
            'pin_memory': dataloader_config['pin_memory']
        })
        
        dm = LoveDADataModule(
            dataset_config=test_config,
            transform_config=None,
            dataloader_config=test_dataloader_config
        )
        
        # 4. Setup并验证参数
        dm.setup('fit')
        
        print("\n✅ 验证DataModule中的实际参数:")
        print(f"  实际类别数: {dm.num_classes}")
        
        # 5. 验证数据加载器参数
        train_loader = dm.train_dataloader()
        print(f"  实际批次大小: {train_loader.batch_size}")
        print(f"  实际工作进程: {train_loader.num_workers}")
        print(f"  实际pin_memory: {train_loader.pin_memory}")
        
        # 6. 验证数据集大小
        print(f"  训练集样本数: {len(dm.train_dataset)}")
        print(f"  验证集样本数: {len(dm.val_dataset)}")
        
        # 7. 验证数据格式
        batch = next(iter(train_loader))
        print(f"  图像shape: {batch['image'].shape}")
        print(f"  掩码shape: {batch['mask'].shape}")
        
        # 8. 对比配置文件vs实际使用
        print("\n📊 配置文件 vs 实际使用对比:")
        config_vs_actual = [
            ("类别数", dataset_config['num_classes'], dm.num_classes),
            ("pin_memory", dataloader_config['pin_memory'], train_loader.pin_memory),
        ]
        
        all_match = True
        for name, config_val, actual_val in config_vs_actual:
            match = "✅" if config_val == actual_val else "❌"
            print(f"  {name}: 配置{config_val} vs 实际{actual_val} {match}")
            if config_val != actual_val:
                all_match = False
        
        return all_match
        
    except Exception as e:
        print(f"❌ LoveDA配置验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_differences():
    """对比两个配置文件的关键差异"""
    
    print("\n🔍 对比SuiDe和LoveDA配置文件差异...")
    
    try:
        # 加载两个配置文件
        suide_path = Path(__file__).parent.parent / "configs" / "data" / "suide.yaml"
        loveda_path = Path(__file__).parent.parent / "configs" / "data" / "loveda.yaml"
        
        with open(suide_path, 'r') as f:
            suide_config = yaml.safe_load(f)
        
        with open(loveda_path, 'r') as f:
            loveda_config = yaml.safe_load(f)
        
        print("📊 关键差异对比:")
        
        # 对比关键参数
        comparisons = [
            ("目标类", suide_config['_target_'], loveda_config['_target_']),
            ("类别数", suide_config['dataset_config']['num_classes'], loveda_config['dataset_config']['num_classes']),
            ("批次大小", suide_config['dataloader_config']['batch_size'], loveda_config['dataloader_config']['batch_size']),
            ("图像尺寸", suide_config['transform_config']['image_size'], loveda_config['transform_config']['image_size']),
        ]
        
        for name, suide_val, loveda_val in comparisons:
            print(f"  {name}:")
            print(f"    SuiDe: {suide_val}")
            print(f"    LoveDA: {loveda_val}")
            print(f"    相同: {'✅' if suide_val == loveda_val else '❌'}")
            print()
        
        # 检查SuiDe特有的配置
        print("🎯 SuiDe特有配置:")
        if 'scale_weights' in suide_config['dataset_config']:
            print(f"  多尺度权重: {suide_config['dataset_config']['scale_weights']}")
        
        # 检查LoveDA特有的配置
        print("🎯 LoveDA特有配置:")
        loveda_dataset = loveda_config['dataset_config']
        if 'include_background' in loveda_dataset:
            print(f"  包含背景: {loveda_dataset['include_background']}")
        if 'ignore_nodata' in loveda_dataset:
            print(f"  忽略nodata: {loveda_dataset['ignore_nodata']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置对比失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始配置文件使用验证...")
    
    success1 = test_suide_config_usage()
    success2 = test_loveda_config_usage()
    success3 = test_config_differences()
    
    if success1 and success2 and success3:
        print("\n🎉 配置文件验证成功！")
        print("📋 验证结果:")
        print("  ✅ SuiDe配置参数正确使用")
        print("  ✅ LoveDA配置参数正确使用")
        print("  ✅ 两个配置文件差异明确")
        print("  ✅ 配置文件与实际代码一致")
    else:
        print("\n💥 配置文件验证失败！")
        if not success1:
            print("  ❌ SuiDe配置验证失败")
        if not success2:
            print("  ❌ LoveDA配置验证失败")
        if not success3:
            print("  ❌ 配置对比失败")
