#!/usr/bin/env python3
"""
完整的@data数据集测试

测试SuiDe和LoveDA数据集在实际@data目录下的工作情况
"""

import sys
import traceback
from pathlib import Path

# 确保可以导入src模块
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))


def test_suide_data_loading():
    """测试SuiDe数据集加载"""
    print("🧪 测试SuiDe数据集加载")
    print("-" * 50)
    
    try:
        import hydra
        from omegaconf import OmegaConf
        
        # 加载SuiDe配置
        config_path = project_root / 'configs/data/suide_v2.1_new.yaml'
        config = OmegaConf.load(config_path)
        
        print(f"📁 配置的数据路径: {config.dataset_config.data_dir}")
        print(f"📁 类别信息路径: {config.dataset_config.class_info_path}")
        
        # 检查数据路径是否存在
        data_dir = Path(config.dataset_config.data_dir)
        if not data_dir.is_absolute():
            # 相对路径，从tests目录解析
            data_dir = Path(__file__).parent.parent / config.dataset_config.data_dir
        
        print(f"📁 解析后的数据路径: {data_dir}")
        print(f"📁 数据路径存在: {data_dir.exists()}")
        
        if not data_dir.exists():
            print(f"❌ 数据目录不存在: {data_dir}")
            return False
        
        # 实例化DataModule
        datamodule = hydra.utils.instantiate(config)
        print(f"✅ SuiDe DataModule实例化成功")
        
        # 设置数据
        datamodule.setup('fit')
        print(f"✅ 数据设置完成")
        
        # 获取数据加载器
        train_loader = datamodule.train_dataloader()
        val_loader = datamodule.val_dataloader()
        
        print(f"📊 训练集大小: {len(datamodule.train_dataset)}")
        print(f"📊 验证集大小: {len(datamodule.val_dataset)}")
        
        # 测试数据加载
        train_batch = next(iter(train_loader))
        val_batch = next(iter(val_loader))
        
        print(f"✅ 数据加载成功")
        print(f"   训练批次: image {train_batch['image'].shape}, mask {train_batch['mask'].shape}")
        print(f"   验证批次: image {val_batch['image'].shape}, mask {val_batch['mask'].shape}")
        print(f"   图像数据类型: {train_batch['image'].dtype}")
        print(f"   标签数据类型: {train_batch['mask'].dtype}")
        print(f"   图像值范围: [{train_batch['image'].min():.3f}, {train_batch['image'].max():.3f}]")
        print(f"   标签值范围: [{train_batch['mask'].min()}, {train_batch['mask'].max()}]")
        
        return True
        
    except Exception as e:
        print(f"❌ SuiDe数据集测试失败: {e}")
        traceback.print_exc()
        return False


def test_loveda_data_loading():
    """测试LoveDA数据集加载"""
    print("\n🧪 测试LoveDA数据集加载")
    print("-" * 50)
    
    try:
        import hydra
        from omegaconf import OmegaConf
        
        # 加载LoveDA配置
        config_path = project_root / 'configs/data/loveda.yaml'
        config = OmegaConf.load(config_path)
        
        print(f"📁 配置的数据路径: {config.dataset_config.data_dir}")
        
        # 检查数据路径是否存在
        data_dir = Path(config.dataset_config.data_dir)
        if not data_dir.is_absolute():
            # 相对路径，从tests目录解析
            data_dir = Path(__file__).parent.parent / config.dataset_config.data_dir
        
        print(f"📁 解析后的数据路径: {data_dir}")
        print(f"📁 数据路径存在: {data_dir.exists()}")
        
        if not data_dir.exists():
            print(f"❌ 数据目录不存在: {data_dir}")
            return False
        
        # 检查列表文件
        train_list = data_dir / "train_list.txt"
        val_list = data_dir / "val_list.txt"
        print(f"📁 训练列表文件存在: {train_list.exists()}")
        print(f"📁 验证列表文件存在: {val_list.exists()}")
        
        # 实例化DataModule
        datamodule = hydra.utils.instantiate(config)
        print(f"✅ LoveDA DataModule实例化成功")
        
        # 设置数据
        datamodule.setup('fit')
        print(f"✅ 数据设置完成")
        
        # 获取数据加载器
        train_loader = datamodule.train_dataloader()
        val_loader = datamodule.val_dataloader()
        
        print(f"📊 训练集大小: {len(datamodule.train_dataset)}")
        print(f"📊 验证集大小: {len(datamodule.val_dataset)}")
        
        # 测试数据加载
        train_batch = next(iter(train_loader))
        val_batch = next(iter(val_loader))
        
        print(f"✅ 数据加载成功")
        print(f"   训练批次: image {train_batch['image'].shape}, mask {train_batch['mask'].shape}")
        print(f"   验证批次: image {val_batch['image'].shape}, mask {val_batch['mask'].shape}")
        print(f"   图像数据类型: {train_batch['image'].dtype}")
        print(f"   标签数据类型: {train_batch['mask'].dtype}")
        print(f"   图像值范围: [{train_batch['image'].min():.3f}, {train_batch['image'].max():.3f}]")
        print(f"   标签值范围: [{train_batch['mask'].min()}, {train_batch['mask'].max()}]")
        
        # 检查是否使用了列表文件
        if hasattr(datamodule.train_dataset, 'samples'):
            sample = datamodule.train_dataset.samples[0]
            print(f"✅ 使用列表文件加载，样本示例:")
            print(f"   图像: {sample['image']}")
            print(f"   标签: {sample['mask']}")
            print(f"   域: {sample['domain']}")
        
        return True
        
    except Exception as e:
        print(f"❌ LoveDA数据集测试失败: {e}")
        traceback.print_exc()
        return False


def test_data_consistency():
    """测试数据一致性"""
    print("\n🧪 测试数据一致性")
    print("-" * 50)
    
    try:
        import hydra
        from omegaconf import OmegaConf
        import torch
        
        # 加载两个数据集
        suide_config = OmegaConf.load(project_root / 'configs/data/suide_v2.1_new.yaml')
        loveda_config = OmegaConf.load(project_root / 'configs/data/loveda.yaml')
        
        suide_dm = hydra.utils.instantiate(suide_config)
        loveda_dm = hydra.utils.instantiate(loveda_config)
        
        suide_dm.setup('fit')
        loveda_dm.setup('fit')
        
        # 测试数据加载的一致性
        print("🔄 测试多次加载的一致性...")
        
        # SuiDe一致性测试
        suide_loader = suide_dm.train_dataloader()
        suide_batch1 = next(iter(suide_loader))
        suide_batch2 = next(iter(suide_loader))
        
        print(f"✅ SuiDe批次形状一致: {suide_batch1['image'].shape == suide_batch2['image'].shape}")
        print(f"✅ SuiDe数据类型一致: {suide_batch1['image'].dtype == suide_batch2['image'].dtype}")
        
        # LoveDA一致性测试
        loveda_loader = loveda_dm.train_dataloader()
        loveda_batch1 = next(iter(loveda_loader))
        loveda_batch2 = next(iter(loveda_loader))
        
        print(f"✅ LoveDA批次形状一致: {loveda_batch1['image'].shape == loveda_batch2['image'].shape}")
        print(f"✅ LoveDA数据类型一致: {loveda_batch1['image'].dtype == loveda_batch2['image'].dtype}")
        
        # 测试数据增强是否生效
        print("🔄 测试数据增强效果...")
        
        # 检查是否有随机性（数据增强生效的标志）
        suide_same = torch.equal(suide_batch1['image'], suide_batch2['image'])
        loveda_same = torch.equal(loveda_batch1['image'], loveda_batch2['image'])
        
        print(f"✅ SuiDe数据增强生效: {not suide_same}")
        print(f"✅ LoveDA数据增强生效: {not loveda_same}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据一致性测试失败: {e}")
        traceback.print_exc()
        return False


def test_data_paths():
    """测试数据路径配置"""
    print("\n🧪 测试数据路径配置")
    print("-" * 50)
    
    try:
        from omegaconf import OmegaConf
        
        # 检查配置文件中的路径
        suide_config = OmegaConf.load(project_root / 'configs/data/suide_v2.1_new.yaml')
        loveda_config = OmegaConf.load(project_root / 'configs/data/loveda.yaml')
        
        print("📁 SuiDe配置路径:")
        print(f"   数据目录: {suide_config.dataset_config.data_dir}")
        print(f"   类别信息: {suide_config.dataset_config.class_info_path}")
        
        print("📁 LoveDA配置路径:")
        print(f"   数据目录: {loveda_config.dataset_config.data_dir}")
        
        # 检查实际路径存在性
        suide_data_dir = project_root / suide_config.dataset_config.data_dir
        loveda_data_dir = project_root / loveda_config.dataset_config.data_dir
        
        print(f"✅ SuiDe数据目录存在: {suide_data_dir.exists()}")
        print(f"✅ LoveDA数据目录存在: {loveda_data_dir.exists()}")
        
        if suide_data_dir.exists():
            class_info_path = project_root / suide_config.dataset_config.class_info_path
            print(f"✅ SuiDe类别信息文件存在: {class_info_path.exists()}")
            
            # 检查annotation文件
            train_ann = suide_data_dir / "annotations" / "train.json"
            val_ann = suide_data_dir / "annotations" / "val.json"
            print(f"✅ SuiDe训练annotation存在: {train_ann.exists()}")
            print(f"✅ SuiDe验证annotation存在: {val_ann.exists()}")
        
        if loveda_data_dir.exists():
            train_list = loveda_data_dir / "train_list.txt"
            val_list = loveda_data_dir / "val_list.txt"
            print(f"✅ LoveDA训练列表存在: {train_list.exists()}")
            print(f"✅ LoveDA验证列表存在: {val_list.exists()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据路径测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 @data数据集完整测试")
    print("=" * 60)
    print("测试SuiDe和LoveDA数据集在实际@data目录下的工作情况")
    print()
    
    tests = [
        ("数据路径配置", test_data_paths),
        ("SuiDe数据加载", test_suide_data_loading),
        ("LoveDA数据加载", test_loveda_data_loading),
        ("数据一致性", test_data_consistency)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有@data数据集测试通过！")
        print("\n✨ 验证成果:")
        print("  ✅ SuiDe数据集正常工作")
        print("  ✅ LoveDA数据集正常工作")
        print("  ✅ 数据路径配置正确")
        print("  ✅ 数据加载一致性良好")
        print("  ✅ 数据增强功能正常")
        print("  ✅ 列表文件机制工作正常")
        
        return True
    else:
        print(f"\n❌ 有 {total - passed} 个测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
