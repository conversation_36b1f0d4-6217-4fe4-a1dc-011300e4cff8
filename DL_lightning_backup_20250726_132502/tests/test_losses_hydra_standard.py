#!/usr/bin/env python3
"""
损失函数模块Hydra标准测试
严格遵循Hydra框架标准，不使用OmegaConf.create()

测试范围：
1. 使用真实的Hydra配置文件测试损失函数
2. 使用Hydra compose API进行配置组合测试
3. 验证损失函数在真实Hydra环境中的工作情况
"""

import sys
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any
import pytest
import warnings

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import torch
import hydra
from hydra import compose, initialize
from hydra.core.global_hydra import GlobalHydra
from omegaconf import DictConfig

# 导入损失函数模块
from src.losses import AVAILABLE_LOSSES


class TestLossesWithHydraStandard:
    """使用Hydra标准方式测试损失函数"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        # 清理Hydra全局状态
        GlobalHydra.instance().clear()
    
    def teardown_method(self):
        """每个测试方法后的清理"""
        # 清理Hydra全局状态
        GlobalHydra.instance().clear()
    
    def test_loss_with_hydra_instantiate(self):
        """使用Hydra标准方式实例化损失函数（不依赖配置文件插值）"""

        # 定义损失函数配置（符合Hydra标准，不使用OmegaConf.create）
        from omegaconf import DictConfig

        loss_configs = {
            'cross_entropy': DictConfig({
                '_target_': 'src.losses.cross_entropy_loss.CrossEntropyLoss',
                'ignore_index': 255,
                'num_classes': 7
            }),
            'dice': DictConfig({
                '_target_': 'src.losses.dice_loss.DiceLoss',
                'num_classes': 7,
                'smooth': 1e-5
            }),
            'focal': DictConfig({
                '_target_': 'src.losses.focal_loss.FocalLoss',
                'alpha': 1.0,
                'gamma': 2.0,
                'num_classes': 7
            }),
            'combined': DictConfig({
                '_target_': 'src.losses.combined_loss.CombinedLoss',
                'loss_weights': {'ce': 0.5, 'dice': 0.5},
                'ignore_index': 255,
                'num_classes': 7
            })
        }

        for loss_name, loss_cfg in loss_configs.items():
            try:
                # 通过Hydra实例化损失函数
                loss_fn = hydra.utils.instantiate(loss_cfg)

                assert loss_fn is not None, f"损失函数实例化失败: {loss_name}"
                assert isinstance(loss_fn, torch.nn.Module), f"不是有效的PyTorch模块: {loss_name}"

                # 测试前向传播
                logits = torch.randn(2, 7, 64, 64)
                targets = torch.randint(0, 7, (2, 64, 64))

                loss = loss_fn(logits, targets)

                assert isinstance(loss, torch.Tensor), f"未返回张量: {loss_name}"
                assert not torch.isnan(loss), f"损失为NaN: {loss_name}"
                assert not torch.isinf(loss), f"损失为无穷大: {loss_name}"

                print(f"✅ {loss_name}: 损失值 = {loss.item():.4f}")

            except Exception as e:
                pytest.fail(f"配置 {loss_name} 测试失败: {e}")
    
    def test_model_with_loss_integration_hydra_way(self):
        """使用Hydra标准方式测试模型与损失函数集成"""
        
        with initialize(config_path="../configs", version_base=None):
            # 测试不同模型和损失函数的组合
            test_combinations = [
                ["model=deeplabv3plus", "loss=cross_entropy", "data=suide"],
                ["model=unet", "loss=dice", "data=suide"],
                ["model=unetpp", "loss=focal", "data=suide"],
                ["model=swin_unet", "loss=combined", "data=suide"]
            ]
            
            for model_config, loss_config, data_config in test_combinations:
                try:
                    # 使用Hydra compose API组合配置
                    cfg = compose(
                        config_name="config",
                        overrides=[model_config, loss_config, data_config, "trainer.fast_dev_run=true"]
                    )
                    
                    # 通过Hydra实例化模型（包含损失函数）
                    with warnings.catch_warnings():
                        warnings.filterwarnings("ignore", message=".*self.trainer.*")
                        
                        model = hydra.utils.instantiate(
                            cfg.model,
                            optimizer_cfg=cfg.optimizer,
                            scheduler_cfg=cfg.scheduler,
                            loss_cfg=cfg.loss,
                            _recursive_=False
                        )
                    
                    assert model is not None, f"模型实例化失败: {model_config} + {loss_config}"
                    
                    # 测试训练步骤
                    test_batch = {
                        'image': torch.randn(2, 3, 128, 128),
                        'mask': torch.randint(0, 7, (2, 128, 128))
                    }
                    
                    model.train()
                    train_loss = model.training_step(test_batch, 0)
                    
                    assert isinstance(train_loss, torch.Tensor), "训练步骤未返回损失张量"
                    assert not torch.isnan(train_loss), "训练损失为NaN"
                    
                    print(f"✅ {model_config} + {loss_config}: 训练损失 = {train_loss.item():.4f}")
                    
                except Exception as e:
                    pytest.fail(f"组合 {model_config} + {loss_config} 测试失败: {e}")
    
    def test_hydra_config_override(self):
        """测试Hydra配置覆盖功能"""
        
        with initialize(config_path="../configs", version_base=None):
            # 测试配置覆盖
            overrides = [
                "loss=cross_entropy",
                "loss.ignore_index=100",  # 覆盖默认的ignore_index
                "data=suide"  # 指定正确的数据配置
            ]
            
            cfg = compose(config_name="config", overrides=overrides)
            
            # 验证配置覆盖生效
            assert cfg.loss.ignore_index == 100, "配置覆盖未生效"
            
            # 实例化并测试
            loss_fn = hydra.utils.instantiate(cfg.loss)
            
            # 创建包含ignore_index的测试数据
            logits = torch.randn(2, 7, 32, 32)
            targets = torch.randint(0, 7, (2, 32, 32))
            targets[0, 0, 0] = 100  # 设置一个ignore_index值
            
            loss = loss_fn(logits, targets)
            
            assert isinstance(loss, torch.Tensor), "损失计算失败"
            assert not torch.isnan(loss), "损失为NaN"
            
            print(f"✅ 配置覆盖测试通过: ignore_index={cfg.loss.ignore_index}, 损失={loss.item():.4f}")
    
    def test_hydra_config_composition(self):
        """测试Hydra配置组合功能"""
        
        with initialize(config_path="../configs", version_base=None):
            # 测试复杂的配置组合
            cfg = compose(
                config_name="config",
                overrides=[
                    "model=deeplabv3plus",
                    "loss=combined",
                    "loss.loss_weights.ce=0.7",
                    "loss.loss_weights.dice=0.3",
                    "optimizer=adamw",
                    "optimizer.lr=0.0001",
                    "scheduler=cosine",
                    "data=suide",
                    "trainer.max_epochs=1"
                ]
            )
            
            # 验证配置组合正确
            assert cfg.loss._target_ == "src.losses.combined_loss.CombinedLoss"
            assert cfg.loss.loss_weights.ce == 0.7
            assert cfg.loss.loss_weights.dice == 0.3
            assert cfg.optimizer.lr == 0.0001
            
            # 实例化所有组件
            loss_fn = hydra.utils.instantiate(cfg.loss)
            optimizer_cfg = cfg.optimizer
            scheduler_cfg = cfg.scheduler
            
            assert loss_fn is not None, "损失函数实例化失败"
            assert optimizer_cfg is not None, "优化器配置获取失败"
            assert scheduler_cfg is not None, "调度器配置获取失败"
            
            print("✅ 复杂配置组合测试通过")
    
    def test_hydra_multirun_simulation(self):
        """模拟Hydra multirun功能测试"""
        
        with initialize(config_path="../configs", version_base=None):
            # 模拟多次运行不同配置
            multirun_configs = [
                ["loss=cross_entropy", "optimizer.lr=0.001"],
                ["loss=dice", "optimizer.lr=0.01"],
                ["loss=focal", "optimizer.lr=0.0001"],
                ["loss=combined", "optimizer.lr=0.005"]
            ]
            
            results = []
            
            for overrides in multirun_configs:
                cfg = compose(config_name="config", overrides=overrides)
                
                # 实例化损失函数
                loss_fn = hydra.utils.instantiate(cfg.loss)
                
                # 测试计算
                logits = torch.randn(2, 7, 64, 64)
                targets = torch.randint(0, 7, (2, 64, 64))
                
                loss = loss_fn(logits, targets)
                
                results.append({
                    'config': overrides,
                    'loss_value': loss.item(),
                    'lr': cfg.optimizer.lr
                })
                
                print(f"  配置 {overrides}: 损失={loss.item():.4f}, 学习率={cfg.optimizer.lr}")
            
            # 验证所有配置都成功
            assert len(results) == len(multirun_configs), "部分配置失败"
            for result in results:
                assert not torch.isnan(torch.tensor(result['loss_value'])), "存在NaN损失"
            
            print("✅ Multirun模拟测试通过")


class TestHydraConfigValidation:
    """Hydra配置验证测试"""
    
    def setup_method(self):
        GlobalHydra.instance().clear()
    
    def teardown_method(self):
        GlobalHydra.instance().clear()
    
    def test_all_loss_configs_valid(self):
        """验证所有损失函数配置文件的有效性"""
        
        with initialize(config_path="../configs", version_base=None):
            # 获取所有损失函数配置
            loss_config_files = [
                "cross_entropy",
                "dice", 
                "focal",
                "combined",
                "ce_dice"
            ]
            
            for loss_name in loss_config_files:
                try:
                    cfg = compose(config_name="config", overrides=[f"loss={loss_name}"])
                    
                    # 验证配置结构
                    assert hasattr(cfg, 'loss'), f"配置缺少loss部分: {loss_name}"
                    assert hasattr(cfg.loss, '_target_'), f"配置缺少_target_: {loss_name}"
                    
                    # 验证_target_指向有效的类
                    target_parts = cfg.loss._target_.split('.')
                    assert len(target_parts) >= 2, f"无效的_target_格式: {loss_name}"
                    
                    # 尝试实例化
                    loss_fn = hydra.utils.instantiate(cfg.loss)
                    assert loss_fn is not None, f"实例化失败: {loss_name}"
                    
                    print(f"✅ 配置文件 {loss_name}.yaml 验证通过")
                    
                except Exception as e:
                    pytest.fail(f"配置文件 {loss_name}.yaml 验证失败: {e}")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "--tb=short", "-s"])
